import { Fragment, type ReactNode } from "react";
import { useHelperStore } from "~/hooks/store";
import { cn } from "~/lib/utils";

export const View = ({
  children,
  hotkeys,
  description,
  className,
}: {
  children: ReactNode;
  hotkeys?: string[];
  description: string;
  className?: string;
}) => {
  const helperActive = useHelperStore((state) => state.helperActive);

  return (
    <div className="relative">
      {children}
      {helperActive && hotkeys && hotkeys.length > 0 && (
        <div
          className={cn(
            "absolute left-1/2 top-1/2 z-[999] flex w-fit select-none flex-col items-center gap-[5px] text-label",
            className,
          )}
        >
          {hotkeys && (
            <div className="flex gap-0.5">
              {hotkeys.map((hotkey, index) => (
                <Fragment key={hotkey}>
                  <div
                    className={cn(
                      "box-border flex h-6 items-center justify-center border border-neonGreen bg-black/60 text-white",
                      hotkey.length > 2 ? "px-3 py-1.5" : "w-6",
                    )}
                  >
                    {hotkey}
                  </div>
                  {index < hotkeys.length - 1 && (
                    <div className="my-auto">+</div>
                  )}
                </Fragment>
              ))}
            </div>
          )}

          <div className="w-fit rounded-[1px] bg-black/60 px-2 py-0.5 text-white">
            {description}
          </div>
        </div>
      )}
    </div>
  );
};
