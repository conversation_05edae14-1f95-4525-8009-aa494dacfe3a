import { FFmpeg } from "@ffmpeg/ffmpeg";
import { fetchFile, toBlobURL } from "@ffmpeg/util";

// const baseURL = "https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd";
const baseURL = "https://unpkg.com/@ffmpeg/core-mt@0.12.6/dist/umd";
const inputFilename = "input.ts";

export const ceilNumber = (value: number, digit = 4) => {
  let multiplier = 1;
  for (let i = 0; i < digit; i++) {
    multiplier *= 10;
  }
  return Math.ceil(value * multiplier) / multiplier;
};

const getFramesInfo = async ({
  ffmpeg,
  setProgress,
}: {
  ffmpeg: FFmpeg;
  setProgress?: (value: number) => void;
}) => {
  const logMessages: string[] = [];

  ffmpeg.on("log", ({ message }) => {
    logMessages.push(message);
  });

  ffmpeg.on("progress", ({ progress }) => {
    if (setProgress) setProgress(progress);
  });

  // await ffmpeg.exec([
  //   "-fflags",
  //   "+genpts",
  //   "-copyts",
  //   "-hwaccel",
  //   "none", // Disable hardware acceleration
  //   "-i",
  //   inputFilename,
  //   "-vf",
  //   "showinfo",
  //   "-an", // Disable audio processing
  //   "-vsync",
  //   "cfr", // Constant frame rate
  //   "-f",
  //   "null",
  //   "-",
  // ]);
  await ffmpeg.exec(["-i", inputFilename]);

  let fps = 0;
  let timeBase:
    | {
        numerator: number;
        denominator: number;
      }
    | undefined;
  const framesInfo: { frameNumber: number; timestamp: number; pts: number }[] =
    [];
  const fpsRegex = /(\d+(\.\d+)?)\s+tbr/;
  const timeBaseRegex = /time_base:\s*(\d+)\/(\d+)/;

  let width: number | undefined;
  let height: number | undefined;
  const resolutionRegex = /, (\d{2,5})x(\d{2,5})[,\s]/;

  logMessages.forEach((message) => {
    //get fps if fps is still 0
    if (!fps) {
      const matchFps = fpsRegex.exec(message);
      if (matchFps) {
        fps = parseFloat(matchFps[1]!);
      }
    }

    //get time base if time base is still 0
    if (!timeBase) {
      const matchTimeBase = timeBaseRegex.exec(message);
      if (matchTimeBase) {
        const numerator = parseInt(matchTimeBase[1]!, 10);
        const denominator = parseInt(matchTimeBase[2]!, 10);
        timeBase = { numerator, denominator };
      }
    }

    // Try to extract resolution
    if (!width || !height) {
      const resMatch = resolutionRegex.exec(message);
      if (resMatch) {
        width = parseInt(resMatch[1]!, 10);
        height = parseInt(resMatch[2]!, 10);
      }
    }

    //get frames info
    const ptsRegex = /pts: ([\d.]+)/g;
    const ptsMatch = ptsRegex.exec(message);
    if (ptsMatch) {
      if (!timeBase) throw new Error("Time base not found");
      const pts = parseInt(ptsMatch[1]!, 10);
      const ptsTime = (pts * timeBase.numerator) / timeBase.denominator;
      const frameNumber = Math.round(ptsTime * fps);
      framesInfo.push({ frameNumber, timestamp: ceilNumber(ptsTime), pts });
    }
  });

  if (!fps) {
    throw new Error("Failed to parse FPS from video file");
  }

  return { framesInfo, fps, timeBase, width, height };
};

export const extractFrames = async ({
  videoUrl,
  setProgress,
}: {
  videoUrl: string;
  setProgress?: (value: number) => void;
}) => {
  const ffmpeg = new FFmpeg();

  if (!ffmpeg.loaded) {
    await ffmpeg.load({
      coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, "text/javascript"),
      wasmURL: await toBlobURL(
        `${baseURL}/ffmpeg-core.wasm`,
        "application/wasm",
      ),
      workerURL: await toBlobURL(
        `${baseURL}/ffmpeg-core.worker.js`,
        "text/javascript",
      ),
    });
  }

  // ffmpeg.on("log", ({ message }) => {
  //   console.log("FFmpeg log:", message);
  // });

  const fileData = await fetchFile(videoUrl);
  await ffmpeg.writeFile(inputFilename, fileData);

  const { framesInfo, fps, timeBase, width, height } = await getFramesInfo({
    ffmpeg,
    setProgress,
  });

  return { framesInfo, fps, videoUrl, timeBase, width, height };
};

export type TSSegment = Awaited<ReturnType<typeof extractFrames>>;
