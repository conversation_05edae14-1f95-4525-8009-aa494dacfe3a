import { useEffect } from "react";
import { DeleteTagAthlete } from "~/app/_components/EditVideo/DeleteTagAthlete";
import { EditTagAthlete } from "~/app/_components/EditVideo/EditTagAthlete";
import { Card } from "~/components/Card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { useStore } from "~/hooks/store";

export const AthleteSelect = ({
  athleteOptions,
  disableDelete,
}: {
  athleteOptions: { athleteId: string; name: string }[];
  disableDelete?: boolean;
}) => {
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const setSelectedAthlete = useStore((state) => state.setSelectedAthlete);

  const uniqueAthleteOptions: { athleteId: string; name: string }[] = [];
  for (const athlete of athleteOptions) {
    if (!uniqueAthleteOptions.find((a) => a.athleteId === athlete.athleteId)) {
      uniqueAthleteOptions.push(athlete);
    }
  }
  const sortedAthletes = uniqueAthleteOptions.sort((a, b) =>
    a.name.localeCompare(b.name),
  );

  useEffect(() => {
    if (!selectedAthlete) {
      setSelectedAthlete(sortedAthletes[0]?.athleteId);
    }
  }, [sortedAthletes, setSelectedAthlete, selectedAthlete]);

  return (
    <Card className="w-full items-start gap-2.5">
      <div className="flex w-full flex-col items-start gap-[5px]">
        <p className="text-smallLabel text-black/60">Athlete</p>
        <div className="flex w-full items-center justify-center gap-[5px]">
          <Select
            value={selectedAthlete}
            disabled={false}
            defaultValue={sortedAthletes[0]?.athleteId}
            onValueChange={(value) => {
              setSelectedAthlete(value);
              // Use a slightly longer timeout to ensure the component has fully updated
              setTimeout(() => {
                if (document.activeElement instanceof HTMLElement) {
                  document.activeElement.blur();
                }
              }, 100);
            }}
          >
            <SelectTrigger
              id="athlete-select"
              className="flex flex-1 gap-2.5 rounded-full py-[5px]"
            >
              <SelectValue placeholder="Select athlete" />
            </SelectTrigger>
            <SelectContent>
              {sortedAthletes.map((athlete) => (
                <SelectItem key={athlete.athleteId} value={athlete.athleteId}>
                  {athlete.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <EditTagAthlete
            athleteOptions={sortedAthletes.filter(
              (a) => a.athleteId !== selectedAthlete,
            )}
            originalAthleteId={selectedAthlete}
          />

          {!disableDelete && (
            <DeleteTagAthlete
              athleteId={selectedAthlete}
              onSuccess={() => setSelectedAthlete("")}
            />
          )}
        </div>
      </div>
    </Card>
  );
};
