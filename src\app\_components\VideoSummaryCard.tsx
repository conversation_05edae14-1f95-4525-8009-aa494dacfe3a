import { usePathname, useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { Card } from "~/components/Card";
import { EditSwimmingRaceContainer } from "~/components/swimming/EditSwimmingRaceContainer";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { useStore } from "~/hooks/store";
import { Sport } from "~/lib/enums/enums";
import { getSport } from "~/lib/utils";
import { api } from "~/trpc/react";
import { EditVideoButton } from "./EditVideoButton";

export const VideoSummaryCard = () => {
  const pathname = usePathname();
  const router = useRouter();

  const isValidation = pathname.startsWith("/validation");

  const videoSummary = useStore((state) => state.videoSummary);

  const title = videoSummary?.title;
  const status = videoSummary?.status;
  const videoId = videoSummary?.id;
  const sport = getSport(videoSummary);

  const { mutate: completeTag, isPending } = api.videos.completeTag.useMutation(
    {
      onSuccess: () => {
        router.refresh();
        toast.success("Review finished");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    },
  );

  const onFinishReview = () => {
    if (!videoId) return;
    completeTag({ id: videoId });
  };

  return (
    <Card className="flex w-full flex-col items-start gap-2.5 rounded-lg border bg-white p-2.5">
      <div className="flex w-full items-start justify-between gap-[5px]">
        <div className="flex flex-1 items-start gap-[5px]">
          <p className="text-smallLabel text-black/60">Video:</p>
          <p className="line-clamp-2 whitespace-normal break-all text-xs leading-[12px] text-black">
            {title}
          </p>
        </div>
        <EditVideoButton videoId={videoId} title="Edit Video" />
      </div>
      <div className="flex items-start gap-[5px]">
        <p className="text-smallLabel text-black/60">Status:</p>
        <Badge variant="tag" className="leading-[10px] tracking-tight">
          {status?.replaceAll("_", " ")}
        </Badge>
        {sport === Sport.swimming && <EditSwimmingRaceContainer />}
      </div>
      {isValidation && (
        <Button
          className="w-full px-5 py-2.5 leading-[17.60px] tracking-wide"
          onClick={onFinishReview}
          loading={isPending}
        >
          Complete Review
        </Button>
      )}
    </Card>
  );
};
