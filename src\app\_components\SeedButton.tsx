import { Button } from "~/components/ui/button";
import { api } from "~/trpc/server";

export const SeedButton = ({ hidden = false }: { hidden?: boolean }) => {
  if (hidden) return null;
  return (
    <form>
      <Button
        formAction={async () => {
          "use server";
          await api.seed.swimming();
          // await api.seed.longvideo();
        }}
      >
        Seed
      </Button>
    </form>
  );
};
