"use client";

import { PopoverClose } from "@radix-ui/react-popover";
import { ArrowLeftRight, X } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useState } from "react";
import toast from "react-hot-toast";
import { But<PERSON> } from "~/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { api } from "~/trpc/react";
import { useStore } from "~/hooks/store";
import { Sport } from "~/lib/enums/enums";
import { getSport } from "~/lib/utils";

export const EditTagAthlete = ({
  athleteOptions,
  originalAthleteId,
}: {
  athleteOptions: { athleteId: string; name: string }[];
  originalAthleteId?: string;
}) => {
  const utils = api.useUtils();
  const router = useRouter();
  const params = useParams<{ id: string }>();
  const videoId = params.id;

  const videoSummary = useStore((state) => state.videoSummary);
  const shotputThrow = useStore((state) => state.shotputThrow);

  const [open, setOpen] = useState(false);
  const [newSelectedAthlete, setNewSelectedAthlete] = useState<
    string | undefined
  >(athleteOptions[0]?.athleteId);

  const { mutate: editSwimmingTagAthlete } =
    api.swimming.editTagAthlete.useMutation({
      onSuccess: () => {
        toast.success("Athlete updated");
        router.refresh();
        void utils.swimming.getVideoTags.invalidate({
          id: videoId,
        });
        setOpen(false);
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });
  const { mutate: editShotputTagAthlete } =
    api.shotput.editTagAthlete.useMutation({
      onSuccess: () => {
        toast.success("Athlete updated");
        router.refresh();
        void utils.shotput.getTags.invalidate({
          id: videoId,
        });
        setOpen(false);
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });

  const handleAthleteSwap = (newAthleteId: string) => {
    if (!originalAthleteId) return;

    const sport = getSport(videoSummary);
    switch (sport) {
      case Sport.swimming:
        editSwimmingTagAthlete({
          videoId,
          originalAthleteId,
          newAthleteId,
        });
        break;
      case "shotput":
        const shotputThrowNumber = shotputThrow?.number;
        if (!shotputThrowNumber) return;
        editShotputTagAthlete({
          videoId,
          originalAthleteId,
          newAthleteId,
          number: shotputThrowNumber,
        });
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild disabled={!originalAthleteId}>
        <button className="icon-popover">
          <ArrowLeftRight className="h-[14px] w-[14px]" />
        </button>
      </PopoverTrigger>
      <PopoverContent className="p-0" align="end">
        <div className="flex w-full flex-col items-start">
          <div className="flex w-full items-center justify-between gap-2.5 border-b px-5 py-2.5">
            <ArrowLeftRight className="h-[14px] w-[14px]" />
            <p className="flex-1 text-label">Swap athletes</p>
            <PopoverClose asChild>
              <X
                className="h-[14px] w-[14px] cursor-pointer"
                strokeWidth={1.5}
              />
            </PopoverClose>
          </div>
          <div className="flex w-full flex-col items-start justify-center gap-5 p-5">
            <p className="text-label">
              Move all current tags to a different athlete.
            </p>
            <div className="flex w-full flex-col items-start gap-[5px]">
              <p className="text-smallLabel text-black/60">
                Select new athlete:
              </p>
              <Select
                value={newSelectedAthlete}
                onValueChange={setNewSelectedAthlete}
              >
                <SelectTrigger className="rounded-full py-[7px] pl-2.5 pr-[34px]">
                  <SelectValue placeholder="Select athlete" />
                </SelectTrigger>
                <SelectContent>
                  {athleteOptions.map((athlete) => (
                    <SelectItem
                      key={athlete.athleteId}
                      value={athlete.athleteId}
                    >
                      {athlete.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button
              variant="black"
              className="w-full"
              disabled={!newSelectedAthlete}
              onClick={() => {
                if (newSelectedAthlete) {
                  handleAthleteSwap(newSelectedAthlete);
                }
              }}
            >
              Confirm athlete swap
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
