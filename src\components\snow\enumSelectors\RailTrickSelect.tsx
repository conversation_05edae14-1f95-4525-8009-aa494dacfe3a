"use client";

import { api } from "~/trpc/react";
import toast from "react-hot-toast";
import { EditableSelector } from "~/components/Selector";

export const RailTrickSelect = ({
  isSnowboard,
  value,
  onSelect,
}: {
  isSnowboard: boolean;
  value: string | null;
  onSelect: (value: string) => void;
}) => {
  const utils = api.useUtils();
  const { data: options, refetch } = api.snowOptions.getRailTricks.useQuery({
    isSnowboard,
  });

  const { mutate: add, isPending: isPendingAdd } =
    api.snowOptions.addRailTrick.useMutation({
      onSuccess: () => {
        void refetch();
      },
    });

  const { mutate: remove } = api.snowOptions.deleteRailTrick.useMutation({
    onError: () => {
      toast.error("Failed to remove jump type");
      void refetch();
    },
  });

  const onAddOption = (value: string) => {
    add({ name: value, isSnowboard });
  };

  const onRemoveOption = (value: string) => {
    remove({ id: parseInt(value) });
    utils.snowOptions.getRailTricks.setData({ isSnowboard }, (prev) => {
      return prev?.filter((x) => x.value !== value);
    });
  };

  return (
    <EditableSelector
      placeholder="<Select/Enter new rail feature>"
      options={options ?? []}
      onAdd={onAddOption}
      onRemove={onRemoveOption}
      value={value}
      onSelect={onSelect}
      isPendingAdd={isPendingAdd}
    />
  );
};
