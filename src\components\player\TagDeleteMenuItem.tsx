"use client";

import { useHighJumpTags } from "~/hooks/useHighJumpTags";
import { ContextMenuItem } from "../ui/context-menu";
import { useShotputTags } from "~/hooks/useShotputTags";
import { useSwimmingTags } from "~/hooks/useSwimmingTags";
import { useSprintingTags } from "~/hooks/useSprintingTags";

export const HighJumpTagDeleteMenuItem = ({
  id,
  selectedTags,
}: {
  id: string;
  selectedTags: string[];
}) => {
  const { onDeleteStrides, onDeleteFrame } = useHighJumpTags();

  const onDelete = () => {
    const tagsToDelete = [...selectedTags];
    if (!selectedTags.includes(id)) {
      tagsToDelete.push(id);
    }
    const strideIds = tagsToDelete.filter(
      (x) => !x.endsWith("-start") && !x.endsWith("-end"),
    );
    if (strideIds.length > 0) {
      onDeleteStrides(strideIds);
    }

    //remove start/end frames of jump
    const framesToDelete: { startFrame?: number; endFrame?: number } = {};
    tagsToDelete.forEach((x) => {
      if (x.endsWith("-start")) {
        framesToDelete.startFrame = 0;
      } else if (x.endsWith("-end")) {
        framesToDelete.endFrame = 0;
      }
    });
    if (
      framesToDelete.startFrame !== undefined ||
      framesToDelete.endFrame !== undefined
    ) {
      const jumpId = id.split("-")[0]!;
      onDeleteFrame(jumpId, framesToDelete.startFrame, framesToDelete.endFrame);
    }
  };

  return <ContextMenuItem onClick={onDelete}>Delete</ContextMenuItem>;
};

export const ShotputTagDeleteMenuItem = ({
  id,
  selectedTags,
}: {
  id: string;
  selectedTags: string[];
}) => {
  const { onDeleteTags } = useShotputTags();

  return (
    <ContextMenuItem
      onClick={() => {
        const tagsToDelete = [...selectedTags];
        if (!selectedTags.includes(id)) {
          tagsToDelete.push(id);
        }
        onDeleteTags(tagsToDelete);
      }}
    >
      Delete
    </ContextMenuItem>
  );
};

export const SwimmingTagDeleteMenuItem = ({
  id,
  selectedTags,
}: {
  id: string;
  selectedTags: string[];
}) => {
  const { onDeleteTags } = useSwimmingTags();

  return (
    <ContextMenuItem
      onClick={() => {
        const tagsToDelete = [...selectedTags];
        if (!selectedTags.includes(id)) {
          tagsToDelete.push(id);
        }
        onDeleteTags(tagsToDelete);
      }}
    >
      Delete
    </ContextMenuItem>
  );
};

export const SprintingTagDeleteMenuItem = ({
  id,
  selectedTags,
  type,
}: {
  id: string;
  selectedTags: string[];
  type: "race" | "stride";
}) => {
  const { onDeleteTags, onDeleteStrides } = useSprintingTags();

  const onDelete = () => {
    const tagsToDelete = [...selectedTags];
    if (!selectedTags.includes(id)) {
      tagsToDelete.push(id);
    }

    if (type === "stride") {
      onDeleteStrides(tagsToDelete);
    } else {
      onDeleteTags(tagsToDelete);
    }
  };

  return (
    <ContextMenuItem onClick={onDelete}>
      {type === "stride" ? "Delete Stride" : "Delete Tag"}
    </ContextMenuItem>
  );
};
