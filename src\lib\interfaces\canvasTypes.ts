import type { Dispatch, MouseEvent, RefObject, SetStateAction } from "react";
import { type TSSegment } from "../ffmpeg";
import type { TaglistTag } from "../interface";
import {
  type ColouredLine,
  type CurrentPose,
  type DrawingMode,
} from "./drawingTypes";

// Canvas manager props
export interface CanvasManagerProps {
  canvasRef: RefObject<HTMLCanvasElement>;
  currentFrame: number;
  filteredTags: TaglistTag[];
  selectedAthlete: string;
  showPose: boolean;
  showAngles: boolean;
  currentFramePose: CurrentPose;
  firstSegment?: TSSegment;
  lines: ColouredLine[];
  setLines: Dispatch<SetStateAction<ColouredLine[]>>;
  drawingMode: DrawingMode;
  selectedColour: string;
  isCtrlPressed: boolean;
  strokeWidth: number;
  poseCircleThickness: number;
  poseLineThickness: number;
}

// Canvas manager return type
export interface CanvasManagerHandlers {
  handleMouseDown: (e: MouseEvent<HTMLCanvasElement>) => void;
  handleMouseMove: (e: MouseEvent<HTMLCanvasElement>) => void;
  handleMouseUp: () => void;
}
