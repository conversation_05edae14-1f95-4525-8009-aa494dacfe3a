"use client";

import { Parser } from "m3u8-parser";
import { useParams } from "next/navigation";
import { useEffect } from "react";
import { VideoSummaryCard } from "~/app/_components/VideoSummaryCard";
import { VideoPlayer } from "~/components/player/Player";
import { useStore } from "~/hooks/store";
import { Sport } from "~/lib/enums/enums";
import {
  getDefaultSelectedAthlete,
  getSport,
  getTimelineTagTypes,
} from "~/lib/utils";
import type { GetVideoInfoOutput } from "~/server/api/routers/video";
import { api } from "~/trpc/react";
import { ShotputForm } from "./ShotputForm";
import { SwimmingForm } from "./SwimmingForm";
import type { GetPoseInfoOutput } from "~/server/api/routers/pose";

export const ValidationTool = ({
  videoSummary,
  poseData,
}: {
  videoSummary: GetVideoInfoOutput;
  poseData: GetPoseInfoOutput;
}) => {
  const params = useParams<{ id: string }>();
  const sport = getSport(videoSummary);
  const videoId = params.id;

  const [{ m3u8Text }] = api.videos.getVideoSourceStream.useSuspenseQuery({
    id: videoId,
  });
  const parser = new Parser();
  parser.push(m3u8Text);
  parser.end();

  const m3u8Segments = parser.manifest.segments;

  const defaultSelectedAthlete = getDefaultSelectedAthlete(videoSummary);
  const timelineTagTypes = getTimelineTagTypes(videoSummary);

  const setSelectedAthlete = useStore((state) => state.setSelectedAthlete);
  const setVideoSummary = useStore((state) => state.setVideoSummary);
  const resetStore = useStore((state) => state.resetStore);

  //initialize on leaving page
  useEffect(() => {
    return () => {
      resetStore();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!defaultSelectedAthlete) return;
    setSelectedAthlete(defaultSelectedAthlete);
  }, [defaultSelectedAthlete, setSelectedAthlete]);

  useEffect(() => {
    setVideoSummary(videoSummary);
  }, [videoSummary, setVideoSummary]);

  return (
    <div className="grid h-full grid-cols-12 gap-4">
      <VideoPlayer
        m3u8Segments={m3u8Segments}
        m3u8Text={m3u8Text}
        tagTypes={timelineTagTypes}
        poseData={poseData}
      />
      <div className="col-span-3 flex flex-1 flex-col gap-2.5">
        <VideoSummaryCard />

        {sport === Sport.swimming && <SwimmingForm />}
        {sport === "shotput" && <ShotputForm />}
      </div>
    </div>
  );
};
