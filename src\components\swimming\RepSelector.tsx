import { Card } from "~/components/Card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { useStore } from "~/hooks/store";
import { api } from "~/trpc/react";
import { useParams } from "next/navigation";
import { EditTagAthlete } from "~/app/_components/EditVideo/EditTagAthlete";
import { DeleteTagAthlete } from "~/app/_components/EditVideo/DeleteTagAthlete";
import { useEffect } from "react";

export const RepSelector = () => {
  const params = useParams();
  const videoId = params.id as string;

  const { data: tags } = api.swimming.getVideoTags.useQuery({
    id: videoId,
  });

  const { data: reps } = api.swimmingPta.getReps.useQuery({
    videoId,
  });

  const tagAthleteIds = [...new Set(tags?.tags.map((tag) => tag.athleteId))];

  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const setSelectedAthlete = useStore((state) => state.setSelectedAthlete);
  const setSelectedSwimmingRep = useStore(
    (state) => state.setSelectedSwimmingRep,
  );

  const onValueChange = (value: string) => {
    setSelectedAthlete(value);
    const repId = reps?.find((rep) => rep.athlete_id === value);
    setSelectedSwimmingRep(repId);
  };

  const athleteOptions: { athleteId: string; name: string }[] = [];

  for (const rep of reps ?? []) {
    if (!athleteOptions.find((a) => a.athleteId === rep.athlete_id)) {
      athleteOptions.push({
        athleteId: rep.athlete_id,
        name: rep.athlete_name,
      });
    }
  }

  tagAthleteIds.forEach((athleteId) => {
    if (!athleteOptions.find((a) => a.athleteId === athleteId)) {
      athleteOptions.push({
        athleteId,
        name: athleteId,
      });
    }
  });

  useEffect(() => {
    if (
      !selectedAthlete &&
      athleteOptions.length > 0 &&
      selectedAthlete !== ""
    ) {
      setSelectedAthlete(athleteOptions[0]?.athleteId ?? "");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [athleteOptions]);

  const sortedAthletes =
    athleteOptions?.sort((a, b) => a.name.localeCompare(b.name)) ?? [];

  return (
    <Card className="w-full items-start gap-2.5">
      <div className="flex w-full flex-col items-start gap-[5px]">
        <p className="text-smallLabel text-black/60">Rep Athlete</p>
        <div className="flex w-full items-center gap-[5px]">
          <Select
            value={selectedAthlete}
            disabled={false}
            defaultValue={sortedAthletes[0]?.athleteId}
            onValueChange={(value) => {
              onValueChange(value);
              setTimeout(() => {
                if (document.activeElement instanceof HTMLElement) {
                  document.activeElement.blur();
                }
              }, 100);
            }}
          >
            <SelectTrigger className="flex max-w-40 flex-1 gap-2.5 rounded-full py-[5px]">
              <SelectValue placeholder="Select athlete" />
            </SelectTrigger>
            <SelectContent>
              {sortedAthletes.map((athlete) => (
                <SelectItem key={athlete.athleteId} value={athlete.athleteId}>
                  {athlete.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <EditTagAthlete
            athleteOptions={sortedAthletes.filter(
              (a) => a.athleteId !== selectedAthlete,
            )}
            originalAthleteId={selectedAthlete}
          />

          <DeleteTagAthlete
            athleteId={selectedAthlete}
            onSuccess={() => setSelectedAthlete("")}
          />
        </div>
      </div>
    </Card>
  );
};
