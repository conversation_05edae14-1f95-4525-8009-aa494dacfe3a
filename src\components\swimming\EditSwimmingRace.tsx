"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { type ReactNode, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import type { z } from "zod";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { useStore } from "~/hooks/store";
import { FormItemType, Gender } from "~/lib/enums/enums";
import {
  SwimCourseType,
  swimmingStrokeCategories,
  SwimmingStrokeCategory,
} from "~/lib/enums/swimming";
import type { SwimmingRace } from "~/lib/interfaces/swimming";
import { swimmingRaceFormSchema } from "~/lib/schemas";
import { api } from "~/trpc/react";
import { FormItem, type FormItemProps } from "~/components/FormItem";
import { Form } from "~/components/ui/form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import type { Option } from "~/lib/interface";
import { Checkbox } from "../ui/check-box";

type FormProps = z.infer<typeof swimmingRaceFormSchema>;

interface RaceFormItemProps extends FormItemProps {
  name: keyof FormProps;
}

const initialFormData: FormProps = {
  course_type: SwimCourseType.long,
  date: new Date(),
  round: "",
  stroke_category: SwimmingStrokeCategory.FREESTYLE,
  race_distance: "",
  is_relay: false,
  gender: Gender.Men,
  age_category: "Senior",
  classification: null,
};

const getClassificationOptions = (strokeCategory: SwimmingStrokeCategory) => {
  let prefix = "S";

  switch (strokeCategory) {
    case SwimmingStrokeCategory.BREASTSTROKE:
      prefix = "SB";

      break;
    case SwimmingStrokeCategory.INDIVIDUAL_MEDLEY:
      prefix = "SM";
      break;
    default:
      break;
  }
  const options: Option[] = [
    {
      label: "None",
      value: "none",
    },
    {
      label: "Multi class",
      value: "MULTI_CLASS",
    },
    ...Array.from({ length: 14 }, (_, i) => ({
      label: `${prefix}${i + 1}`,
      value: `${prefix}${i + 1}`,
    })),
  ];
  return options;
};

export const EditSwimmingRace = ({
  race,
  Trigger,
}: {
  race?: SwimmingRace;
  Trigger: ReactNode;
}) => {
  const utils = api.useUtils();

  const [open, setOpen] = useState(false);

  const videoSummary = useStore((state) => state.videoSummary);
  const selectedSwimmingRace = useStore((state) => state.selectedSwimmingRace);
  const setSelectedSwimmingRace = useStore(
    (state) => state.setSelectedSwimmingRace,
  );

  const form = useForm<FormProps>({
    resolver: zodResolver(swimmingRaceFormSchema),
    defaultValues: { ...initialFormData },
  });

  useEffect(() => {
    if (!race) {
      form.reset();
      return;
    }
    form.setValue("course_type", race.course_type);
    form.setValue("date", new Date(race.date));
    form.setValue("round", race.round);
    form.setValue("stroke_category", race.stroke_category);
    form.setValue("race_distance", race.race_distance.toString());
    form.setValue("gender", race.gender);
    form.setValue("age_category", race.age_category);
    form.setValue("classification", race.classification);
    form.setValue("is_relay", race.is_relay);

    return () => {
      form.reset();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [race]);

  const { mutate: upsertRace, isPending } =
    api.swimmingPta.upsertRace.useMutation({
      onSuccess: async (data) => {
        toast.success("Race updated successfully");
        await utils.swimmingPta.getRaces.invalidate();
        setOpen(false);
        setSelectedSwimmingRace(data);
      },
      onError: () => {
        toast.error("Failed to update race");
      },
    });

  const strokeCategory = form.watch("stroke_category");
  const classificationOptions = getClassificationOptions(strokeCategory);

  const formItems: RaceFormItemProps[] = [
    {
      name: "course_type",
      title: "Course Type",
      type: FormItemType.select,
      className: "grid",
      options: Object.values(SwimCourseType).map((courseType) => ({
        label: courseType,
        value: courseType,
      })),
    },
    {
      name: "date",
      title: "Date",
      className: "grid",
      type: FormItemType.date,
    },
    {
      name: "round",
      title: "Round",
      className: "grid",
      type: FormItemType.text,
    },
    {
      name: "stroke_category",
      title: "Stroke Category",
      type: FormItemType.select,
      className: "grid",
      options: swimmingStrokeCategories.map((category) => ({
        label: category,
        value: category,
      })),
    },
    {
      name: "classification",
      title: "Classification",
      className: "grid",
      type: FormItemType.select,
      options: classificationOptions,
    },
    {
      name: "race_distance",
      title: "Race Distance",
      className: "grid",
      type: FormItemType.number,
    },
    {
      name: "gender",
      title: "Gender",
      className: "grid",
      type: FormItemType.select,
      options: Object.values(Gender).map((gender) => ({
        label: gender,
        value: gender,
      })),
    },
    {
      name: "age_category",
      title: "Age Category",
      className: "grid",
      type: FormItemType.text,
    },
    {
      name: "is_relay",
      title: "Is Relay",
      type: FormItemType.checkbox,
      className: "grid",
      options: [
        {
          label: "Yes",
          value: true as unknown as string,
        },
      ],
      CustomRender: (field) => (
        <Checkbox
          checked={field.value as unknown as boolean}
          onCheckedChange={(checked) => {
            field.onChange(checked as unknown as string);
          }}
        />
      ),
    },
  ];

  const onSubmit = (data: FormProps) => {
    const competitionId = videoSummary?.competition?.id;
    if (!competitionId) {
      toast.error("Competition not found");
      return;
    }
    upsertRace({
      race: {
        race_id: selectedSwimmingRace?.race_id ?? undefined,
        competition_id: competitionId,
        ...data,
      },
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{Trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Race</DialogTitle>
          <DialogDescription>
            Competition: {videoSummary?.competition?.name}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            className="flex flex-col gap-2"
            onSubmit={form.handleSubmit(onSubmit)}
          >
            {formItems.map((item) => (
              <FormItem key={item.name} control={form.control} {...item} />
            ))}
            <Button type="submit" loading={isPending}>
              Save
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
