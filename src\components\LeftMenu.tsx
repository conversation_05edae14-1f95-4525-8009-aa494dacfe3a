"use client";

import { LayoutGrid, MonitorPlay, Settings } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { View } from "~/components/View";
import { HelperActivator } from "~/components/HelperActivator";
import { cn } from "~/lib/utils";
import { api } from "~/trpc/react";
import { useHotkeys } from "react-hotkeys-hook";
import { VideoStatus } from "~/lib/enums/enums";

export const LeftMenu = () => {
  const pathname = usePathname();
  const router = useRouter();
  const isValidation = pathname.startsWith("/validation");
  const base = isValidation ? "/validation" : "/tagger";

  const isQueue = pathname === base;
  const isReview = pathname.split("/").length === 3;
  const { data: videos } = api.videos.getVideos.useQuery({
    status: isValidation ? VideoStatus.Tagged_by_AI : undefined,
  });

  const baseRoutes = [
    {
      label: <LayoutGrid className="h-6 w-6 text-gray" />,
      href: base,
      active: isQueue,
      hotkey: "F1",
      description: "video queue",
      className: "w-[86px] items-start",
    },
  ];

  const firstVideoToReview = videos?.list?.[0];

  const routes = firstVideoToReview
    ? [
        ...baseRoutes,
        {
          label: <MonitorPlay className="h-6 w-6 text-gray" />,
          href: `/tagger/${firstVideoToReview.id}`,
          active: isReview,
          hotkey: "F2",
          description: "video player",
          className: "w-[86px] translate-y-[20%] items-start",
        },
      ]
    : baseRoutes;

  useHotkeys("f2", () => router.push(base));
  useHotkeys("f3", () => {
    if (firstVideoToReview) {
      router.push(`/tagger/${firstVideoToReview.id}`);
    }
  });

  return (
    <div className="flex h-full w-[60px] flex-col justify-between p-2.5">
      <div className="grid gap-[5px]">
        {routes.map(({ label, href, active, description, className }) => (
          <Link
            href={href}
            key={href}
            prefetch
            className={cn("rounded-[5px] p-2", active && "bg-seaSalt")}
          >
            <View
              key={href}
              // hotkeys={[hotkey]}
              description={description}
              className={className}
            >
              {label}
            </View>
          </Link>
        ))}
      </div>
      <div className="grid gap-[5px]">
        <View
          hotkeys={["Alt"]}
          description="view hot keys"
          className="w-28 items-start"
        >
          <HelperActivator />
        </View>
        <button className="p-2">
          <Settings className="h-6 w-6 text-gray" />
        </button>
      </div>
    </div>
  );
};
