"use client";

import { Pen<PERSON><PERSON>, PlusIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Label } from "~/components/ui/label";
import { useStore } from "~/hooks/store";
import { api } from "~/trpc/react";
import { Selector } from "../Selector";
import { EditSwimmingRace } from "./EditSwimmingRace";
import { EditSwimmingSession } from "./EditSwimmingSession";
import { EditSwimmingRep } from "./EditSwimmingRep";
import { Switch } from "../ui/switch";
import { EditSwimmingLaps } from "./EditSwimmingLaps";

export const EditSwimmingRaceContainer = () => {
  const videoSummary = useStore((state) => state.videoSummary);
  const selectedSwimmingRace = useStore((state) => state.selectedSwimmingRace);
  const setSelectedSwimmingRace = useStore(
    (state) => state.setSelectedSwimmingRace,
  );

  const [isOfficialRace, setIsOfficialRace] = useState(
    videoSummary?.competition?.isOfficial ? true : false,
  );

  const { data: races } = api.swimmingPta.getRaces.useQuery({
    competitionId: videoSummary?.competition?.id,
  });

  useEffect(() => {
    const hasSelectedRace =
      selectedSwimmingRace &&
      races?.find((x) => x.race_id === selectedSwimmingRace.race_id);
    if (!!hasSelectedRace) return;
    if (!selectedSwimmingRace && races && races.length > 0) {
      setSelectedSwimmingRace(races[0]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [races]);

  const selectedRace = races?.find(
    (race) => race.race_id === selectedSwimmingRace?.race_id,
  );

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="ml-auto h-fit px-[5px] py-[2.5px] text-[10px]">
          Edit Info
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[90vh] max-w-[900px] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Swimming Info</DialogTitle>
          <DialogDescription>
            Edit the swimming details for the selected video.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4">
          {videoSummary?.competition && isOfficialRace && (
            <Label>Competition: {videoSummary.competition.name}</Label>
          )}
          <div className="flex items-center space-x-2">
            <Switch
              id="is-race"
              checked={isOfficialRace}
              onCheckedChange={setIsOfficialRace}
            />
            <Label htmlFor="is-race">Is Official Race</Label>
          </div>
          {isOfficialRace && (
            <div className="grid gap-2.5">
              {/* <Label htmlFor="name">Race</Label> */}
              <div className="flex items-end gap-2">
                <Selector
                  containerClassName="grid gap-2.5"
                  className="w-96 bg-white"
                  label="Race"
                  options={races?.map((race) => ({
                    label: `${race.stroke_category} ${race.race_distance}m ${race.gender} ${race.age_category} ${race.round} (${race.classification?.replaceAll("_", " ")})`,
                    value: race.race_id,
                  }))}
                  value={selectedSwimmingRace?.race_id ?? ""}
                  onValueChange={(value) => {
                    const race = races?.find((race) => race.race_id === value);
                    if (race) {
                      setSelectedSwimmingRace(race);
                    }
                  }}
                />
                <EditSwimmingRace
                  race={selectedRace}
                  Trigger={
                    <Button
                      size="icon"
                      variant="search"
                      disabled={!selectedRace}
                    >
                      <Pencil className="h-3 w-3 text-seaSalt-k40" />
                    </Button>
                  }
                />
                <EditSwimmingRace
                  race={selectedRace}
                  Trigger={
                    <Button
                      size="icon"
                      variant="search"
                      onClick={() => setSelectedSwimmingRace(null)}
                    >
                      <PlusIcon className="h-3 w-3 text-seaSalt-k40" />
                    </Button>
                  }
                />
              </div>
            </div>
          )}
          <EditSwimmingSession races={races} isOfficialRace={isOfficialRace} />
          <EditSwimmingRep />
          <EditSwimmingLaps />
        </div>
      </DialogContent>
    </Dialog>
  );
};
