import { TRPCError } from "@trpc/server";
import type { NextRequest } from "next/server";
import { UserRole, VideoStatus } from "~/lib/enums/enums";
import { triggerAI } from "~/server/api/utils/azure";
import { checkToken } from "~/server/api/utils/permissions";
import { returnError } from "~/server/api/utils/returnError";
import { updateVideoStatus } from "~/server/api/utils/video";

/**
 * @swagger
 * /api/v1/shotput/{id}/process:
 *   post:
 *     description: Trigger AI to process shotput video.
 *     tags: [Shotput]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 */

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  if (!id) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid video id",
    });
  }

  try {
    const { tokenPayload } = await checkToken(request);
    if (
      !tokenPayload.roles.includes(UserRole.admin) &&
      !tokenPayload.roles.includes(UserRole.analyst)
    ) {
      throw new TRPCError({
        message: "Unauthorized",
        code: "UNAUTHORIZED",
      });
    }

    const { rowsAffected } = await updateVideoStatus(
      id,
      VideoStatus.AI_in_Progress,
    );
    if (rowsAffected === 0) {
      return Response.json({ message: "Video already processed" });
    }
    await triggerAI(id, "shotput");

    return Response.json({});
  } catch (cause) {
    await updateVideoStatus(id, VideoStatus.Raw);
    return returnError(cause);
  }
}
