import { PopoverClose } from "@radix-ui/react-popover";
import { Trash2, X } from "lucide-react";
import { Button } from "~/components/ui/button";
import { api } from "~/trpc/react";
import { useParams, useRouter } from "next/navigation";
import toast from "react-hot-toast";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { getSport } from "~/lib/utils";
import { useStore } from "~/hooks/store";
import { Sport } from "~/lib/enums/enums";

export const DeleteTagAthlete = ({
  athleteId,
  onSuccess,
}: {
  athleteId?: string;
  onSuccess: () => void;
}) => {
  const router = useRouter();
  const utils = api.useUtils();
  const params = useParams();
  const videoId = params.id as string;

  const videoSummary = useStore((state) => state.videoSummary);
  const sport = getSport(videoSummary);

  const { mutate: deleteAthleteTags } =
    api.swimming.deleteAthleteTags.useMutation({
      onSuccess: () => {
        toast.success("Athlete removed");
        onSuccess();
        router.refresh();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });
  const { mutate: deleteShotputTagAthlete } =
    api.shotput.deleteAthleteTags.useMutation({
      onSuccess: () => {
        toast.success("Athlete removed");
        onSuccess();
        void utils.shotput.getThrows.invalidate({ videoId: videoSummary?.id });
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });

  const handleDelete = () => {
    if (!athleteId) return;
    switch (sport) {
      case Sport.swimming:
        deleteAthleteTags({ athleteId });
        break;
      case "shotput":
        deleteShotputTagAthlete({ athleteId, videoId });
        break;
    }
  };

  return (
    <Popover>
      <PopoverTrigger asChild disabled={!athleteId}>
        <button className="icon-popover">
          <Trash2 className="h-[14px] w-[14px]" />
        </button>
      </PopoverTrigger>
      <PopoverContent className="p-0" align="end">
        <div className="flex w-full flex-col items-start">
          <div className="flex w-full items-center justify-between gap-2.5 border-b px-5 py-2.5">
            <Trash2 className="h-[14px] w-[14px]" />
            <p className="flex-1 text-label">Remove athlete</p>
            <PopoverClose asChild>
              <X
                className="h-[14px] w-[14px] cursor-pointer"
                strokeWidth={1.5}
              />
            </PopoverClose>
          </div>
          <div className="flex w-full flex-col items-start justify-center gap-5 p-5">
            <p className="text-label">
              Remove all current tags and associated athlete.
            </p>
            <Button
              variant="red"
              className="w-full focus-visible:bg-red/80"
              data-state="closed"
              onClick={handleDelete}
            >
              Remove athlete
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
