import { type InferSelectModel, relations, sql } from "drizzle-orm";
import {
  boolean,
  double,
  float,
  index,
  int,
  mysqlEnum,
  mysqlTableCreator,
  primaryKey,
  text,
  timestamp,
  unique,
  uniqueIndex,
  varchar,
} from "drizzle-orm/mysql-core";
import { type AdapterAccount } from "next-auth/adapters";
import {
  shotputHands,
  shotputMovements,
  shotputTypes,
} from "~/lib/enums/shotput";

/**
 * This is an example of how to use the multi-project schema feature of Drizzle ORM. Use the same
 * database instance for multiple projects.
 *
 * @see https://orm.drizzle.team/docs/goodies#multi-project-schema
 */
export const createTable = mysqlTableCreator((name) => `tagger_${name}`);

export const users = createTable("user", {
  id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  name: varchar("name", { length: 255 }),
  email: varchar("email", { length: 255 }).notNull(),
  emailVerified: timestamp("emailVerified", {
    mode: "date",
    fsp: 3,
  }).default(sql`CURRENT_TIMESTAMP(3)`),
  image: varchar("image", { length: 255 }),
});

export const usersRelations = relations(users, ({ many }) => ({
  accounts: many(accounts),
}));

export const accounts = createTable(
  "account",
  {
    userId: varchar("userId", { length: 255 }).notNull(),
    type: varchar("type", { length: 255 })
      .$type<AdapterAccount["type"]>()
      .notNull(),
    provider: varchar("provider", { length: 255 }).notNull(),
    providerAccountId: varchar("providerAccountId", { length: 255 }).notNull(),
    refresh_token: text("refresh_token"),
    access_token: text("access_token"),
    expires_at: int("expires_at"),
    token_type: varchar("token_type", { length: 255 }),
    scope: varchar("scope", { length: 255 }),
    id_token: text("id_token"),
    session_state: varchar("session_state", { length: 255 }),
  },
  (account) => ({
    compoundKey: primaryKey({
      columns: [account.provider, account.providerAccountId],
    }),
    userIdIdx: index("account_userId_idx").on(account.userId),
  }),
);

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, { fields: [accounts.userId], references: [users.id] }),
}));

export const swimmingTags = createTable("swimming_tags", {
  id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  videoId: varchar("video_id", { length: 255 }).notNull(),
  athleteId: varchar("athlete_id", { length: 255 }).notNull(),
  aiConfidence: double("ai_confidence"),
  repId: varchar("rep_id", { length: 255 }),
  frame: int("frame").notNull(),
  tag: varchar("tag", { length: 255 }).notNull(),
  userId: varchar("user_id", { length: 255 }).notNull(),
  aiTagId: varchar("ai_tag_id", { length: 255 }).unique(), //if human make change to an AI tag, this will be the AI tag id
  isDeleted: boolean("is_deleted").notNull().default(false), //if an AI tag is deleted, this will be true
  dateCreated: timestamp("date_created", { mode: "date" }).default(
    sql`CURRENT_TIMESTAMP`,
  ),
  x1: double("x1"),
  x2: double("x2"),
  y1: double("y1"),
  y2: double("y2"),
});

export type SwimmingTag = InferSelectModel<typeof swimmingTags>;

export const shotputThrows = createTable(
  "shotput_throws",
  {
    id: int("id").primaryKey().autoincrement(),
    videoId: varchar("video_id", { length: 255 }).notNull(),
    athleteId: varchar("athlete_id", { length: 255 }).notNull(),
    number: int("number").notNull(),
    movement: mysqlEnum(shotputMovements).notNull(),
    type: mysqlEnum(shotputTypes).notNull(),
    hand: mysqlEnum(shotputHands).notNull(),
    dateCreated: timestamp("date_created", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
    userId: varchar("user_id", { length: 255 }).notNull(),
    distance: double("distance"),
    weight: double("weight"),
    comment: text("comment"),
    isToolUsed: boolean("is_tool_used").notNull().default(false),
  },
  (table) => [unique().on(table.videoId, table.number)],
);

export type ShotputThrows = InferSelectModel<typeof shotputThrows>;

export const shotputThrowsRelations = relations(shotputThrows, ({ many }) => ({
  phases: many(shotputTags),
}));

export const shotputTags = createTable(
  "shotput_tags",
  {
    id: int("id").primaryKey().autoincrement(),
    throwId: int("throw_id"),
    tag: varchar("phase", { length: 100 }).notNull(),
    aiFrame: int("ai_frame"),
    frame: int("frame").notNull(),
    userId: varchar("user_id", { length: 255 }).notNull(),
    isDeleted: boolean("is_deleted").notNull().default(false), //if an AI tag is deleted, this will be true
    dateCreated: timestamp("date_created", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
  },
  (table) => [unique().on(table.throwId, table.tag)],
);

export type ShotputTag = InferSelectModel<typeof shotputTags>;

export const shotputPhasesRelations = relations(shotputTags, ({ one }) => ({
  throws: one(shotputThrows, {
    fields: [shotputTags.throwId],
    references: [shotputThrows.id],
  }),
}));

export const bodyAngles = createTable(
  "BodyAngles",
  {
    id: varchar("id", { length: 255 })
      .notNull()
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    videoId: varchar("video_id", { length: 255 }).notNull(),

    frameNumber: int("frame_number").notNull(),
    name: varchar("name", { length: 100 }).notNull(),
    angle: float("angle").notNull(),
    aiScore: float("ai_score").notNull(),
  },
  (table) => ({
    videoIndex: index("video_idx").on(table.videoId),
    uniqueVideoFrameName: uniqueIndex("bodyAngles_video_frame_name_unique").on(
      table.videoId,
      table.frameNumber,
      table.name,
    ),
  }),
);
export type NewBodyAngle = typeof bodyAngles.$inferInsert;
export type BodyAngle = typeof bodyAngles.$inferSelect;

export const bodyKeypoints = createTable(
  "BodyKeypoints",
  {
    id: varchar("id", { length: 255 })
      .notNull()
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    videoId: varchar("video_id", { length: 255 }).notNull(),
    frameNumber: int("frame_number").notNull(),
    keypointNum: int("keypoint_num").notNull(),
    x: float("x").notNull(),
    y: float("y").notNull(),
    z: float("z").notNull(),
    aiScore: float("ai_score").notNull(),
  },
  (table) => ({
    videoIndex: index("video_idx").on(table.videoId),
    uniqueVideoFrameKeypoint: uniqueIndex(
      "bodyKeypoints_video_frame_keypoint_unique",
    ).on(table.videoId, table.frameNumber, table.keypointNum),
  }),
);
export type NewKeypoint = typeof bodyKeypoints.$inferInsert;
export type BodyKeypoint = typeof bodyKeypoints.$inferSelect;
