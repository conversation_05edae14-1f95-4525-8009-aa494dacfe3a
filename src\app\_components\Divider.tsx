export default function Divider({ text }: { text?: string }) {
  return (
    <div className="my-2 flex items-center">
      <div className="flex-1 border-t border-gray-300 dark:border-gray-600" />
      {text && (
        <span className="px-4 font-medium text-gray-500 dark:text-gray-400">
          {text}
        </span>
      )}
      <div className="flex-1 border-t border-gray-300 dark:border-gray-600" />
    </div>
  );
}
