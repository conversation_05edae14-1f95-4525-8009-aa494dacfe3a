"use client";
import { useForm } from "react-hook-form";
import { useHotkeys } from "react-hotkeys-hook";
import { Card } from "~/components/Card";
import { useParams } from "next/navigation";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Checkbox } from "~/components/ui/check-box";
import { Textarea } from "~/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { useStore } from "~/hooks/store";
import { useShotputTags } from "~/hooks/useShotputTags";
import {
  ShotputHand,
  shotputHandOptions,
  ShotputMovement,
  shotputMovementOptions,
  ShotputType,
  shotputTypeOptions,
} from "~/lib/enums/shotput";
import type { ShotputThrows } from "~/server/db/schema";
import { api } from "~/trpc/react";

const radioGroups = [
  { key: "movement", options: shotputMovementOptions },
  { key: "type", options: shotputTypeOptions },
  { key: "hand", options: shotputHandOptions },
];

export const ShotputThrowForm = ({ disabled }: { disabled: boolean }) => {
  const params = useParams();

  const setShotputThrow = useStore((state) => state.setShotputThrow);
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const videoId = params.id as string;

  const { tags } = useShotputTags();
  const throws = tags?.throws;
  const utils = api.useUtils();

  const firstThrow = throws?.[0];

  const { mutate: upsertShotputThrow } =
    api.shotput.upsertShotputThrow.useMutation({
      onSuccess: (result) => {
        setShotputThrow({
          ...result,
          id: result.id,
          isToolUsed: result.isToolUsed,
        });

        void utils.shotput.getThrows.invalidate();
      },
    });

  const form = useForm<ShotputThrows>({
    defaultValues: {
      number: firstThrow?.number ?? 1,
      movement: firstThrow?.movement ?? ShotputMovement.Rotation,
      type: firstThrow?.type ?? ShotputType.Full,
      hand: firstThrow?.hand ?? ShotputHand.RightHand,
      comment: firstThrow?.comment ?? "",
      weight: firstThrow?.weight ?? 0,
      isToolUsed: firstThrow?.isToolUsed ?? false,
    },
  });

  const handleUpsert = () => {
    if (!selectedAthlete) return;
    const formValues = form.getValues();
    upsertShotputThrow({
      ...formValues,
      id: typeof formValues.id === "number" ? formValues.id : undefined,
      videoId,
      athleteId: selectedAthlete,
      weight: formValues.weight ?? 0,
      comment: formValues.comment ?? "",
      isToolUsed: formValues.isToolUsed ?? false,
    });
  };

  useHotkeys("z", () => form.setValue("movement", ShotputMovement.Rotation));
  useHotkeys("x", () => form.setValue("movement", ShotputMovement.Glide));
  useHotkeys("q", () => form.setValue("type", ShotputType.Full));
  useHotkeys("w", () => form.setValue("type", ShotputType.SouthAfrican));
  useHotkeys("e", () => form.setValue("type", ShotputType.DoubleStance));
  useHotkeys("a", () => form.setValue("type", ShotputType.HalfTurn));
  useHotkeys("s", () => form.setValue("type", ShotputType.Standing));
  useHotkeys("d", () => form.setValue("type", ShotputType.StepThrough));
  useHotkeys("c", () => form.setValue("hand", ShotputHand.LeftHand));
  useHotkeys("v", () => form.setValue("hand", ShotputHand.RightHand));
  return (
    <Card>
      <Form {...form}>
        <form
          // onChange={form.handleSubmit(setShotputThrow)}
          className="space-y-2.5"
        >
          <FormField
            control={form.control}
            name="number"
            render={({ field }) => (
              <FormItem className="grid gap-[5px]">
                <p className="text-smallLabel text-black/60">Throw Number:</p>
                <FormControl className="mt-0 w-full">
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      const throwObj = throws?.find((x) => x.number === +value);
                      setShotputThrow(throwObj);
                      if (throwObj) {
                        form.setValue("movement", throwObj.movement);
                        form.setValue("type", throwObj.type);
                        form.setValue("hand", throwObj.hand);
                        form.setValue("id", throwObj.id);
                        form.setValue("comment", throwObj.comment);
                        form.setValue("weight", throwObj.weight ?? 0);
                        form.setValue("isToolUsed", throwObj.isToolUsed);
                      }
                    }}
                    defaultValue={field.value.toString()}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select athlete" />
                    </SelectTrigger>
                    <SelectContent>
                      {throws?.map((x) => (
                        <SelectItem key={x.id} value={x.number.toString()}>
                          {x.number}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {radioGroups.map((group) => (
            <FormField
              key={group.key}
              control={form.control}
              name={group.key as keyof ShotputThrows}
              render={({ field }) => (
                <FormItem className="flex gap-[5px]">
                  <p className="w-[53px] text-smallLabel capitalize text-black/60">
                    {group.key}:
                  </p>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      disabled={disabled}
                      value={field.value as string}
                      className="grid flex-1 grid-cols-3 gap-[5px]"
                    >
                      {group.options.map((x) => (
                        <FormItem
                          key={x.value}
                          className="flex items-center gap-0.5"
                        >
                          <RadioGroupItem value={x.value} text={x.key} />
                          <p className="text-[10px] leading-[8px] tracking-[0.28px]">
                            {x.value}
                          </p>
                        </FormItem>
                      ))}
                    </RadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
          ))}
          <FormField
            control={form.control}
            name="weight"
            render={({ field }) => (
              <FormItem className="flex gap-[5px]">
                <p className="w-[53px] text-smallLabel text-black/60">
                  Weight:
                </p>
                <FormControl>
                  <Input
                    type="number"
                    className="h-5 w-full"
                    value={field.value?.toString() ?? "0"}
                    onChange={(e) => {
                      const value = Number.parseInt(e.target.value) || 0;
                      field.onChange(value);
                    }}
                    onFinish={handleUpsert}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex gap-5">
            {/* Tool Used Toggle */}
            <FormField
              control={form.control}
              name="isToolUsed"
              render={({ field }) => (
                <FormItem className="flex items-center gap-2">
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <p className="text-smallLabel text-black/60">
                        Tool Used:
                      </p>
                      <Checkbox
                        checked={field.value ?? false}
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                          if (!selectedAthlete) return;
                          const formValues = form.getValues();
                          upsertShotputThrow({
                            ...formValues,
                            id:
                              typeof formValues.id === "number"
                                ? formValues.id
                                : undefined,
                            videoId,
                            athleteId: selectedAthlete,
                            isToolUsed: !!checked,
                            weight: formValues.weight ?? 0,
                            comment: formValues.comment ?? "",
                          });
                        }}
                        disabled={disabled}
                      />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="comment"
            render={({ field }) => (
              <FormItem className="flex flex-col gap-[5px]">
                <p className="text-smallLabel text-black/60">Comment:</p>
                <FormControl>
                  <Textarea
                    className="min-h-[60px] w-full resize-none"
                    value={field.value ?? ""}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                    }}
                    onBlur={handleUpsert}
                    placeholder="Add any notes about the throw..."
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </Card>
  );
};
