import type { Metada<PERSON> } from "next";
import { LeftMenu } from "~/components/LeftMenu";

export const metadata: Metadata = {
  title: "Video tagger tool",
  description: "",
  icons: [{ rel: "icon", url: "/favicon.png" }],
};

export default function ValidationVideoLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex flex-1">
      <LeftMenu />
      <div className="w-full p-2.5">
        <div className="flex h-full w-full flex-col gap-2.5 rounded-xl border bg-seaSalt-40 p-2.5">
          {children}
        </div>
      </div>
    </div>
  );
}
