{"name": "hpsnz-validation-tool", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "bun next-swagger-doc-cli next-swagger-doc.json && next build", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@auth/drizzle-adapter": "^1.7.2", "@azure/identity": "^4.8.0", "@azure/storage-queue": "^12.26.0", "@ffmpeg/core": "^0.12.6", "@ffmpeg/ffmpeg": "^0.12.6", "@ffmpeg/types": "^0.12.2", "@ffmpeg/util": "^0.12.1", "@hookform/resolvers": "^3.10.0", "@paralleldrive/cuid2": "^2.2.2", "@planetscale/database": "^1.19.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5.50.0", "@tanstack/react-virtual": "^3.12.0", "@trpc/client": "^11.0.0-rc.446", "@trpc/react-query": "^11.0.0-rc.446", "@trpc/server": "^11.0.0-rc.446", "@types/jsonwebtoken": "^9.0.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "drizzle-orm": "^0.40.1", "geist": "^1.3.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "lucide-react": "^0.462.0", "m3u8-parser": "^7.2.0", "next": "14.1.4", "next-auth": "^4.24.6", "next-swagger-doc": "^0.4.0", "react": "18.2.0", "react-day-picker": "8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "react-hotkeys-hook": "^4.6.1", "server-only": "^0.0.1", "superjson": "^2.2.1", "swagger-ui-react": "5.17.2", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "video.js": "^8.23.3", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@types/eslint": "^8.56.10", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/swagger-ui-react": "^4.18.3", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "drizzle-kit": "^0.30.2", "eslint": "^8.57.0", "eslint-config-next": "^15.0.1", "eslint-plugin-drizzle": "^0.2.3", "postcss": "^8.4.39", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.3", "typescript": "^5.5.3"}, "ct3aMetadata": {"initVersion": "7.38.1"}}