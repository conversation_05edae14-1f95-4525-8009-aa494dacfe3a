"use client";

import type { DrawingMode } from "~/lib/interface";

interface DrawingModePickerProps {
  selected: DrawingMode;
  onChange: (mode: DrawingMode) => void;
}

export const DrawingModePicker = ({
  selected,
  onChange,
}: DrawingModePickerProps) => {
  const modes: DrawingMode[] = ["line", "rectangle"];

  return (
    <div className="flex flex-col gap-1 text-sm">
      <label className="font-medium">Drawing Mode:</label>
      <div className="flex items-center gap-3">
        {modes.map((mode) => (
          <label key={mode} className="flex items-center gap-1 capitalize">
            <input
              type="radio"
              name="drawingMode"
              value={mode}
              checked={selected === mode}
              onChange={() => onChange(mode)}
            />
            {mode}
          </label>
        ))}
      </div>
    </div>
  );
};
