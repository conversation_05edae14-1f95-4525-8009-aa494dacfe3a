import { Filter, MoveDownIcon, MoveUpIcon } from "lucide-react";
import { TagList } from "~/app/_components/Tags/TagList";
import { TagListVirtual } from "~/app/_components/Tags/TagListVirtual";
import { Card } from "~/components/Card";
import { Badge } from "~/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { useStore } from "~/hooks/store";
import { VideoStatus } from "~/lib/enums/enums";
import type { TagUI } from "~/lib/interface";
import { TagChangeList } from "./TagChangeList";

export const TagListView = ({
  isVirtual,
  height,
  tagOptions,
  onRemoveTag,
}: {
  isVirtual: boolean;
  height?: number;
  tagOptions: TagUI[];
  onRemoveTag: (id: string) => void;
}) => {
  const tagFilterValues = useStore((state) => state.tagFilterValues);
  const videoSummary = useStore((state) => state.videoSummary);
  const currentFrame = useStore((state) => state.currentFrame);
  const filteredTags = useStore((state) => state.filteredTags);
  const editingTagId = useStore((state) => state.editingTagId);
  const tagChanges = useStore((state) => state.tagChanges);
  const setTagFilterValues = useStore((state) => state.setTagFilterValues);
  const setCurrentFrame = useStore((state) => state.setCurrentFrame);
  const setEditingTagId = useStore((state) => state.setEditingTagId);
  const setTagSortByAiConfidence = useStore(
    (state) => state.setTagSortByAiConfidence,
  );
  const tagSortByAiConfidence = useStore(
    (state) => state.tagSortByAiConfidence,
  );

  const tagChangeCount =
    tagChanges.humanAddedTags.length +
    tagChanges.humanUpdatedTags.length +
    tagChanges.removedAITags.length;

  const disabled =
    !videoSummary || videoSummary.status !== VideoStatus.Tagged_by_AI;

  const onFilterClick = (filterValue: string) => {
    if (tagFilterValues.includes(filterValue)) {
      setTagFilterValues(tagFilterValues.filter((x) => x !== filterValue));
    } else {
      setTagFilterValues([...tagFilterValues, filterValue]);
    }
  };

  const onTagClick = (value: string) => {
    if (!currentFrame) return;
    const newFrame = Math.round(+value);
    setCurrentFrame(newFrame);
  };

  const tagTypes = [
    ...new Set(tagOptions.filter((x) => !!x).map((x) => x.keyword ?? x.value)),
  ];

  return (
    <div className="relative w-full">
      <div className="absolute right-0 top-0 flex gap-1">
        <Badge
          className="my-auto h-fit cursor-pointer select-none py-1"
          variant="tag"
          onClick={() => {
            if (tagSortByAiConfidence === null) {
              setTagSortByAiConfidence("asc");
            } else if (tagSortByAiConfidence === "asc") {
              setTagSortByAiConfidence("desc");
            } else {
              setTagSortByAiConfidence(null);
            }
          }}
        >
          Sort %
          {tagSortByAiConfidence === "asc" && (
            <MoveUpIcon className="ml-1 h-3 w-3" />
          )}
          {tagSortByAiConfidence === "desc" && (
            <MoveDownIcon className="ml-1 h-3 w-3" />
          )}
        </Badge>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div className="cursor-pointer rounded-[5px] border bg-white p-1.5">
              <Filter className="h-3.5 w-3.5 text-seaSalt-k40" />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {tagTypes.map((filterValue) => {
              return (
                <DropdownMenuCheckboxItem
                  key={filterValue}
                  className="capitalize"
                  checked={tagFilterValues.includes(filterValue)}
                  onClick={() => {
                    onFilterClick(filterValue);
                  }}
                >
                  {filterValue}
                </DropdownMenuCheckboxItem>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Tabs defaultValue="tags">
        <TabsList className="">
          <TabsTrigger value="tags">Tags ({filteredTags.length})</TabsTrigger>
          <TabsTrigger value="changes">Changes ({tagChangeCount})</TabsTrigger>
        </TabsList>
        <TabsContent value="tags">
          <Card className="p-0">
            {isVirtual && (
              <TagListVirtual
                tags={filteredTags}
                disabled={disabled}
                editingTagId={editingTagId}
                tagOptions={tagOptions}
                onTagClick={onTagClick}
                setEditingTagId={setEditingTagId}
                onRemoveTag={onRemoveTag}
              />
            )}

            {!isVirtual && (
              <TagList
                tags={filteredTags}
                disabled={disabled}
                editingTagId={editingTagId}
                height={height}
                tagOptions={tagOptions}
                onTagClick={onTagClick}
                setEditingTagId={setEditingTagId}
                onRemoveTag={onRemoveTag}
              />
            )}
          </Card>
        </TabsContent>
        <TabsContent value="changes">
          <Card className="p-0">
            <TagChangeList height={height} />
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
