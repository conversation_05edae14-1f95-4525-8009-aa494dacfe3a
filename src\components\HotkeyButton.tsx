import { cn, getTagDisplay } from "~/lib/utils";
import type { AllSport, TagUI } from "~/lib/interface";
import { useStore } from "~/hooks/store";
import { ShotputHand } from "~/lib/enums/shotput";

export const HotkeyButton = ({
  disabled,
  textClassName,
  tag,
  title,
  showDescription,
  sport,
  onClick,
}: {
  disabled?: boolean;
  textClassName?: string;
  tag: NonNullable<TagUI>;
  title?: string;
  showDescription?: boolean;
  sport?: AllSport;
  onClick: () => void;
}) => {
  const shotputThrow = useStore((state) => state.shotputThrow);
  const discusThrow = useStore((state) => state.discusThrow);
  const display = getTagDisplay({
    label: tag.value,
    leftHand:
      (shotputThrow?.hand ?? discusThrow?.hand) === ShotputHand.LeftHand,
    sport,
  });
  return (
    <button
      className="flex w-10 flex-col gap-0.5 text-center"
      disabled={disabled}
      onClick={onClick}
      type="button"
      title={title ?? display}
    >
      <div
        className={cn(
          "mx-auto flex h-4 w-4 items-center justify-center rounded-[2.5px] border bg-seaSalt-40",
          tag.className,
        )}
      >
        <p>{tag.key}</p>
      </div>
      {showDescription && (
        <p
          className={cn(
            "w-10 lowercase leading-[8px] text-black",
            textClassName,
          )}
        >
          {display}
        </p>
      )}
    </button>
  );
};
