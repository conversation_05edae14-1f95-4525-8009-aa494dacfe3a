import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { env } from "~/env";
import { SortBy, Sport, VideoStatus } from "~/lib/enums/enums";
import type { VideoPlayback } from "~/lib/interface";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import type { RouterOutputs } from "~/trpc/react";
import { getAthletes, getCompetitionById } from "../utils/pta/pta";
import {
  callVideoPortal,
  getVideos,
  getVideoSummary,
  updateVideoStatus,
} from "../utils/video";

type Outputs = RouterOutputs["videos"];

export type GetVideosOutput = Outputs["getVideos"];
export type GetVideoInfoOutput = Outputs["getVideoInfo"];

const swimmingRounds = [
  "Heat",
  "Semi-Final",
  "Quarter-Final",
  "Final",
  "Time Trial",
  "Relay - Heat",
  "Relay - Final",
];

export const videoRouter = createTRPCRouter({
  getVideos: protectedProcedure
    .input(
      z
        .object({
          sport: z.array(z.nativeEnum(Sport)).optional(),
          searchText: z.string().optional(),
          sortBy: z.nativeEnum(SortBy).optional(),
          status: z.nativeEnum(VideoStatus).optional(),
          page: z.number().optional().default(1),
        })
        .optional(),
    )
    .query(async ({ ctx, input }) => {
      const { sport, searchText, sortBy, status, page } = input ?? {};
      return getVideos({
        token: ctx.session.accessToken,
        sport,
        searchText,
        status,
        sortBy,
        page,
      });
    }),
  getPlayback: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const url = `${env.NEXT_PUBLIC_VIDEO_PORTAL_URL}/api/v1/video/${input.id}/playbackUrl`;
      const response = await fetch(url, {
        headers: {
          Authorization: "Bearer " + ctx.session.accessToken,
        },
      });
      const json = (await response.json()) as VideoPlayback;
      return json;
    }),
  getVideoSourceStream: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      const url = `${env.NEXT_PUBLIC_VIDEO_PORTAL_URL}/api/v1/video/${input.id}/sourceStream`;

      const response = await fetch(url, {
        headers: {
          Authorization: "Bearer " + ctx.session.accessToken,
        },
      });

      const m3u8Text = await response.text();

      return { m3u8Text, url };
    }),
  getVideoInfo: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      const video = await getVideoSummary(input.id);
      if (!video) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch video",
        });
      }
      const token = ctx.session.accessToken;

      if (video.competition) {
        const competition = await getCompetitionById({
          token,
          id: video.competition.id,
        });
        video.competition.swimCourseType = competition.swim_course_type;
        video.competition.date = competition.start_date;
        video.competition.name = competition.competition_name;
        video.competition.isOfficial = competition.is_official;
      }

      const athletes = await getAthletes({
        token,
        ids: video?.athletes?.map((x) => x.athleteId),
      });

      return {
        ...video,
        athletes: video.athletes?.map((x) => ({
          ...x,
          name:
            athletes?.find((y) => y.athlete_id === x.athleteId)?.first_name +
            " " +
            athletes?.find((y) => y.athlete_id === x.athleteId)?.last_name,
        })),
        rounds: swimmingRounds,
      };
    }),
  completeTag: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      await updateVideoStatus(input.id);
    }),
  setVideoFps: protectedProcedure
    .input(z.object({ id: z.string(), fps: z.number() }))
    .mutation(async ({ input }) => {
      await callVideoPortal(input.id, {
        method: "PUT",
        body: JSON.stringify({ fps: input.fps }),
      });
    }),
});
