import { type InferSelectModel, relations, sql } from "drizzle-orm";
import { createTable } from "./schema";
import {
  boolean,
  float,
  int,
  mysqlEnum,
  timestamp,
  unique,
  varchar,
} from "drizzle-orm/mysql-core";
import {
  landingDescriptions,
  snowFeatureTypes,
  snowJumpSpinDirections,
  snowLandingTypes,
  snowLandingZones,
  snowRailSpinDirections,
} from "~/lib/enums/snow";

//slope style parent table
export const snowSlopeStyleTags = createTable("snow_slope_style_tags", {
  id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  startFrame: int("start_frame").notNull(),
  endFrame: int("end_frame").notNull(),
  score: float("score"),
  featureId: varchar("feature_id", { length: 255 }).notNull(),
  sectionNum: int("section_num").notNull(), //section feature number
  sectionType: mysqlEnum(snowFeatureTypes).notNull(),
  snowSportsRunId: varchar("snow_sports_run_id", { length: 255 }),
  videoId: varchar("video_id", { length: 255 }).notNull(),
  dateCreated: timestamp("date_created", { mode: "date" }).default(
    sql`CURRENT_TIMESTAMP`,
  ),
});

export type NewSnowSlopeStyleTag = typeof snowSlopeStyleTags.$inferInsert;

export type SlopeStyleTagParent = InferSelectModel<typeof snowSlopeStyleTags>;

export const snowSlopeStyleRelations = relations(
  snowSlopeStyleTags,
  ({ one, many }) => ({
    landingDescriptions: many(landingDescriptionEnum),
    snowSlopeRail: one(snowSlopeRailTags, {
      fields: [snowSlopeStyleTags.id],
      references: [snowSlopeRailTags.snowSlopeStyleTagId],
    }),
    snowSlopeJump: one(snowSlopeJumpTags, {
      fields: [snowSlopeStyleTags.id],
      references: [snowSlopeJumpTags.snowSlopeStyleTagId],
    }),
    snowSlopeTransition: one(snowSlopeTransition, {
      fields: [snowSlopeStyleTags.id],
      references: [snowSlopeTransition.snowSlopeStyleTagId],
    }),
  }),
);

export const snowSlopeJumpTags = createTable("snow_slope_jump_tags", {
  snowSlopeStyleTagId: varchar("snow_slope_style_tag_id", {
    length: 255,
  })
    .notNull()
    .primaryKey(),
  takeOffFrame: int("take_off_frame"),
  jumpTakeoffModifierId: int("jump_takeoff_modifier_id"),
  landingFrame: int("landing_frame"),
  grabStart: int("grab_start"),
  grabEnd: int("grab_end"),
  jumpTypeId: int("jump_type_id").notNull(),
  spinDirection: mysqlEnum(snowJumpSpinDirections),
  spinTypeId: int("spin_type_id"),
  spinAmount: int("spin_amount"),
  spinModifierId: int("spin_modifier_id"),
  switch: boolean("switch"),
  cab: boolean("cab"),
  progression: boolean("progression").default(false),
  grabTypeId: int("grab_type_id"),
  executionId: int("execution_id"),
  landingZone: mysqlEnum(snowLandingZones),
  landingType: mysqlEnum(snowLandingTypes),
});

export type NewSnowSlopeJumpTag = typeof snowSlopeJumpTags.$inferInsert;

export const snowSlopeJumpRelations = relations(
  snowSlopeJumpTags,
  ({ one }) => ({
    snowSlopeStyleTag: one(snowSlopeStyleTags, {
      fields: [snowSlopeJumpTags.snowSlopeStyleTagId],
      references: [snowSlopeStyleTags.id],
    }),
    jumpTakeoffModifier: one(snowModifiers, {
      fields: [snowSlopeJumpTags.jumpTakeoffModifierId],
      references: [snowModifiers.id],
    }),
    jumpType: one(snowJumps, {
      fields: [snowSlopeJumpTags.jumpTypeId],
      references: [snowJumps.id],
    }),
    execution: one(snowExecutions, {
      fields: [snowSlopeJumpTags.executionId],
      references: [snowExecutions.id],
    }),
    spinType: one(snowSpinTypes, {
      fields: [snowSlopeJumpTags.spinTypeId],
      references: [snowSpinTypes.id],
    }),
    spinModifier: one(snowModifiers, {
      fields: [snowSlopeJumpTags.spinModifierId],
      references: [snowModifiers.id],
    }),
    grabType: one(snowGrabs, {
      fields: [snowSlopeJumpTags.grabTypeId],
      references: [snowGrabs.id],
    }),
  }),
);

export const snowSlopeRailTags = createTable("snow_slope_rail_tags", {
  snowSlopeStyleTagId: varchar("snow_slope_style_tag_id", {
    length: 255,
  })
    .notNull()
    .primaryKey(),
  takeOffFrame: int("take_off_frame"),
  landingFrame: int("landing_frame"),
  grabStart: int("grab_start"),
  grabEnd: int("grab_end"),
  railFeatureId: int("rail_feature_id"),
  progression: boolean("progression").default(false),
  switch: boolean("switch"),
  cab: boolean("cab"),
  railInSpinDirection: mysqlEnum(snowRailSpinDirections),
  railInSpinAmount: int("rail_in_spin_amount"),
  railInSpinModifierId: int("rail_in_spin_modifier_id"),
  railInGrabId: int("rail_in_grab_id"),
  railTrickId: int("rail_trick_id"),
  railOnSpinDirection: mysqlEnum(snowRailSpinDirections),
  railOnSpinAmount: int("rail_on_spin_amount"),
  railOnSpinModifierId: int("rail_on_spin_modifier_id"),
  railOnGrabId: int("rail_on_grab_id"),
  railOutSpinDirection: mysqlEnum(snowRailSpinDirections),
  railOutSpinAmount: int("rail_out_spin_amount"),
  railOutSpinModifierId: int("rail_out_spin_modifier_id"),
  railOutGrabId: int("rail_out_grab_id"),
  executionId: int("execution_id"),
  landingZone: mysqlEnum(snowLandingZones),
  landingType: mysqlEnum(snowLandingTypes),
});

export type SlopeRailTag = InferSelectModel<typeof snowSlopeRailTags>;
export type NewSnowSlopeRailTag = typeof snowSlopeRailTags.$inferInsert;

export const snowSlopeRailRelations = relations(
  snowSlopeRailTags,
  ({ one }) => ({
    snowSlopeStyleTag: one(snowSlopeStyleTags, {
      fields: [snowSlopeRailTags.snowSlopeStyleTagId],
      references: [snowSlopeStyleTags.id],
    }),
    railFeature: one(snowRailFeatures, {
      fields: [snowSlopeRailTags.railFeatureId],
      references: [snowRailFeatures.id],
    }),
    railInSpinModifier: one(snowModifiers, {
      fields: [snowSlopeRailTags.railInSpinModifierId],
      references: [snowModifiers.id],
    }),
    railInGrad: one(snowGrabs, {
      fields: [snowSlopeRailTags.railInGrabId],
      references: [snowGrabs.id],
    }),
    railTrick: one(snowTricks, {
      fields: [snowSlopeRailTags.railTrickId],
      references: [snowTricks.id],
    }),
    railOnSpinModifier: one(snowModifiers, {
      fields: [snowSlopeRailTags.railOnSpinModifierId],
      references: [snowModifiers.id],
    }),
    railOnGrad: one(snowGrabs, {
      fields: [snowSlopeRailTags.railOnGrabId],
      references: [snowGrabs.id],
    }),
    railOutSpinModifier: one(snowModifiers, {
      fields: [snowSlopeRailTags.railOutSpinModifierId],
      references: [snowModifiers.id],
    }),
    railOutGrad: one(snowGrabs, {
      fields: [snowSlopeRailTags.railOutGrabId],
      references: [snowGrabs.id],
    }),
    trickExecution: one(snowExecutions, {
      fields: [snowSlopeRailTags.executionId],
      references: [snowExecutions.id],
    }),
  }),
);

//transition parent table
export const snowSlopeTransition = createTable("snow_slope_transition", {
  snowSlopeStyleTagId: varchar("snow_slope_style_tag_id", {
    length: 255,
  })
    .notNull()
    .primaryKey(),
  progression: boolean("progression").default(false),
  switch: boolean("switch"),
  cab: boolean("cab"),
  grabStart: int("grab_start"),
  grabEnd: int("grab_end"),
  takeOffFrame: int("take_off_frame"),
  landingFrame: int("landing_frame"),
  executionId: int("execution_id"),
  landingZone: mysqlEnum(snowLandingZones),
  landingType: mysqlEnum(snowLandingTypes),
});

export type NewSnowSlopeTransition = typeof snowSlopeTransition.$inferInsert;

export const snowSlopeTransitionRelations = relations(
  snowSlopeTransition,
  ({ one }) => ({
    snowSlopeTransitionRail: one(snowSlopeTransitionRailTags, {
      fields: [snowSlopeTransition.snowSlopeStyleTagId],
      references: [snowSlopeTransitionRailTags.snowSlopeStyleTagId],
    }),
    snowSlopeTransitionJump: one(snowSlopeTransitionJumpTags, {
      fields: [snowSlopeTransition.snowSlopeStyleTagId],
      references: [snowSlopeTransitionJumpTags.snowSlopeStyleTagId],
    }),
    snowSlopeTransitionTransition: one(snowSlopeTransitionTransitionTags, {
      fields: [snowSlopeTransition.snowSlopeStyleTagId],
      references: [snowSlopeTransitionTransitionTags.snowSlopeStyleTagId],
    }),
    snowSlopeStyleTag: one(snowSlopeStyleTags, {
      fields: [snowSlopeTransition.snowSlopeStyleTagId],
      references: [snowSlopeStyleTags.id],
    }),
    trickExecution: one(snowExecutions, {
      fields: [snowSlopeTransition.executionId],
      references: [snowExecutions.id],
    }),
  }),
);

export const snowSlopeTransitionJumpTags = createTable(
  "snow_slope_transition_jump_tags",
  {
    snowSlopeStyleTagId: varchar("snow_slope_style_tag_id", {
      length: 255,
    })
      .notNull()
      .primaryKey(),
    jumpTypeId: int("jump_type_id").notNull(),
    jumpTakeoffModifierId: int("jump_takeoff_modifier_id"),
    spinDirection: mysqlEnum(snowJumpSpinDirections),
    spinTypeId: int("spin_type_id"),
    spinAmount: int("spin_amount"),
    spinModifierId: int("spin_modifier_id"),
    grabTypeId: int("grab_type_id"),
  },
);

export type NewSnowSlopeTransitionJumpTag =
  typeof snowSlopeTransitionJumpTags.$inferInsert;

export const snowSlopeTransitionJumpRelations = relations(
  snowSlopeTransitionJumpTags,
  ({ one }) => ({
    snowSlopeTransition: one(snowSlopeTransition, {
      fields: [snowSlopeTransitionJumpTags.snowSlopeStyleTagId],
      references: [snowSlopeTransition.snowSlopeStyleTagId],
    }),
    jumpTakeoffModifier: one(snowModifiers, {
      fields: [snowSlopeTransitionJumpTags.jumpTakeoffModifierId],
      references: [snowModifiers.id],
    }),
    spinType: one(snowSpinTypes, {
      fields: [snowSlopeTransitionJumpTags.spinTypeId],
      references: [snowSpinTypes.id],
    }),
    spinModifier: one(snowModifiers, {
      fields: [snowSlopeTransitionJumpTags.spinModifierId],
      references: [snowModifiers.id],
    }),
    grabType: one(snowGrabs, {
      fields: [snowSlopeTransitionJumpTags.grabTypeId],
      references: [snowGrabs.id],
    }),
    jumpType: one(snowJumps, {
      fields: [snowSlopeTransitionJumpTags.jumpTypeId],
      references: [snowJumps.id],
    }),
  }),
);

export const snowSlopeTransitionRailTags = createTable(
  "snow_slope_transition_rail_tags",
  {
    snowSlopeStyleTagId: varchar("snow_slope_style_tag_id", {
      length: 255,
    })
      .notNull()
      .primaryKey(),
    railFeatureId: int("rail_feature_id"),
    railInSpinDirection: mysqlEnum(snowRailSpinDirections),
    railInSpinAmount: int("rail_in_spin_amount"),
    railInSpinModifierId: int("rail_in_spin_modifier_id"),
    railInGrabId: int("rail_in_grab_id"),
    railTrickId: int("rail_trick_id"),
    railOnSpinDirection: mysqlEnum(snowRailSpinDirections),
    railOnSpinAmount: int("rail_on_spin_amount"),
    railOnSpinModifierId: int("rail_on_spin_modifier_id"),
    railOnGrabId: int("rail_on_grab_id"),
    railOutSpinDirection: mysqlEnum(snowRailSpinDirections),
    railOutSpinAmount: int("rail_out_spin_amount"),
    railOutSpinModifierId: int("rail_out_spin_modifier_id"),
    railOutGrabId: int("rail_out_grab_id"),
  },
);

export type NewSnowSlopeTransitionRailTag =
  typeof snowSlopeTransitionRailTags.$inferInsert;

export const snowSlopeTransitionRailRelations = relations(
  snowSlopeTransitionRailTags,
  ({ one }) => ({
    snowSlopeTransition: one(snowSlopeTransition, {
      fields: [snowSlopeTransitionRailTags.snowSlopeStyleTagId],
      references: [snowSlopeTransition.snowSlopeStyleTagId],
    }),
    railFeature: one(snowRailFeatures, {
      fields: [snowSlopeTransitionRailTags.railFeatureId],
      references: [snowRailFeatures.id],
    }),
    railInSpinModifier: one(snowModifiers, {
      fields: [snowSlopeTransitionRailTags.railInSpinModifierId],
      references: [snowModifiers.id],
    }),
    railInGrad: one(snowGrabs, {
      fields: [snowSlopeTransitionRailTags.railInGrabId],
      references: [snowGrabs.id],
    }),
    railTrick: one(snowTricks, {
      fields: [snowSlopeTransitionRailTags.railTrickId],
      references: [snowTricks.id],
    }),
    railOnSpinModifier: one(snowModifiers, {
      fields: [snowSlopeTransitionRailTags.railOnSpinModifierId],
      references: [snowModifiers.id],
    }),
    railOnGrad: one(snowGrabs, {
      fields: [snowSlopeTransitionRailTags.railOnGrabId],
      references: [snowGrabs.id],
    }),
    railOutSpinModifier: one(snowModifiers, {
      fields: [snowSlopeTransitionRailTags.railOutSpinModifierId],
      references: [snowModifiers.id],
    }),
    railOutGrad: one(snowGrabs, {
      fields: [snowSlopeTransitionRailTags.railOutGrabId],
      references: [snowGrabs.id],
    }),
  }),
);

export const snowSlopeTransitionTransitionTags = createTable(
  "snow_slope_transition_transition_tags",
  {
    snowSlopeStyleTagId: varchar("tag_id", {
      length: 255,
    })
      .notNull()
      .primaryKey(),
    transitionTypeId: int("transition_type_id").notNull(),
    jumpTakeoffModifierId: int("jump_takeoff_modifier_id"),
    spinDirection: mysqlEnum(snowJumpSpinDirections),
    spinTypeId: int("spin_type_id"),
    spinAmount: int("spin_amount"),
    spinModifierId: int("spin_modifier_id"),
    grabTypeId: int("grab_type_id"),
  },
);

export type TransitionTransitionTag = InferSelectModel<
  typeof snowSlopeTransitionTransitionTags
>;
export type NewSnowSlopeTransitionTransitionTag =
  typeof snowSlopeTransitionTransitionTags.$inferInsert;

export const snowSlopeTransitionTransitionRelations = relations(
  snowSlopeTransitionTransitionTags,
  ({ one }) => ({
    snowSlopeTransition: one(snowSlopeTransition, {
      fields: [snowSlopeTransitionTransitionTags.snowSlopeStyleTagId],
      references: [snowSlopeTransition.snowSlopeStyleTagId],
    }),
    jumpTakeoffModifier: one(snowModifiers, {
      fields: [snowSlopeTransitionTransitionTags.jumpTakeoffModifierId],
      references: [snowModifiers.id],
    }),
    spinType: one(snowSpinTypes, {
      fields: [snowSlopeTransitionTransitionTags.spinTypeId],
      references: [snowSpinTypes.id],
    }),
    spinModifier: one(snowModifiers, {
      fields: [snowSlopeTransitionTransitionTags.spinModifierId],
      references: [snowModifiers.id],
    }),
    grabType: one(snowGrabs, {
      fields: [snowSlopeTransitionTransitionTags.grabTypeId],
      references: [snowGrabs.id],
    }),
    transitionType: one(snowTransitions, {
      fields: [snowSlopeTransitionTransitionTags.transitionTypeId],
      references: [snowTransitions.id],
    }),
  }),
);

//half pipe
export const snowHalfPipeTags = createTable(
  "snow_half_pipe_tags",
  {
    id: varchar("id", { length: 255 })
      .notNull()
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    startFrame: int("start_frame").notNull(),
    endFrame: int("end_frame").notNull(),
    trickNum: int("trick_num").notNull(), //hit number
    amplitude: float("amplitude"),
    jumpTypeId: int("jump_type_id").notNull(),
    jumpTakeoffModifierId: int("jump_takeoff_modifier_id"),
    progression: boolean("progression").notNull().default(false),
    switch: boolean("switch").notNull().default(false), //1 for switch, 0 for forwards
    cab: boolean("cab").notNull().default(false),
    spinDirection: varchar("spin_direction", { length: 100 }),
    spinTypeId: int("spin_type_id"),
    spinAmount: int("spin_amount"),
    spinModifierId: int("spin_modifier_id"),
    grabTypeId: int("grab_type_id"),
    grabStart: int("grab_start"),
    grabEnd: int("grab_end"),
    takeOffFrame: int("take_off_frame"),
    landingFrame: int("landing_frame"),
    executionId: int("execution_id"),
    landingZone: varchar("landing_zone", { length: 100 }),
    landingType: varchar("landing_type", { length: 100 }),
    snowSportsRunId: varchar("snow_sports_run_id", { length: 255 }).notNull(),
    videoId: varchar("video_id", { length: 255 }).notNull(),
    dateCreated: timestamp("date_created", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
  },
  (table) => [unique().on(table.snowSportsRunId, table.trickNum)],
);

export type HalfPipeTag = InferSelectModel<typeof snowHalfPipeTags>;
export type HalfPipeWith = HalfPipeTag & {
  landingDescriptions: LandingDescription[];
};

export const snowHalfPipeTagsRelations = relations(
  snowHalfPipeTags,
  ({ one, many }) => ({
    jumpTakeoffModifier: one(snowModifiers, {
      fields: [snowHalfPipeTags.jumpTakeoffModifierId],
      references: [snowModifiers.id],
    }),
    landingDescriptions: many(landingDescriptionEnum),
    jumpType: one(snowJumps, {
      fields: [snowHalfPipeTags.jumpTypeId],
      references: [snowJumps.id],
    }),
    spinType: one(snowSpinTypes, {
      fields: [snowHalfPipeTags.spinTypeId],
      references: [snowSpinTypes.id],
    }),
    spinModifier: one(snowModifiers, {
      fields: [snowHalfPipeTags.spinModifierId],
      references: [snowModifiers.id],
    }),
    trickExecution: one(snowExecutions, {
      fields: [snowHalfPipeTags.executionId],
      references: [snowExecutions.id],
    }),
    grabType: one(snowGrabs, {
      fields: [snowHalfPipeTags.grabTypeId],
      references: [snowGrabs.id],
    }),
  }),
);

//big air
export const snowBigAirTags = createTable("snow_big_air_tags", {
  id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  startFrame: int("start_frame").notNull(),
  endFrame: int("end_frame").notNull(),
  score: float("score"), // big air & slope style only
  jumpTypeId: int("jump_type_id").notNull(),
  jumpTakeoffModifierId: int("jump_takeoff_modifier_id"),
  progression: boolean("progression").default(false),
  switch: boolean("switch"), //1 for switch, 0 for forwards
  cab: boolean("cab"),
  spinDirection: varchar("spin_direction", { length: 100 }),
  spinTypeId: int("spin_type_id"),
  spinAmount: int("spin_amount"),
  spinModifierId: int("spin_modifier_id"),
  grabTypeId: int("grab_type_id"),
  grabStart: int("grab_start"),
  grabEnd: int("grab_end"),
  takeOffFrame: int("take_off_frame"),
  landingFrame: int("landing_frame"),
  executionId: int("execution_id"),
  landingZone: varchar("landing_zone", { length: 100 }),
  landingType: varchar("landing_type", { length: 100 }),
  snowSportsRunId: varchar("snow_sports_run_id", { length: 255 }).unique(),
  videoId: varchar("video_id", { length: 255 }).notNull(),
  dateCreated: timestamp("date_created", { mode: "date" }).default(
    sql`CURRENT_TIMESTAMP`,
  ),
});

export type BigAirTag = InferSelectModel<typeof snowBigAirTags>;

export const snowBigAirTagsRelations = relations(
  snowBigAirTags,
  ({ one, many }) => ({
    jumpTakeoffModifier: one(snowModifiers, {
      fields: [snowBigAirTags.jumpTakeoffModifierId],
      references: [snowModifiers.id],
    }),
    landingDescriptions: many(landingDescriptionEnum),
    jumpType: one(snowJumps, {
      fields: [snowBigAirTags.jumpTypeId],
      references: [snowJumps.id],
    }),
    spinType: one(snowSpinTypes, {
      fields: [snowBigAirTags.spinTypeId],
      references: [snowSpinTypes.id],
    }),
    spinModifier: one(snowModifiers, {
      fields: [snowBigAirTags.spinModifierId],
      references: [snowModifiers.id],
    }),
    trickExecution: one(snowExecutions, {
      fields: [snowBigAirTags.executionId],
      references: [snowExecutions.id],
    }),
    grabType: one(snowGrabs, {
      fields: [snowBigAirTags.grabTypeId],
      references: [snowGrabs.id],
    }),
  }),
);

export const snowFeatures = createTable("snow_features", {
  id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  featureNumber: int("feature_number").notNull(),
  // featureCount: int("feature_count").notNull().default(1),
  type: mysqlEnum(snowFeatureTypes).notNull(),
  sections: varchar("sections", { length: 100 }),
  raceId: varchar("race_id", { length: 100 }).notNull(),
});
export type SnowFeature = InferSelectModel<typeof snowFeatures>;

export const snowExecutions = createTable(
  "snow_executions",
  {
    id: int("id").primaryKey().autoincrement(),
    name: varchar("name", { length: 100 }).notNull(),
    isJump: boolean("is_jump").notNull(), //slope rail & slope transition rail false, others true
    active: boolean("active").default(true),
  },
  (table) => [unique().on(table.name, table.isJump)],
);

export const snowExecutionsRelations = relations(
  snowExecutions,
  ({ many }) => ({
    snowSlopeJump: many(snowSlopeJumpTags),
    snowSlopeRail: many(snowSlopeRailTags),
    snowSlopeTransitionJump: many(snowSlopeTransitionJumpTags),
    snowSlopeTransitionRail: many(snowSlopeTransitionRailTags),
    snowHalfPipe: many(snowHalfPipeTags),
    snowBigAir: many(snowBigAirTags),
  }),
);

export const snowTricks = createTable(
  "snow_tricks",
  {
    id: int("id").primaryKey().autoincrement(),
    name: varchar("name", { length: 100 }).notNull(),
    isSnowboard: boolean("is_snowboard").notNull(), // 1 for snowboard, 0 for ski
    active: boolean("active").default(true),
  },
  (table) => [unique().on(table.name, table.isSnowboard)],
);

export const snowTricksRelations = relations(snowTricks, ({ many }) => ({
  snowSlopeRail: many(snowSlopeRailTags),
  snowSlopeTransitionRail: many(snowSlopeTransitionRailTags),
}));

export const snowModifiers = createTable("snow_modifiers", {
  id: int("id").primaryKey().autoincrement(),
  name: varchar("name", { length: 100 }).notNull().unique(),
  active: boolean("active").default(true),
});

export const snowModifiersRelations = relations(snowModifiers, ({ many }) => ({
  snowSlopeJump: many(snowSlopeJumpTags),
  snowSlopeTransitionJump: many(snowSlopeTransitionJumpTags),
  snowHalfPipe: many(snowHalfPipeTags),
  snowBigAir: many(snowBigAirTags),
}));

export const snowRailFeatures = createTable("snow_rail_features", {
  id: int("id").primaryKey().autoincrement(),
  name: varchar("name", { length: 100 }).notNull().unique(),
  active: boolean("active").default(true),
});

export const snowRailFeaturesRelations = relations(
  snowRailFeatures,
  ({ many }) => ({
    snowSlopeRail: many(snowSlopeRailTags),
    snowSlopeTransitionRail: many(snowSlopeTransitionRailTags),
  }),
);

export const snowGrabs = createTable(
  "snow_grabs",
  {
    id: int("id").primaryKey().autoincrement(),
    name: varchar("name", { length: 100 }).notNull(),
    isSnowboard: boolean("is_snowboard").notNull(),
    active: boolean("active").default(true),
  },
  (table) => [unique().on(table.name, table.isSnowboard)],
);

export const snowGrabsRelations = relations(snowGrabs, ({ many }) => ({
  snowSlopeJump: many(snowSlopeJumpTags),
  snowSlopeTransitionJump: many(snowSlopeTransitionJumpTags),
  snowHalfPipe: many(snowHalfPipeTags),
  snowBigAir: many(snowBigAirTags),
}));

export const snowSpinTypes = createTable("snow_spin_types", {
  id: int("id").primaryKey().autoincrement(),
  name: varchar("name", { length: 100 }).notNull().unique(),
  active: boolean("active").default(true),
});

export const snowSpinTypesRelations = relations(snowSpinTypes, ({ many }) => ({
  snowSlopeJump: many(snowSlopeJumpTags),
  snowSlopeTransitionJump: many(snowSlopeTransitionJumpTags),
  snowHalfPipe: many(snowHalfPipeTags),
  snowBigAir: many(snowBigAirTags),
}));

export const snowJumps = createTable(
  "snow_jumps",
  {
    id: int("id").primaryKey().autoincrement(),
    name: varchar("name", { length: 100 }).notNull(),
    isSnowboard: boolean("is_snowboard").notNull(),
    active: boolean("active").default(true),
  },
  (table) => [unique().on(table.name, table.isSnowboard)],
);

export const snowJumpsRelations = relations(snowJumps, ({ many }) => ({
  snowSlopeTransitionJump: many(snowSlopeTransitionJumpTags),
  snowSlopeJump: many(snowSlopeJumpTags),
  snowBigAir: many(snowBigAirTags),
  snowHalfPipe: many(snowHalfPipeTags),
}));

export const snowTransitions = createTable("snow_transitions", {
  id: int("id").primaryKey().autoincrement(),
  name: varchar("name", { length: 100 }).notNull().unique(),
  active: boolean("active").default(true),
});

export const snowTransitionsRelations = relations(
  snowTransitions,
  ({ many }) => ({
    snowSlopeTransition: many(snowSlopeTransition),
  }),
);

export const landingDescriptionEnum = createTable("landing_descriptions", {
  id: int("id").primaryKey().autoincrement(),
  value: mysqlEnum(landingDescriptions).notNull(),
  halfpipeTagId: varchar("halfpipe_tag_id", { length: 255 }),
  bigAirTagId: varchar("big_air_tag_id", { length: 255 }),
  slopeTagId: varchar("slope_tag_id", { length: 255 }),
});

export const landingDescriptionRelations = relations(
  landingDescriptionEnum,
  ({ one }) => ({
    halfpipe: one(snowHalfPipeTags, {
      fields: [landingDescriptionEnum.halfpipeTagId],
      references: [snowHalfPipeTags.id],
    }),
    snowBigAir: one(snowBigAirTags, {
      fields: [landingDescriptionEnum.bigAirTagId],
      references: [snowBigAirTags.id],
    }),
    snowSlopeStyle: one(snowSlopeStyleTags, {
      fields: [landingDescriptionEnum.slopeTagId],
      references: [snowSlopeStyleTags.id],
    }),
  }),
);

export type LandingDescription = InferSelectModel<
  typeof landingDescriptionEnum
>;
