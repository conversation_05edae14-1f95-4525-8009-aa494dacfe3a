"use client";

import { useParams } from "next/navigation";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useHotkeys } from "react-hotkeys-hook";
import { Card } from "~/components/Card";
import { Checkbox } from "~/components/ui/check-box";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Textarea } from "~/components/ui/textarea";
import { useStore } from "~/hooks/store";
import {
  ShotputHand,
  shotputHandOptions,
  ShotputMovement,
  shotputMovementOptions,
  ShotputType,
  shotputTypeOptions,
} from "~/lib/enums/shotput";
import type { ShotputThrows } from "~/server/db/schema";
import { api } from "~/trpc/react";

const radioGroups = [
  { key: "movement", options: shotputMovementOptions },
  { key: "type", options: shotputTypeOptions },
  { key: "hand", options: shotputHandOptions },
];

// Update the ShotputThrowForm to handle the case when a new throw is added
export const ShotputThrowForm = ({ disabled }: { disabled?: boolean }) => {
  const params = useParams();
  const videoId = params.id as string;
  const utils = api.useUtils();

  const setShotputThrow = useStore((state) => state.setShotputThrow);
  const setSelectedAthlete = useStore((state) => state.setSelectedAthlete);
  const shotputThrow = useStore((state) => state.shotputThrow);
  const selectedAthlete = useStore((state) => state.selectedAthlete);

  const { data: throws } = api.shotput.getThrows.useQuery(
    { videoId },
    { enabled: !!videoId },
  );
  const firstThrow = throws?.[0];

  const { mutate: upsertShotputThrow } =
    api.shotput.upsertShotputThrow.useMutation({
      onSuccess: (result) => {
        setShotputThrow({
          ...result,
          id: result.id,
          isToolUsed: result.isToolUsed,
        });

        void utils.shotput.getThrows.invalidate();
      },
    });

  const form = useForm<ShotputThrows>({
    defaultValues: {
      number: firstThrow?.number ?? 1,
      movement: ShotputMovement.Rotation,
      type: ShotputType.Full,
      hand: ShotputHand.RightHand,
      weight: 0,
      comment: "",
      isToolUsed: false,
    },
  });

  useEffect(() => {
    if (shotputThrow) {
      form.setValue("number", shotputThrow.number);
      form.setValue("movement", shotputThrow.movement);
      form.setValue("type", shotputThrow.type);
      form.setValue("hand", shotputThrow.hand);
      form.setValue("comment", shotputThrow.comment);
      form.setValue("weight", shotputThrow.weight ?? 0);
      form.setValue("isToolUsed", shotputThrow.isToolUsed);

      setSelectedAthlete(shotputThrow.athleteId);

      // If the throw has an ID, set it in the form
      if (shotputThrow.id) {
        form.setValue("id", shotputThrow.id);
      }
    }
  }, [shotputThrow, form, setSelectedAthlete]);

  const handleUpsert = () => {
    if (!selectedAthlete) return;
    const formValues = form.getValues();
    upsertShotputThrow({
      ...formValues,
      id: typeof formValues.id === "number" ? formValues.id : undefined,
      videoId,
      athleteId: selectedAthlete,
      weight: formValues.weight ?? 0,
      comment: formValues.comment ?? "",
      isToolUsed: formValues.isToolUsed,
    });
  };

  const onHotkeyPress = (
    key: keyof ShotputThrows,
    value: ShotputMovement | ShotputType | ShotputHand,
  ) => {
    if (!selectedAthlete) return;
    form.setValue(key, value);
    const formValues = form.getValues();
    upsertShotputThrow({
      ...formValues,
      id: typeof formValues.id === "number" ? formValues.id : undefined,
      videoId,
      athleteId: selectedAthlete,
      weight: formValues.weight ?? 0,
      comment: formValues.comment ?? "",
      isToolUsed: formValues.isToolUsed,
      [key]: value,
    });
  };

  useHotkeys("z", () => onHotkeyPress("movement", ShotputMovement.Rotation));
  useHotkeys("x", () => onHotkeyPress("movement", ShotputMovement.Glide));
  useHotkeys("q", () => onHotkeyPress("type", ShotputType.Full));
  useHotkeys("w", () => onHotkeyPress("type", ShotputType.SouthAfrican));
  useHotkeys("e", () => onHotkeyPress("type", ShotputType.DoubleStance));
  useHotkeys("a", () => onHotkeyPress("type", ShotputType.HalfTurn));
  useHotkeys("s", () => onHotkeyPress("type", ShotputType.Standing));
  useHotkeys("d", () => onHotkeyPress("type", ShotputType.StepThrough));
  useHotkeys("c", () => onHotkeyPress("hand", ShotputHand.LeftHand));
  useHotkeys("v", () => onHotkeyPress("hand", ShotputHand.RightHand));

  // In the return statement, update the Select component to use allThrows
  return (
    <Card>
      <Form {...form}>
        <form
          className="space-y-2.5"
          onSubmit={(e) => {
            e.preventDefault();
          }}
        >
          <FormField
            control={form.control}
            name="number"
            render={({ field }) => (
              <FormItem className="grid gap-[5px]">
                <p className="text-smallLabel text-black/60">Throw Number:</p>
                <FormControl className="mt-0 w-full">
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      const throwObj = throws?.find((x) => x.number === +value);
                      if (throwObj) {
                        // Update all form fields with the selected throw's data
                        form.setValue("movement", throwObj.movement);
                        form.setValue("type", throwObj.type);
                        form.setValue("hand", throwObj.hand);
                        form.setValue("isToolUsed", throwObj.isToolUsed);
                        form.setValue("weight", throwObj.weight ?? 0);
                        form.setValue("comment", throwObj.comment);

                        // Set the ID if available
                        if (throwObj.id) {
                          form.setValue("id", throwObj.id);
                        }

                        // Update store immediately when throw number changes
                        setShotputThrow({
                          ...throwObj,
                          id: throwObj.id,
                        });

                        // Update selected athlete if different
                        if (throwObj.athleteId !== selectedAthlete) {
                          setSelectedAthlete(throwObj.athleteId);
                        }
                      }
                    }}
                    value={field.value?.toString()}
                  >
                    <SelectTrigger className="rounded-full py-[7px]">
                      <SelectValue placeholder="Select throw number" />
                    </SelectTrigger>
                    <SelectContent>
                      {throws?.map((x) => (
                        <SelectItem key={x.id} value={x.number.toString()}>
                          Throw {x.number}
                        </SelectItem>
                      ))}
                      {/* Only show current throw if it's not already in the list and it's not throw 1 */}
                      {shotputThrow &&
                        throws &&
                        shotputThrow.number !== 1 &&
                        !throws.some(
                          (t) => t.number === shotputThrow.number,
                        ) && (
                          <SelectItem
                            key="current"
                            value={shotputThrow.number.toString()}
                          >
                            Throw {shotputThrow.number}
                          </SelectItem>
                        )}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {radioGroups.map((group) => (
            <FormField
              key={group.key}
              control={form.control}
              name={group.key as keyof ShotputThrows}
              render={({ field }) => (
                <FormItem className="flex gap-[5px]">
                  <p className="w-[53px] text-smallLabel capitalize text-black/60">
                    {group.key}:
                  </p>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        if (!selectedAthlete) return;
                        const formValues = form.getValues();
                        field.onChange(value);
                        upsertShotputThrow({
                          ...formValues,
                          id:
                            typeof formValues.id === "number"
                              ? formValues.id
                              : undefined,
                          videoId,
                          athleteId: selectedAthlete,
                          weight: formValues.weight ?? 0,
                          comment: formValues.comment ?? "",
                          isToolUsed: formValues.isToolUsed,
                          [group.key]: value,
                        });
                      }}
                      disabled={disabled}
                      value={field.value as string}
                      className="grid flex-1 grid-cols-3 gap-[5px]"
                    >
                      {group.options.map((x) => (
                        <FormItem
                          key={x.value}
                          className="flex items-center gap-0.5"
                        >
                          <RadioGroupItem value={x.value} text={x.key} />
                          <p className="text-[10px] leading-[8px] tracking-[0.28px]">
                            {x.value}
                          </p>
                        </FormItem>
                      ))}
                    </RadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
          ))}

          <FormField
            control={form.control}
            name="weight"
            render={({ field }) => (
              <FormItem className="flex gap-[5px]">
                <p className="w-[53px] text-smallLabel text-black/60">
                  Weight:
                </p>
                <FormControl>
                  <Input
                    type="number"
                    className="h-5 w-full"
                    value={field.value?.toString() ?? "0"}
                    onChange={(e) => {
                      const value = Number.parseInt(e.target.value) || 0;
                      field.onChange(value);
                    }}
                    onFinish={handleUpsert}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Boolean Toggles */}
          <div className="flex gap-5">
            {/* Tool Used Toggle */}
            <FormField
              control={form.control}
              name="isToolUsed"
              render={({ field }) => (
                <FormItem className="flex items-center gap-2">
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <p className="text-smallLabel text-black/60">
                        Tool Used:
                      </p>
                      <Checkbox
                        checked={field.value ?? false}
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                          if (!selectedAthlete) return;
                          const formValues = form.getValues();
                          upsertShotputThrow({
                            ...formValues,
                            id:
                              typeof formValues.id === "number"
                                ? formValues.id
                                : undefined,
                            videoId,
                            athleteId: selectedAthlete,
                            isToolUsed: !!checked,
                            weight: formValues.weight ?? 0,
                            comment: formValues.comment ?? "",
                          });
                        }}
                        disabled={disabled}
                      />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          {/* Comment Textarea */}
          <FormField
            control={form.control}
            name="comment"
            render={({ field }) => (
              <FormItem className="flex flex-col gap-[5px]">
                <p className="text-smallLabel text-black/60">Comment:</p>
                <FormControl>
                  <Textarea
                    className="min-h-[60px] w-full resize-none"
                    value={field.value ?? ""}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                    }}
                    onBlur={handleUpsert}
                    disabled={disabled}
                    placeholder="Add any notes about the throw..."
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </Card>
  );
};
