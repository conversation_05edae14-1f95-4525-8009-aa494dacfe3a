import { DefaultAzureCredential } from "@azure/identity";
import { QueueServiceClient } from "@azure/storage-queue";
import { env } from "~/env";
import { Sport } from "~/lib/enums/enums";

export const addVideoToQueue = async (
  videoId: string,
  targetQueueName: string,
): Promise<void> => {
  try {
    const credential = new DefaultAzureCredential();

    const queueServiceClient = new QueueServiceClient(
      `https://${env.AZURE_STORAGE_ACCOUNT}.queue.core.windows.net/`,
      credential,
    );

    const queueClient = queueServiceClient.getQueueClient(targetQueueName);

    // Encode the message to base64
    const encodedMessage = Buffer.from(videoId, "utf-8").toString("base64");

    // Send the message
    await queueClient.sendMessage(encodedMessage);
  } catch (e) {
    console.error(e);
    throw e;
  }
};

/**
 * Triggers AI processing for a video
 * Environment variables needed:
 * - AZURE_TENANT_ID
 * - AZURE_CLIENT_ID
 * - AZURE_CLIENT_SECRET
 */
export const triggerAI = async (
  videoId: string,
  sport: Sport.swimming | "shotput",
): Promise<void> => {
  // For dev environment, prefix the video name with 'dev-'
  const isProd =
    env.NEXT_PUBLIC_VIDEO_PORTAL_URL === "https://video.goldmine.org.nz";
  const processedVideoId = isProd ? videoId : `dev-${videoId}`;

  const queueName =
    sport === Sport.swimming
      ? "swimming-transfer-videos"
      : "shotput-transfer-videos";

  // Add the video to the queue for processing
  await addVideoToQueue(processedVideoId, queueName);
};
