import { db } from "~/server/db";
import { swimmingTags } from "~/server/db/schema";
import tagsData from "./Tags1.json";

interface Tag {
  videoid: string;
  athleteid: string;
  frame: number;
  tag: string;
  userid: string | null;
  x1: number | null;
  x2: number | null;
  y1: number | null;
  y2: number | null;
}

const typedTags = tagsData as Tag[];

const seedTags = async () => {
  await db.insert(swimmingTags).values(
    typedTags.map((tag) => ({
      videoId: tag.videoid,
      athleteId: tag.athleteid,
      // athleteId: "Anon 4",
      frame: tag.frame,
      tag: tag.tag,
      userId: tag.userid ?? "1",
      x1: tag.x1,
      x2: tag.x2,
      y1: tag.y1,
      y2: tag.y2,
    })),
  );
  console.log("Seeded tags");
};

export const seedSwimming = async () => {
  console.log("Seeding swimming data");
  await seedTags();
};
