"use client";

import { api } from "~/trpc/react";
import toast from "react-hot-toast";
import { EditableSelector } from "~/components/Selector";

export const ExecutionSelect = ({
  isJump,
  value,
  onSelect,
}: {
  isJump: boolean;
  value: string | null;
  onSelect: (value: string) => void;
}) => {
  const utils = api.useUtils();
  const { data: options, refetch } = api.snowOptions.getExecutions.useQuery({
    isJump,
  });

  const { mutate: add, isPending: isPendingAdd } =
    api.snowOptions.addExecution.useMutation({
      onSuccess: () => {
        void refetch();
      },
    });

  const { mutate: remove } = api.snowOptions.deleteExecution.useMutation({
    onError: () => {
      toast.error("Failed to remove jump type");
      void refetch();
    },
  });

  const onAddOption = (value: string) => {
    add({ name: value, isJump });
  };

  const onRemoveOption = (value: string) => {
    remove({ id: parseInt(value) });
    utils.snowOptions.getExecutions.setData({ isJump }, (prev) => {
      return prev?.filter((x) => x.value !== value);
    });
  };

  return (
    <EditableSelector
      placeholder="<Optional>"
      options={options ?? []}
      onAdd={onAddOption}
      onRemove={onRemoveOption}
      value={value}
      onSelect={onSelect}
      isPendingAdd={isPendingAdd}
    />
  );
};
