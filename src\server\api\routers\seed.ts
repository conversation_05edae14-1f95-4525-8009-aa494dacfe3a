import { seedSwimming } from "~/seed/swimming";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { updateVideoStatus } from "../utils/video";
import { seedSnow } from "~/seed/snowsport";

export const seedRouter = createTRPCRouter({
  revertDemoVideoStatus: protectedProcedure.mutation(async () => {
    const videoIds = [
      "kty9prj7c05g36kk78nxjn9z",
      "v7ghrzr5hkrfsrp204x0u4y2",
      "l2932mkpeltaz68untpek0ly",
      "vsjqgwjkj25avis6zdqrd290",
      "ll9ng3ebgn63muqj37gzk8fo",
    ];
    await Promise.all(
      videoIds.map((id) => updateVideoStatus(id, "Tagged_by_AI")),
    );
  }),
  swimming: protectedProcedure.mutation(async () => {
    await seedSwimming();
  }),
  longvideo: protectedProcedure.mutation(async () => {
    await seedSnow();
  }),
});
