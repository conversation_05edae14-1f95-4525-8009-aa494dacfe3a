import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "~/lib/utils";
import { Loading } from "~/app/_components/Loading";

const buttonVariants = cva(
  "flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          "bg-white rounded-full border border-black disabled:pointer-events-none text-black shadow hover:bg-black/10  disabled:text-black/60 disabled:bg-none disabled:border-black/60",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline:
          "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        search: "bg-white border border-seaSalt-k40 rounded-full font-normal",
        black:
          "bg-black rounded-full disabled:pointer-events-none text-white shadow hover:bg-black/60 disabled:text-black/30 disabled:bg-black/10 active:bg-black/80 active:text-white",
        red: "bg-red rounded-full disabled:pointer-events-none text-white shadow hover:bg-red/60 disabled:text-black/30 disabled:bg-black/10 active:bg-red/80 active:text-white",
      },
      size: {
        default: "h-[38px] px-5 py-2.5",
        xs: "px-2.5 text-smallLabel py-[3px]",
        sm: "h-8 px-3 text-xs",
        lg: "h-10 px-8",
        icon: "p-1",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    { className, variant, size, asChild = false, loading, children, ...props },
    ref,
  ) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={loading}
        type="button"
        {...props}
      >
        <div className="flex flex-1 items-center justify-center gap-3 rounded-full">
          {children}
          {loading && <Loading />}
        </div>
      </Comp>
    );
  },
);
Button.displayName = "Button";

export { Button, buttonVariants };
