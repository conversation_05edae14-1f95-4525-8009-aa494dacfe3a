"use client";

import { useParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { Card } from "~/components/Card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { useStore } from "~/hooks/store";
import {
  DiscusHand,
  DiscusMovement,
  discusMovementOptions,
  DiscusType,
  discusTypeOptions,
  discusHandOptions,
} from "~/lib/enums/discus";
import { api } from "~/trpc/react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "~/components/ui/form";
import { useEffect, useRef } from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { Checkbox } from "../ui/check-box";
import { Input } from "../ui/input";
import type { DiscusThrows } from "~/server/db/discusSchema";

const radioGroups = [
  { key: "movement", options: discusMovementOptions },
  { key: "type", options: discusTypeOptions },
  { key: "hand", options: discusHandOptions },
];

export const DiscusThrowForm = ({ disabled }: { disabled?: boolean }) => {
  const params = useParams();
  const videoId = params.id as string;
  const utils = api.useUtils();

  const setDiscusThrow = useStore((state) => state.setDiscusThrow);
  const setSelectedAthlete = useStore((state) => state.setSelectedAthlete);
  const discusThrow = useStore((state) => state.discusThrow);
  const selectedAthlete = useStore((state) => state.selectedAthlete);

  const { data: throws } = api.discus.getThrows.useQuery(
    { videoId },
    { enabled: !!videoId },
  );

  const firstThrow = throws?.[0];

  const { mutate: upsertDiscusThrow } =
    api.discus.upsertDiscusThrow.useMutation({
      onSuccess: (result) => {
        setDiscusThrow({
          ...result,
          id: result.id,
        });

        void utils.discus.getThrows.invalidate();
      },
    });

  const form = useForm<DiscusThrows>({
    defaultValues: {
      number: firstThrow?.number ?? 1,
      movement: DiscusMovement.Rotation,
      type: DiscusType.Full,
      hand: DiscusHand.RightHand,
      weight: 0,
      nonReverse: false,
      denfyTool: false,
    },
  });

  useEffect(() => {
    if (discusThrow) {
      form.setValue("number", discusThrow.number);
      form.setValue("movement", discusThrow.movement);
      form.setValue("type", discusThrow.type);
      form.setValue("hand", discusThrow.hand);
      form.setValue("nonReverse", discusThrow.nonReverse);
      form.setValue("weight", discusThrow.weight ?? 0);
      form.setValue("denfyTool", discusThrow.denfyTool);

      setSelectedAthlete(discusThrow.athleteId);

      // If the throw has an ID, set it in the form
      if (discusThrow.id) {
        form.setValue("id", discusThrow.id);
      }
    }
  }, [discusThrow, form, setSelectedAthlete]);

  const handleUpsert = () => {
    if (!selectedAthlete) return;
    const formValues = form.getValues();
    upsertDiscusThrow({
      ...formValues,
      id: typeof formValues.id === "number" ? formValues.id : undefined,
      videoId,
      athleteId: selectedAthlete,
      weight: formValues.weight ?? 0,
      nonReverse: formValues.nonReverse,
      denfyTool: formValues.denfyTool,
    });
  };

  const onHotkeyPress = (
    key: keyof DiscusThrows,
    value: DiscusMovement | DiscusType | DiscusHand,
  ) => {
    if (!selectedAthlete) return;
    const formValues = form.getValues();
    upsertDiscusThrow({
      ...formValues,
      id: typeof formValues.id === "number" ? formValues.id : undefined,
      videoId,
      athleteId: selectedAthlete,
      weight: formValues.weight ?? 0,
      [key]: value,
    });
  };

  useHotkeys("z", () => onHotkeyPress("movement", DiscusMovement.Rotation));
  useHotkeys("x", () => onHotkeyPress("movement", DiscusMovement.Glide));

  useHotkeys("q", () => onHotkeyPress("type", DiscusType.Full));
  useHotkeys("w", () => onHotkeyPress("type", DiscusType.Standing));
  useHotkeys("e", () => onHotkeyPress("type", DiscusType.Half));
  useHotkeys("a", () => onHotkeyPress("type", DiscusType.SouthAfrican));
  useHotkeys("s", () => onHotkeyPress("type", DiscusType.StepIn));
  useHotkeys("d", () => onHotkeyPress("type", DiscusType.FullWalking));

  useHotkeys("c", () => onHotkeyPress("hand", DiscusHand.LeftHand));
  useHotkeys("v", () => onHotkeyPress("hand", DiscusHand.RightHand));

  return (
    <Card>
      <Form {...form}>
        <form
          className="space-y-2.5"
          onSubmit={(e) => {
            e.preventDefault();
          }}
        >
          <FormField
            control={form.control}
            name="number"
            render={({ field }) => (
              <FormItem className="grid gap-[5px]">
                <p className="text-smallLabel text-black/60">Throw Number:</p>
                <FormControl className="mt-0 w-full">
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      const throwObj = throws?.find((x) => x.number === +value);
                      if (throwObj) {
                        // Update all form fields with the selected throw's data
                        form.setValue("movement", throwObj.movement);
                        form.setValue("type", throwObj.type);
                        form.setValue("hand", throwObj.hand);
                        form.setValue("nonReverse", throwObj.nonReverse);
                        form.setValue("weight", throwObj.weight ?? 0);
                        form.setValue("denfyTool", throwObj.denfyTool);

                        // Set the ID if available
                        if (throwObj.id) {
                          form.setValue("id", throwObj.id);
                        }

                        // Update store immediately when throw number changes
                        setDiscusThrow({
                          ...throwObj,
                          id: throwObj.id,
                        });

                        // Update selected athlete if different
                        if (throwObj.athleteId !== selectedAthlete) {
                          setSelectedAthlete(throwObj.athleteId);
                        }
                      }
                    }}
                    value={field.value?.toString()}
                  >
                    <SelectTrigger className="rounded-full py-[7px]">
                      <SelectValue placeholder="Select throw number" />
                    </SelectTrigger>
                    <SelectContent>
                      {throws?.map((x) => (
                        <SelectItem key={x.id} value={x.number.toString()}>
                          Throw {x.number}
                        </SelectItem>
                      ))}
                      {/* Only show current throw if it's not already in the list and it's not throw 1 */}
                      {discusThrow &&
                        throws &&
                        discusThrow.number !== 1 &&
                        !throws.some(
                          (t) => t.number === discusThrow.number,
                        ) && (
                          <SelectItem
                            key="current"
                            value={discusThrow.number.toString()}
                          >
                            Throw {discusThrow.number}
                          </SelectItem>
                        )}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {radioGroups.map((group) => (
            <FormField
              key={group.key}
              control={form.control}
              name={group.key as keyof DiscusThrows}
              render={({ field }) => (
                <FormItem className="flex gap-[5px]">
                  <p className="w-[53px] text-smallLabel capitalize text-black/60">
                    {group.key}:
                  </p>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        if (!selectedAthlete) return;
                        const formValues = form.getValues();

                        field.onChange(value);
                        upsertDiscusThrow({
                          ...formValues,
                          id:
                            typeof formValues.id === "number"
                              ? formValues.id
                              : undefined,
                          videoId,
                          athleteId: selectedAthlete,
                          weight: formValues.weight ?? 0,
                          [group.key]: value,
                        });
                      }}
                      disabled={disabled}
                      value={field.value as string}
                      className="grid flex-1 grid-cols-3 gap-[5px]"
                    >
                      {group.options.map((x) => (
                        <FormItem
                          key={x.value}
                          className="flex items-center gap-0.5"
                        >
                          <RadioGroupItem value={x.value} text={x.key} />
                          <p className="text-[10px] leading-[8px] tracking-[0.28px]">
                            {x.value}
                          </p>
                        </FormItem>
                      ))}
                    </RadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
          ))}

          {/* Weight Input Field */}
          <FormField
            control={form.control}
            name="weight"
            render={({ field }) => (
              <FormItem className="flex gap-[5px]">
                <p className="w-[53px] text-smallLabel text-black/60">
                  Weight:
                </p>
                <FormControl>
                  <Input
                    type="number"
                    className="h-5 w-full"
                    value={field.value?.toString() ?? "0"}
                    onChange={(e) => {
                      const value = Number.parseInt(e.target.value) || 0;
                      field.onChange(value);
                    }}
                    onBlur={handleUpsert}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Boolean Toggles */}
          <div className="flex gap-5">
            {/* Non-Reverse Toggle */}
            <FormField
              control={form.control}
              name="nonReverse"
              render={({ field }) => (
                <FormItem className="flex items-center gap-2">
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <p className="text-smallLabel text-black/60">
                        Non-Reverse:
                      </p>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                          if (!selectedAthlete) return;
                          const formValues = form.getValues();

                          upsertDiscusThrow({
                            ...formValues,
                            id:
                              typeof formValues.id === "number"
                                ? formValues.id
                                : undefined,
                            videoId,
                            athleteId: selectedAthlete,
                            nonReverse: !!checked,
                            weight: formValues.weight ?? 0,
                            denfyTool: formValues.denfyTool,
                          });
                        }}
                        disabled={disabled}
                      />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Denfy Tool Toggle */}
            <FormField
              control={form.control}
              name="denfyTool"
              render={({ field }) => (
                <FormItem className="flex items-center gap-2">
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <p className="text-smallLabel text-black/60">
                        Denfy Tool:
                      </p>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                          if (!selectedAthlete) return;
                          const formValues = form.getValues();

                          upsertDiscusThrow({
                            ...formValues,
                            id:
                              typeof formValues.id === "number"
                                ? formValues.id
                                : undefined,
                            videoId,
                            athleteId: selectedAthlete,
                            nonReverse: formValues.nonReverse,
                            weight: formValues.weight ?? 0,
                            denfyTool: !!checked,
                          });
                        }}
                        disabled={disabled}
                      />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </form>
      </Form>
    </Card>
  );
};
