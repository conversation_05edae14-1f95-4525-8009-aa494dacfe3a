import { Client } from "@planetscale/database";
import { drizzle } from "drizzle-orm/planetscale-serverless";

import { env } from "~/env";
import * as schema from "./schema";
import * as snowSchema from "./snowSchema";
import * as highJumpSchema from "./highJumpSchema";
import * as boxingSchema from "./boxingSchema";
import * as discusSchema from "./discusSchema";
import * as sprintSchema from "./sprintSchema";

export const db = drizzle(new Client({ url: env.DATABASE_URL }), {
  schema: {
    ...schema,
    ...snowSchema,
    ...highJumpSchema,
    ...boxingSchema,
    ...discusSchema,
    ...sprintSchema,
  },
});

export type Transaction = Parameters<
  Parameters<(typeof db)["transaction"]>[0]
>[0];
