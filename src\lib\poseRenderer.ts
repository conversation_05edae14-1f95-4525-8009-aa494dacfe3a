import { poseConnections } from "~/lib/constants";
import { angleAnchors } from "./interfaces/drawingTypes";
import type {
  PartialBodyAngles,
  PartialBodyKeypoints,
} from "./interfaces/drawingTypes";

export function drawPoseOverlay(
  ctx: CanvasRenderingContext2D,
  keypoints: PartialBodyKeypoints,
  angles: PartialBodyAngles,
  displayWidth: number,
  displayHeight: number,
  displayScaleX: number,
  displayScaleY: number,
  videoWidth: number,
  videoHeight: number,
  poseCircleThickness: number,
  poseLineThickness: number,
) {
  if (keypoints.length === 0) return;

  ctx.strokeStyle = "rgb(9, 48, 239)";
  ctx.lineWidth = poseLineThickness / Math.min(displayScaleX, displayScaleY);

  // Draw skeleton lines
  drawSkeletonLines(
    ctx,
    keypoints,
    displayWidth,
    displayHeight,
    videoWidth,
    videoHeight,
    displayScaleX,
    displayScaleY,
    poseLineThickness,
  );

  // Draw keypoints
  drawKeypoints(
    ctx,
    keypoints,
    displayWidth,
    displayHeight,
    videoWidth,
    videoHeight,
    displayScaleX,
    displayScaleY,
    poseCircleThickness,
  );

  // Draw angle annotations
  drawAngleAnnotations(
    ctx,
    keypoints,
    angles,
    displayWidth,
    displayHeight,
    videoWidth,
    videoHeight,
    displayScaleX,
    displayScaleY,
    poseCircleThickness,
  );
}

function drawSkeletonLines(
  ctx: CanvasRenderingContext2D,
  keypoints: PartialBodyKeypoints,
  displayWidth: number,
  displayHeight: number,
  videoWidth: number,
  videoHeight: number,
  displayScaleX: number,
  displayScaleY: number,
  poseLineThickness: number,
) {
  poseConnections.forEach(([fromIdx, toIdx]) => {
    const fromKeypoint = keypoints.find((x) => x.keypointNum === fromIdx);
    const toKeypoint = keypoints.find((x) => x.keypointNum === toIdx);

    if (fromKeypoint && toKeypoint) {
      let fromX, fromY, toX, toY;

      if (fromKeypoint.x <= 1 && fromKeypoint.y <= 1) {
        fromX = fromKeypoint.x * displayWidth;
        fromY = fromKeypoint.y * displayHeight;
      } else {
        const scaleToDisplayX = displayWidth / videoWidth;
        const scaleToDisplayY = displayHeight / videoHeight;
        fromX = fromKeypoint.x * scaleToDisplayX;
        fromY = fromKeypoint.y * scaleToDisplayY;
      }

      if (toKeypoint.x <= 1 && toKeypoint.y <= 1) {
        toX = toKeypoint.x * displayWidth;
        toY = toKeypoint.y * displayHeight;
      } else {
        const scaleToDisplayX = displayWidth / videoWidth;
        const scaleToDisplayY = displayHeight / videoHeight;
        toX = toKeypoint.x * scaleToDisplayX;
        toY = toKeypoint.y * scaleToDisplayY;
      }

      ctx.beginPath();
      ctx.moveTo(fromX, fromY);
      ctx.lineTo(toX, toY);
      ctx.stroke();
    }
  });
}

function drawKeypoints(
  ctx: CanvasRenderingContext2D,
  keypoints: PartialBodyKeypoints,
  displayWidth: number,
  displayHeight: number,
  videoWidth: number,
  videoHeight: number,
  displayScaleX: number,
  displayScaleY: number,
  poseCircleThickness: number,
) {
  // Red circles at all keypoints
  const keypointNumsToDraw = new Set<number>();
  poseConnections.forEach(([fromIdx, toIdx]) => {
    if (fromIdx !== undefined) keypointNumsToDraw.add(fromIdx);
    if (toIdx !== undefined) keypointNumsToDraw.add(toIdx);
  });

  keypointNumsToDraw.forEach((num) => {
    const keypoint = keypoints.find((k) => k.keypointNum === num);
    if (!keypoint) return;

    let x, y;
    if (keypoint.x <= 1 && keypoint.y <= 1) {
      x = keypoint.x * displayWidth;
      y = keypoint.y * displayHeight;
    } else {
      const scaleToDisplayX = displayWidth / videoWidth;
      const scaleToDisplayY = displayHeight / videoHeight;
      x = keypoint.x * scaleToDisplayX;
      y = keypoint.y * scaleToDisplayY;
    }

    const circleRadius =
      poseCircleThickness / Math.min(displayScaleX, displayScaleY);
    ctx.beginPath();
    ctx.arc(x, y, circleRadius, 0, 2 * Math.PI);
    ctx.fillStyle = "rgb(252, 1, 2)";
    ctx.fill();
  });
}

function drawAngleAnnotations(
  ctx: CanvasRenderingContext2D,
  keypoints: PartialBodyKeypoints,
  angles: PartialBodyAngles,
  displayWidth: number,
  displayHeight: number,
  videoWidth: number,
  videoHeight: number,
  displayScaleX: number,
  displayScaleY: number,
  poseCircleThickness: number,
) {
  // Angle anchor keypoints
  angles.forEach((angle) => {
    const anchorKeypointNum = angleAnchors[angle.name];
    if (anchorKeypointNum === undefined) return;

    const keypoint = keypoints.find((k) => k.keypointNum === anchorKeypointNum);
    if (!keypoint) return;

    let x, y;
    if (keypoint.x <= 1 && keypoint.y <= 1) {
      x = keypoint.x * displayWidth;
      y = keypoint.y * displayHeight;
    } else {
      const scaleToDisplayX = displayWidth / videoWidth;
      const scaleToDisplayY = displayHeight / videoHeight;
      x = keypoint.x * scaleToDisplayX;
      y = keypoint.y * scaleToDisplayY;
    }

    const circleRadius =
      poseCircleThickness / Math.min(displayScaleX, displayScaleY);
    const fontSize = 12 / Math.min(displayScaleX, displayScaleY);
    ctx.font = `${fontSize}px Arial`;
    ctx.fillStyle = "yellow";
    ctx.fillText(`${angle.angle.toFixed(0)}`, x + circleRadius + 2, y);
  });
}
