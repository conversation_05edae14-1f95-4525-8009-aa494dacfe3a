"use client";

import { useParams } from "next/navigation";
import { api } from "~/trpc/react";
import { useStore } from "~/hooks/store";
import toast from "react-hot-toast";
import type { SwimmingTag } from "~/server/db/schema";
import { useSession } from "next-auth/react";
import { swimmingTags } from "~/lib/enums/swimming";
import { Sport, TagAction } from "~/lib/enums/enums";
import { checkIsAiTag, getFilteredTags } from "~/lib/utils";
import { useEffect, useMemo } from "react";
import type { TagChange } from "~/lib/interface";

export const useSwimmingTags = () => {
  const { data: session } = useSession();

  const params = useParams<{ id: string }>();
  const videoId = params.id;
  const utils = api.useUtils();

  const tagFilterValues = useStore((state) => state.tagFilterValues);
  const currentFrame = useStore((state) => state.currentFrame);
  const editingTagId = useStore((state) => state.editingTagId);
  const filteredTags = useStore((state) => state.filteredTags);
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const tagSortByAiConfidence = useStore(
    (state) => state.tagSortByAiConfidence,
  );
  const setEditingTagId = useStore((state) => state.setEditingTagId);
  const setFilteredTags = useStore((state) => state.setFilteredTags);
  const setTagChanges = useStore((state) => state.setTagChanges);

  const { data: tags, isLoading } = api.swimming.getVideoTags.useQuery(
    { id: videoId },
    { enabled: !!videoId },
  );

  const newFilteredTags = useMemo(
    () =>
      getFilteredTags({
        sport: Sport.swimming,
        athleteId: selectedAthlete,
        tags: tags?.tags ?? [],
        tagTypes: swimmingTags,
        filterValues: tagFilterValues,
        sortByAiConfidence: tagSortByAiConfidence,
      }),
    [selectedAthlete, tags?.tags, tagFilterValues, tagSortByAiConfidence],
  );

  useEffect(() => {
    setFilteredTags(newFilteredTags);
  }, [newFilteredTags, setFilteredTags]);

  const { mutate: upsertTag } = api.swimming.upsertTag.useMutation({
    onSuccess: (data) => {
      if (editingTagId === data.id) {
        setEditingTagId(null);
      }
    },
    onError: (error) => {
      console.error(error);
      toast.error("Failed to add tag");
      void utils.swimming.getVideoTags.invalidate({ id: videoId });
    },
  });

  const onAddTagToState = (tag: SwimmingTag) => {
    utils.swimming.getVideoTags.setData({ id: videoId }, (data) => {
      if (!data) return { tags: [] };
      if (tag.aiTagId) {
        const existingHumanTag = data.tags.find(
          (x) => x.aiTagId === tag.aiTagId,
        );
        if (existingHumanTag) {
          return {
            tags: data.tags.map((x) => (x.aiTagId === tag.aiTagId ? tag : x)),
          };
        }
      }
      return { tags: [...data.tags, tag] };
    });
  };

  const onAddTag = (tag: string) => {
    if (!selectedAthlete) return;
    //don't allow same tag on same frame for same athlete
    const existingTag = tags?.tags.find(
      (x) =>
        x.frame === currentFrame &&
        x.tag === tag &&
        x.athleteId === selectedAthlete,
    );
    if (existingTag) return;
    const tagId = crypto.randomUUID();

    let aiTagId: string | null = null;
    if (editingTagId) {
      console.log("editingTagId", editingTagId);
      const editingTag = filteredTags.find((x) => x.id === editingTagId);
      console.log("editingTag", editingTag);
      if (editingTag) {
        aiTagId = editingTag.aiTagId ?? editingTag.id;
      }
    }
    const obj: SwimmingTag = {
      id: tagId,
      videoId,
      athleteId: selectedAthlete,
      frame: currentFrame,
      tag,
      aiTagId,
      userId: session!.user.id,
      aiConfidence: null,
      repId: null,
      isDeleted: false,
      dateCreated: new Date(),
      x1: null,
      x2: null,
      y1: null,
      y2: null,
    };
    onAddTagToState(obj);
    upsertTag(obj);
  };

  const { mutate: deleteTag } = api.swimming.deleteTag.useMutation({
    onError: (error) => {
      console.error(error);
      toast.error("Failed to delete tag");
      void utils.swimming.getVideoTags.invalidate({ id: videoId });
    },
  });

  const { mutate: deleteTags } = api.swimming.deleteTags.useMutation({
    onError: (error) => {
      console.error(error);
      toast.error("Failed to delete tags");
      void utils.swimming.getVideoTags.invalidate({ id: videoId });
    },
  });

  const onDeleteTag = (id: string) => {
    const tag = tags?.tags.find((x) => x.id === id);
    if (!tag) return;

    const isAITag = checkIsAiTag(tag.userId);

    utils.swimming.getVideoTags.setData({ id: videoId }, (data) => {
      if (!data) return { tags: [] };
      return { tags: data.tags.filter((x) => x.id !== id) };
    });

    if (isAITag) {
      deleteTag({ id, softDelete: true });
      return;
    }
    deleteTag({ id });
  };

  const onDeleteTags = (ids: string[]) => {
    const softDeleteIds: string[] = [];
    const hardDeleteIds: string[] = [];
    for (const id of ids) {
      const tag = tags?.tags.find((x) => x.id === id);
      if (!tag) continue;
      if (checkIsAiTag(tag.userId)) {
        hardDeleteIds.push(id);
      } else {
        softDeleteIds.push(id);
      }
    }
    utils.swimming.getVideoTags.setData({ id: videoId }, (data) => {
      if (!data) return { tags: [] };
      return { tags: data.tags.filter((x) => !ids.includes(x.id)) };
    });
    deleteTags({ softDeleteIds, hardDeleteIds });
  };

  //get ai tag changes

  const athleteTags =
    tags?.tags.filter((x) => x.athleteId === selectedAthlete) ?? [];
  const humanTags = athleteTags.filter((x) => !checkIsAiTag(x.userId));

  const removedAITags: TagChange[] = athleteTags
    .filter((x) => x.isDeleted)
    .map((x) => ({
      tagId: x.id,
      athleteId: [x.athleteId],
      action: TagAction.remove,
      tagType: [x.tag],
      frame: [x.frame],
    }));

  const humanAddedTags: TagChange[] = humanTags
    .filter((x) => !x.aiTagId)
    .map((x) => ({
      tagId: x.id,
      athleteId: [x.athleteId],
      action: TagAction.add,
      tagType: [x.tag],
      frame: [x.frame],
    }));

  const humanUpdatedTags: TagChange[] = humanTags
    .filter((x) => !!x.aiTagId)
    .map((tag) => {
      const aiTag = athleteTags.find((x) => x.id === tag.aiTagId);
      return {
        tagId: tag.id,
        athleteId: [tag.athleteId],
        action: TagAction.update,
        tagType: [aiTag?.tag ?? "", tag.tag],
        frame: [aiTag?.frame ?? 0, tag.frame],
      };
    });

  useEffect(() => {
    setTagChanges({
      humanAddedTags,
      humanUpdatedTags,
      removedAITags,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [humanAddedTags, humanUpdatedTags, removedAITags]);

  return {
    tags,
    isLoading,
    onAddTag,
    onDeleteTag,
    onDeleteTags,
  };
};
