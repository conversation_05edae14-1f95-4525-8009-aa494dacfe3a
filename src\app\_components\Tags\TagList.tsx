import { useEffect, useRef } from "react";
import { TagItem } from "~/components/TagItem";
import type { TaglistTag, TagUI } from "~/lib/interface";

export const TagList = ({
  tags,
  disabled,
  editingTagId,
  height,
  tagOptions,
  onTagClick,
  onRemoveTag,
  setEditingTagId,
}: {
  tags: TaglistTag[];
  disabled?: boolean;
  editingTagId: string | null;
  height?: number;
  tagOptions: TagUI[];
  onTagClick: (frame: string) => void;
  onRemoveTag: (tagId: string) => void;
  setEditingTagId: (tagId: string | null) => void;
}) => {
  const listRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (editingTagId && listRef.current) {
      const tagElement = document.getElementById(editingTagId);

      if (tagElement) {
        tagElement.scrollIntoView({ behavior: "smooth", block: "nearest" });
      }
    }
  }, [editingTagId]);

  return (
    <div
      ref={listRef}
      className="flex flex-col gap-[5px] overflow-y-auto p-2.5"
      style={{ height }}
    >
      {tags.map((tag) => {
        const isEditing = editingTagId === tag.id;

        const className = tagOptions.find(
          (x) => x?.value.toString() === tag.tag,
        )?.className;

        return (
          <TagItem
            id={tag.id}
            key={tag.id}
            className={
              isEditing
                ? "bg-white text-black/60 outline outline-2 outline-offset-1 outline-seaSalt-k40"
                : className
            }
            onClick={() => onTagClick(tag.frame.toString())}
            frame={tag.frame}
            tag={tag.tag}
            onEdit={() => {
              if (disabled) return;
              setEditingTagId(editingTagId === tag.id ? null : tag.id);
              onTagClick(tag.frame.toString());
            }}
            onDelete={() => {
              if (disabled) return;
              onRemoveTag(tag.id);
            }}
            userId={tag.userId}
            aiConfidence={tag.aiConfidence}
          />
        );
      })}
    </div>
  );
};
