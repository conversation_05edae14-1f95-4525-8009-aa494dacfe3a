import { eq, inArray } from "drizzle-orm";
import { z } from "zod";
import { DiscusHand, DiscusMovement, DiscusType } from "~/lib/enums/discus";
import { discusTags, discusThrows } from "~/server/db/discusSchema";
import type { RouterOutputs } from "~/trpc/react";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import type { TaglistTag } from "~/lib/interface";

type Outputs = RouterOutputs["discus"];

export type GetDiscusTagsOutput = Outputs["getTags"];

export const discusRouter = createTRPCRouter({
  getTags: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const throws = await ctx.db.query.discusThrows.findMany({
        where: eq(discusThrows.videoId, input.id),
        with: {
          phases: true,
        },
      });

      const formatTags: TaglistTag[] = [];
      throws.forEach((t) => {
        t.phases.forEach((p) => {
          formatTags.push({
            id: p.id.toString(),
            throwId: p.throwId ?? 1,
            tag: p.tag,
            frame: p.frame,
            userId: p.userId,
            isDeleted: p.isDeleted,
            athleteId: t.athleteId,
          });
        });
      });

      return {
        throws,
        tags: formatTags,
      };
    }),

  upsertDiscusThrow: protectedProcedure
    .input(
      z.object({
        id: z.number().optional(),
        videoId: z.string(),
        athleteId: z.string(),
        startFrame: z.number().optional(),
        endFrame: z.number().optional(),
        number: z.number(),
        movement: z.nativeEnum(DiscusMovement),
        type: z.nativeEnum(DiscusType),
        hand: z.nativeEnum(DiscusHand),
        nonReverse: z.boolean(),
        weight: z.number(),
        denfyTool: z.boolean(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const obj = {
        id: input.id,
        videoId: input.videoId,
        athleteId: input.athleteId,
        startFrame: input.startFrame,
        endFrame: input.endFrame,
        number: input.number,
        movement: input.movement,
        type: input.type,
        hand: input.hand,
        nonReverse: input.nonReverse,
        weight: input.weight,
        denfyTool: input.denfyTool,
        userId: ctx.session.user.id,
        dateCreated: new Date(),
      };
      const id = await ctx.db
        .insert(discusThrows)
        .values(obj)
        .onDuplicateKeyUpdate({
          set: obj,
        })
        .$returningId();
      return { ...obj, id: input.id ?? id[0]!.id };
    }),

  upsertTag: protectedProcedure
    .input(
      z.object({
        id: z.number().optional(),
        throwId: z.number(),
        tag: z.string(),
        frame: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .insert(discusTags)
        .values({
          ...input,
          userId: ctx.session.user.id,
        })
        .onDuplicateKeyUpdate({
          set: {
            frame: input.frame,
            userId: ctx.session.user.id,
            isDeleted: false,
          },
        })
        .$returningId();
    }),
  getThrows: protectedProcedure
    .input(z.object({ videoId: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.db.query.discusThrows.findMany({
        where: (throws, { eq }) => eq(throws.videoId, input.videoId),
        orderBy: (throws, { asc }) => [asc(throws.number)],
      });
    }),
  deleteTag: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db
        .update(discusTags)
        .set({
          isDeleted: true,
        })
        .where(eq(discusTags.id, input.id));
    }),

  deleteTags: protectedProcedure
    .input(z.object({ ids: z.array(z.number()) }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db
        .update(discusTags)
        .set({ isDeleted: true })
        .where(inArray(discusTags.id, input.ids));
    }),
});
