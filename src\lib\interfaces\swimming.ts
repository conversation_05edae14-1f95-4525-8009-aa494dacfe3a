import type { Gender } from "../enums/enums";
import type {
  SwimCourseType,
  SwimmingSource,
  SwimmingStartType,
  SwimmingStrokeCategory,
} from "../enums/swimming";
import type { PTAAthlete } from "./externalCall";

interface RelayTeam {
  team_id?: string;
  athlete_id: string;
  leg?: number;
  let_number?: number;
}

export interface SwimmingRace {
  race_id: string;
  competition_id: string;
  course_type: SwimCourseType;
  date: string; //yyyy-mm-dd
  start_time: string; //yyyy-mm-ddThh:mm:ssZ
  round: string;
  stroke_category: SwimmingStrokeCategory;
  classification: string | null;
  race_distance: number;
  gender: Gender;
  age_category: string | null;
  is_relay: boolean;
}

export interface SwimmingSessionInput {
  session_id: string;
  athlete_id: string | null;
  athlete: PTAAthlete | null;
  relay_team_id: string | null;
  relay_team: RelayTeam[] | null;
  date: string; //yyyy-mm-dd
  start_time: string | null;
  session_type: string;
  session_description: string;
  course_type: SwimCourseType;
  race_id: string | null;
  placing: number | null;
  official_time: number | null;
  result_code: string | null;
  reps: SwimmingRep[] | null;
}
//origin race athlete
export interface SwimmingSession {
  session_id: string;
  athlete_id: string | null;
  athlete: PTAAthlete | null;
  relay_team_id: string | null;
  is_relay: boolean;
  relay_team: {
    team_id: string;
    name: string;
    country: string;
    athletes: { athlete: PTAAthlete; athlete_id: string; leg_number: number }[];
  } | null;
  date: string; //yyyy-mm-dd
  start_time: string | null;
  session_type: string;
  session_description: string;
  course_type: SwimCourseType;
  race_id: string | null;
  placing: number | null;
  official_time: number | null;
  result_code: string | null;
  reps: SwimmingRep[] | null;
}

//origin swimming splits
export interface SwimmingRep {
  id?: string;
  session_id: string;
  athlete_id: string;
  athlete_name: string;
  stroke_category: SwimmingStrokeCategory;
  start_type: SwimmingStartType;
  source: SwimmingSource;
  race_suit: boolean;
  piece_number: number;
  set_number: number;
  rep_number: number;
  lane_number?: number | null;
  video_id: string;
  duration: number;
  distance: number;
  speed: number;
  stroke_rate?: number | null;
  max_stroke_rate?: number | null;
  distance_per_stroke?: number | null;
  stroke_count?: number | null;
  breath_count?: number | null;
  start?: {
    stroke_type: string;
    reaction_time: number;
  } | null;
  laps?: SwimmingLap[];
}

export interface SwimmingLap {
  rep_id?: string;
  lap_number: number;
  stroke_type: SwimmingStrokeCategory;
  lap_distance: number;
  distance: number;
  lap_duration: number;
  duration: number;
  speed: number;
  stroke_rate?: number | null;
  max_stroke_rate?: number | null;
  distance_per_stroke?: number | null;
  stroke_count?: number | null;
  breath_count?: number | null;
  // splits: any[];
  // strokes: any[];
}
