"use client";

import * as React from "react";
import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { Check } from "lucide-react";

import { cn } from "~/lib/utils";

interface CheckboxProps
  extends React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> {
  text?: string;
  bg?: string;
}

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  CheckboxProps
>(({ className, text, bg, ...props }, ref) => (
  <CheckboxPrimitive.Root
    ref={ref}
    className={cn(
      "peer relative h-5 w-5 shrink-0 rounded-sm border border-black/10 bg-seaSalt-40 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:text-white",
      className,
    )}
    {...props}
  >
    <p className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-smallLabel uppercase">
      {text}
    </p>
    <CheckboxPrimitive.Indicator
      className={cn("flex items-center justify-center")}
    >
      {text && (
        <div className={cn("h-4 w-4 rounded-[2.5px] bg-seaSalt-k40", bg)}></div>
      )}
      {!text && <Check className="h-4 w-4 text-black" />}
    </CheckboxPrimitive.Indicator>
  </CheckboxPrimitive.Root>
));
Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export { Checkbox };
