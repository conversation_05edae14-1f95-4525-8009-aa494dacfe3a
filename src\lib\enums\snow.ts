import type { TagUI } from "../interface";

export enum SnowFeatureType {
  jump = "jump",
  rail = "rail",
  transition = "transition",
}

export const snowFeatureTypes = Object.values(SnowFeatureType) as [
  SnowFeatureType,
  ...SnowFeatureType[],
];

export enum LandingDescription {
  "hands down" = "hands down",
  "two hands down" = "two hands down",
  "butt check" = "butt check",
  "under rotated" = "under rotated",
  "slide out" = "slide out",
  revert = "revert",
  "toe edge" = "toe edge",
  bobble = "bobble",
  "off balance" = "off balance",
  "heel edge" = "heel edge", //snow ski only
  "lost ski" = "lost ski", //snow ski only
}

export const landingDescriptions = Object.values(LandingDescription) as [
  LandingDescription,
  ...LandingDescription[],
];

export enum SnowLandingType {
  none = "none",
  stomped = "stomped",
  sketchy = "sketchy",
  crash = "crash",
}

export const snowLandingTypes = Object.values(SnowLandingType) as [
  SnowLandingType,
  ...SnowLandingType[],
];

export enum SnowLandingZone {
  none = "none",
  deck = "deck",
  knuckle = "knuckle",
  "short of sweet spot" = "short of sweet spot",
  "sweet spot" = "sweet spot",
  deep = "deep",
}

export const snowLandingZones = Object.values(SnowLandingZone) as [
  SnowLandingZone,
  ...SnowLandingZone[],
];

export enum SnowRailSpinDirection {
  none = "none",
  left = "left",
  right = "right",
  backside = "backside",
  frontside = "frontside",
  pretzel = "pretzel",
  continue = "continue",
}

export const snowRailSpinDirections = Object.values(SnowRailSpinDirection) as [
  SnowRailSpinDirection,
  ...SnowRailSpinDirection[],
];

export enum SnowJumpSpinDirection {
  none = "none",
  left = "left",
  right = "right",
  backside = "backside",
  frontside = "frontside",
}

export const snowJumpSpinDirections = Object.values(SnowJumpSpinDirection) as [
  SnowJumpSpinDirection,
  ...SnowJumpSpinDirection[],
];

export enum SnowRunSource {
  VIDEO_ANALYSIS = "VIDEO_ANALYSIS",
  FIS_RESULTS = "FIS_RESULTS",
}

export enum SnowEvent {
  SLOPESTYLE = "SLOPESTYLE",
  BIG_AIR = "BIG_AIR",
  HALF_PIPE = "HALF_PIPE",
}

export enum SnowDiscipline {
  FREESKI = "FREESKI",
  SNOWBOARD = "SNOWBOARD",
}

export const snowTimelineTags: TagUI[] = [
  {
    value: "startFrame",
    label: "Start Frame",
    key: "1",
    className: "bg-fuchsia text-white border-black/30",
  },
  {
    value: "grabStart",
    label: "Grab Start",
    key: "2",
    className: "bg-neonGreen text-black/60 border-black/60",
  },
  {
    value: "grabEnd",
    label: "Grab End",
    key: "3",
    className: "bg-blue text-white border-black/30",
  },
  {
    value: "takeOffFrame",
    label: "Take Off Frame",
    key: "4",
    className: "bg-purple text-white border-black/30",
  },
  {
    value: "landingFrame",
    label: "Landing Frame",
    key: "5",
    className: "bg-orange text-white border-black/30",
  },
  {
    value: "endFrame",
    label: "End Frame",
    key: "6",
    className: "bg-orange text-white border-black/30",
  },
];
