import { and, eq, inArray } from "drizzle-orm";
import { z } from "zod";
import type { TaglistTag } from "~/lib/interface";
import {
  sprintingRaces,
  sprintingTags,
  sprintStrides,
} from "~/server/db/sprintSchema";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { sprintRaceSchema } from "~/lib/schemas";
import type { RouterOutputs } from "~/trpc/react";

// Helper function to check if a tag is a rcustom distance
const isCustomDistance = (tag: string): boolean => {
  return !isNaN(Number(tag));
};

type Outputs = RouterOutputs["sprint"];

export type GetSprintingTagsOutput = Outputs["getTags"];
export type GetSprintingStridesOutput = Outputs["getRace"];

export const sprintRouter = createTRPCRouter({
  getRace: protectedProcedure
    .input(z.object({ videoId: z.string() }))
    .query(async ({ input, ctx }) => {
      return ctx.db.query.sprintingRaces.findFirst({
        where: eq(sprintingRaces.videoId, input.videoId),
        with: {
          strides: true,
        },
      });
    }),
  getTags: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const race = await ctx.db.query.sprintingRaces.findFirst({
        where: eq(sprintingRaces.videoId, input.id),
        with: {
          tags: true,
          strides: true,
        },
      });

      const formatTags: TaglistTag[] = [];
      if (race) {
        race.tags.forEach((t) => {
          formatTags.push({
            id: t.id,
            raceId: t.sprintingRaceId,
            videoId: race.videoId ?? "",
            frame: t.frame,
            tag: t.tag ?? "",
            athleteId: t.athleteId ?? "",
            userId: t.userId ?? "",
          });
        });
      }

      return {
        tags: formatTags,
        races: race ? [{ ...race, tags: undefined }] : [],
      };
    }),

  upsertRace: protectedProcedure
    .input(
      z.object({
        videoId: z.string(),
        ...sprintRaceSchema.shape,
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Use existing ID or generate a new one
      const raceId = input.id ?? crypto.randomUUID();

      await ctx.db
        .insert(sprintingRaces)
        .values({
          ...input,
          id: raceId,
          position: +input.position,
          wind: +input.wind,
        })
        .onDuplicateKeyUpdate({
          set: {
            ...input,
            position: +input.position,
            wind: +input.wind,
          },
        });

      return { ...input, id: raceId };
    }),
  upsertStride: protectedProcedure
    .input(
      z.object({
        sprintId: z.string(),
        number: z.number(),
        heelContact: z.number(),
        toeOff: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const obj = { ...input, userId: ctx.session.user.id };
      await ctx.db.insert(sprintStrides).values(obj).onDuplicateKeyUpdate({
        set: obj,
      });
    }),
  upsertTag: protectedProcedure
    .input(
      z.object({
        id: z.string().optional(),
        sprintingRaceId: z.string(),
        athleteId: z.string().optional(),
        tag: z.string(),
        frame: z.number(),
        userId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // If we have an ID, try to update the existing tag first
      if (input.id) {
        // Update both tag value and frame for all tag types
        await ctx.db
          .update(sprintingTags)
          .set({
            tag: input.tag, // Always update the tag value
            frame: input.frame,
          })
          .where(eq(sprintingTags.id, input.id));

        return [{ id: input.id }];
      }

      // Special handling for numeric tags (custom distances)
      if (isCustomDistance(input.tag)) {
        try {
          //check if there's any custom distance tag at exact frame
          const existingTagAtFrame = await ctx.db.query.sprintingTags.findFirst(
            {
              where: and(
                eq(sprintingTags.sprintingRaceId, input.sprintingRaceId),
                eq(sprintingTags.frame, input.frame),
              ),
            },
          );

          if (existingTagAtFrame && isCustomDistance(existingTagAtFrame.tag)) {
            // If there's a custom distance tag at the same frame, update it with the new value
            await ctx.db
              .update(sprintingTags)
              .set({
                tag: input.tag,
                athleteId: input.athleteId ?? "",
              })
              .where(eq(sprintingTags.id, existingTagAtFrame.id));

            return [{ id: existingTagAtFrame.id }];
          } else {
            // Otherwise, new tag
            const newId = crypto.randomUUID();

            await ctx.db
              .insert(sprintingTags)
              .values({
                id: newId,
                sprintingRaceId: input.sprintingRaceId,
                athleteId: input.athleteId ?? "",
                tag: input.tag,
                frame: input.frame,
                userId: ctx.session.user.id,
              })
              .onDuplicateKeyUpdate({
                set: {
                  tag: input.tag, // Update tag value here too
                  frame: input.frame,
                  athleteId: input.athleteId ?? "",
                  userId: ctx.session.user.id,
                },
              });

            return [{ id: newId }];
          }
        } catch (error) {
          console.error("Error in upsertTag for custom distance:", error);
          throw error;
        }
      } else {
        // Standard behavior for all other tags
        try {
          // First check if a tag with this race ID and tag value already exists
          const existingTag = await ctx.db.query.sprintingTags.findFirst({
            where: and(
              eq(sprintingTags.sprintingRaceId, input.sprintingRaceId),
              eq(sprintingTags.tag, input.tag),
            ),
          });

          if (existingTag) {
            // Update the existing tag
            await ctx.db
              .update(sprintingTags)
              .set({
                frame: input.frame,
                athleteId: input.athleteId ?? "",
              })
              .where(eq(sprintingTags.id, existingTag.id));

            return [{ id: existingTag.id }];
          } else {
            // Insert a new tag
            const newId = crypto.randomUUID();

            await ctx.db.insert(sprintingTags).values({
              id: newId, // Explicitly set the ID
              sprintingRaceId: input.sprintingRaceId,
              athleteId: input.athleteId ?? "", // Use empty string instead of null
              tag: input.tag,
              frame: input.frame,
              userId: ctx.session.user.id,
            });

            // Return the new ID
            return [{ id: newId }];
          }
        } catch (error) {
          console.error("Error in upsertTag:", error);
          throw error;
        }
      }
    }),
  deleteTag: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.delete(sprintingTags).where(eq(sprintingTags.id, input.id));
    }),

  deleteTags: protectedProcedure
    .input(z.object({ ids: z.array(z.string()) }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db
        .delete(sprintingTags)
        .where(inArray(sprintingTags.id, input.ids));
    }),
  deleteStrides: protectedProcedure
    .input(z.object({ ids: z.array(z.string()) }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.delete(sprintStrides).where(
        inArray(
          sprintStrides.id,
          input.ids.map((id) => parseInt(id)),
        ),
      );
    }),
});
