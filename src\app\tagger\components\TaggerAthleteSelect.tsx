import { PlusCircleIcon } from "lucide-react";
import { Card } from "~/components/Card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { useStore } from "~/hooks/store";

/**
 * @deprecated
 */
export const TaggerAthleteSelect = ({
  athleteOptions,
}: {
  athleteOptions: { athleteId: string; name: string }[];
}) => {
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const setSelectedAthlete = useStore((state) => state.setSelectedAthlete);
  const sortedAthletes = athleteOptions.sort((a, b) =>
    a.name.localeCompare(b.name),
  );

  return (
    <Card>
      <div className="flex w-full flex-col items-start justify-center gap-[5px]">
        <p className="text-smallLabel text-gray">Athlete:</p>
        <div className="flex w-full items-start gap-[5px]">
          <Select
            value={selectedAthlete}
            disabled={false}
            defaultValue={selectedAthlete}
            onValueChange={setSelectedAthlete}
          >
            <SelectTrigger className="flex flex-1 gap-2.5 rounded-full py-[5px]">
              <SelectValue placeholder="Select athlete" />
            </SelectTrigger>
            <SelectContent>
              {sortedAthletes.map((athlete) => (
                <SelectItem key={athlete.athleteId} value={athlete.athleteId}>
                  {athlete.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <button>
            <PlusCircleIcon strokeWidth={1} className="text-gray" />
          </button>
        </div>
      </div>
    </Card>
  );
};
