export enum TagAction {
  add = "add",
  remove = "remove",
  update = "update",
}

export enum FormItemType {
  text = "text",
  textarea = "textarea",
  select = "select",
  editSelect = "editSelect",
  checkbox = "checkbox",
  boolean = "boolean",
  number = "number",
  radio = "radio",
  date = "date",
}

export enum UserRole {
  admin = "admin",
  analyst = "analyst",
  support_staff = "support_staff",
  coach = "coach",
  athlete = "athlete",
  guest = "guest",
  pta_platform_access = "pta_platform_access",
  api = "api",
  api_modify = "api_modify",
  video_platform_access = "video_platform_access",
  sport_athletics = "sport_athletics",
  sport_cycling = "sport_cycling",
  sport_kayak = "sport_kayak",
  sport_rowing = "sport_rowing",
  sport_sailing = "sport_sailing",
  sport_snowsports = "sport_snowsports",
  sport_swimming = "sport_swimming",
  sport_training = "sport_training",
  sport_trampoline = "sport_trampoline",
  sport_beachvolleyball = "sport_beachvolleyball",
  sport_boxing = "sport_boxing",
  sport_climbing = "sport_climbing",
  sport_diving = "sport_diving",
  sport_equestrian = "sport_equestrian",
  sport_triathlon = "sport_triathlon",
}

export enum Sport {
  athletics = "athletics",
  cycling = "cycling",
  kayak = "kayak",
  rowing = "rowing",
  sailing = "sailing",
  snowsports = "snow_sports",
  swimming = "swimming",
  training = "training",
  trampoline = "trampoline",
  beachvolleyball = "beach_volleyball",
  boxing = "boxing",
  climbing = "climbing",
  diving = "diving",
  equestrian = "equestrian",
  triathlon = "triathlon",
}

export const sports = Object.values(Sport) as [Sport, ...Sport[]];

export enum SortBy {
  name = "name",
  video_date = "video_date",
  name_desc = "name_desc",
  video_date_desc = "video_date_desc",
}

export enum VideoStatus {
  Raw = "Raw",
  Trimmed = "Trimmed",
  Tagged_by_AI = "Tagged_by_AI",
  AI_in_Progress = "AI_in_Progress",
  Tagged = "Tagged",
  Review = "Review",
  Analysed = "Analysed",
}

export enum Gender {
  Men = "Men",
  Women = "Women",
  Mixed = "Mixed",
}

export const genders = Object.values(Gender) as [Gender, ...Gender[]];
