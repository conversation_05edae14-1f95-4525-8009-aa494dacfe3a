import type { FormItemProps } from "~/components/FormItem";
import { FormItemType } from "~/lib/enums/enums";
import type { Option } from "~/lib/interface";

interface LandingDescriptionFormItemProps extends FormItemProps {
  name: "landingDescriptions";
}

export const landingDescriptionFormItem = ({
  options,
}: {
  options: Option[];
}) => {
  return {
    name: "landingDescriptions",
    title: "Landing Description: (C/V)",
    type: FormItemType.checkbox,
    className: "flex items-start col-span-full",
    hotkey: {
      value: "c",
      className: "bg-fuchsia text-fuchsia-20 text-xs",
      onClick: () => {
        const checkboxGroup = document.getElementById("form-checkbox-group");
        // Get all checkboxes in the group
        const checkBoxes = checkboxGroup?.querySelectorAll("button");

        // Find currently focused checkbox
        const focusedElement = document.activeElement;

        if (
          focusedElement &&
          checkBoxes &&
          Array.from(checkBoxes).includes(focusedElement as HTMLButtonElement)
        ) {
          // Find the index of currently focused checkbox
          const currentIndex = Array.from(checkBoxes).indexOf(
            focusedElement as HTMLButtonElement,
          );
          // Focus on next checkbox (or loop back to first)
          const nextIndex = (currentIndex + 1) % checkBoxes.length;
          checkBoxes[nextIndex]?.focus();
        } else {
          // If no checkbox is focused, focus on the first one
          checkBoxes?.[0]?.focus();
        }
      },
    },
    subHotkey: {
      value: "v",
      onClick: () => {
        const focusedElement = document.activeElement;
        //check if focused element is a checkbox
        if (focusedElement && focusedElement instanceof HTMLButtonElement) {
          focusedElement.click();
        }
      },
    },
    labelClassName: "w-[98px]",
    options,
  } as LandingDescriptionFormItemProps;
};
