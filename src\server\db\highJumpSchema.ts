import { type InferSelectModel, relations, sql } from "drizzle-orm";
import {
  boolean,
  double,
  int,
  mysqlEnum,
  text,
  timestamp,
  unique,
  varchar,
} from "drizzle-orm/mysql-core";
import { createTable } from "./schema";
import { highJumpSides, highJumpTypes } from "~/lib/enums/highJump";

export const highJumps = createTable(
  "high_jumps",
  {
    id: int("id").primaryKey().autoincrement(),
    videoId: varchar("video_id", { length: 255 }).notNull(),
    athleteId: varchar("athlete_id", { length: 255 }).notNull(),
    number: int("number").notNull(),
    startFrame: int("start_frame").notNull(),
    endFrame: int("end_frame").notNull(),
    // numberOfStrides: int("number_of_strides"),
    approachSide: mysqlEnum(highJumpSides).notNull(),
    height: double("height"),
    success: boolean("success"),
    type: mysqlEnum(highJumpTypes),
    withBox: boolean("with_box"),
    withBar: boolean("with_bar"),
    comment: text("comment"),
    userId: varchar("user_id", { length: 255 }).notNull(),
    dateCreated: timestamp("date_created", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
  },
  (t) => [unique().on(t.videoId, t.number)],
);
export type HighJump = InferSelectModel<typeof highJumps>;
export const highJumpsRelations = relations(highJumps, ({ many }) => ({
  strides: many(highJumpStrides),
}));

export const highJumpStrides = createTable(
  "high_jump_strides",
  {
    id: int("id").primaryKey().autoincrement(),
    jumpId: int("jump_id").notNull(),
    number: int("number").notNull(),
    heelContact: int("heel_contact").notNull(),
    toeOff: int("toe_off").notNull(),
    userId: varchar("user_id", { length: 255 }).notNull(),
    dateCreated: timestamp("date_created", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
  },
  (t) => [unique().on(t.jumpId, t.number)],
);
export type HighJumpStride = InferSelectModel<typeof highJumpStrides>;
export const highJumpStridesRelations = relations(
  highJumpStrides,
  ({ one }) => ({
    highJumps: one(highJumps, {
      fields: [highJumpStrides.jumpId],
      references: [highJumps.id],
    }),
  }),
);
