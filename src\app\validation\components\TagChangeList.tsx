import { Badge } from "~/components/ui/badge";
import { useStore } from "~/hooks/store";
import type { TagChange } from "~/lib/interface";
import { cn } from "~/lib/utils";

const TagChangItem = ({ tagChange }: { tagChange: TagChange }) => {
  const hasNewTagType =
    tagChange.tagType[1] && tagChange.tagType[1] !== tagChange.tagType[0];
  const hasNewFrame =
    tagChange.frame[1] && tagChange.frame[1] !== tagChange.frame[0];

  return (
    <div className="flex flex-col gap-0.5 rounded-lg border bg-seaSalt-20 px-2.5 py-[5px] text-smallLabel text-black/60">
      <div className="flex gap-[5px]">
        <p className="w-[35px]">Frame: </p>
        <p className={cn(hasNewFrame ? "line-through" : "text-black")}>
          {tagChange.frame[0]}
        </p>
        {hasNewFrame && <p className="text-black">{tagChange.frame[1]}</p>}
      </div>
      <div className="flex gap-[5px]">
        <p className="w-[35px]">Tag: </p>
        <p className={cn(hasNewTagType ? "line-through" : "text-black")}>
          {tagChange.tagType[0]}
        </p>
        {hasNewTagType && <p className="text-black">{tagChange.tagType[1]}</p>}
      </div>
    </div>
  );
};

export const TagChangeList = ({ height }: { height?: number }) => {
  const tagChanges = useStore((state) => state.tagChanges);

  const changeList: {
    name: string;
    tags: TagChange[];
    variant: "add" | "edit" | "destructive";
  }[] = [
    {
      name: "Add",
      tags: tagChanges.humanAddedTags,
      variant: "add",
    },
    {
      name: "Edit",
      tags: tagChanges.humanUpdatedTags,
      variant: "edit",
    },
    {
      name: "Remove",
      tags: tagChanges.removedAITags,
      variant: "destructive",
    },
  ];

  return (
    <div
      className="flex flex-col gap-2.5 overflow-y-auto py-2.5 pl-2.5 pr-[15px]"
      style={{ height }}
    >
      {changeList.map((change) => {
        if (change.tags.length === 0) return null;
        return (
          <div className="flex flex-col gap-[5px]" key={change.name}>
            <Badge variant={change.variant} className="w-fit">
              {change.name}
            </Badge>
            {change.tags.map((tagChange) => {
              return (
                <TagChangItem key={tagChange.tagId} tagChange={tagChange} />
              );
            })}
          </div>
        );
      })}
    </div>
  );
};
