import { angleAnchors } from "./interfaces/drawingTypes";
import type {
  ColouredLine,
  CurrentLine,
  DrawingMode,
} from "./interfaces/drawingTypes";

// Draw all saved lines
export function drawLines(
  ctx: CanvasRenderingContext2D,
  lines: ColouredLine[],
  displayWidth: number,
  displayHeight: number,
  displayScaleX: number,
  displayScaleY: number,
  drawingMode: DrawingMode,
) {
  lines.forEach((line) => {
    ctx.beginPath();
    ctx.strokeStyle = line.color;
    ctx.lineWidth =
      (line.strokeWidth ?? 2) / Math.min(displayScaleX, displayScaleY);

    if (drawingMode === "rectangle") {
      const width = (line.endX - line.startX) * displayWidth;
      const height = (line.endY - line.startY) * displayHeight;
      ctx.rect(
        line.startX * displayWidth,
        line.startY * displayHeight,
        width,
        height,
      );
    } else {
      ctx.moveTo(line.startX * displayWidth, line.startY * displayHeight);
      ctx.lineTo(line.endX * displayWidth, line.endY * displayHeight);
    }

    ctx.stroke();
  });
}

// Draw the current line being drawn
export function drawCurrentLine(
  ctx: CanvasRenderingContext2D,
  currentLine: CurrentLine | null,
  selectedColour: string,
  displayWidth: number,
  displayHeight: number,
  displayScaleX: number,
  displayScaleY: number,
  drawingMode: DrawingMode,
) {
  if (!currentLine) return;

  ctx.beginPath();
  ctx.strokeStyle = selectedColour;
  ctx.lineWidth =
    (currentLine.strokeWidth ?? 2) / Math.min(displayScaleX, displayScaleY);

  if (drawingMode === "rectangle") {
    const width = (currentLine.endX - currentLine.startX) * displayWidth;
    const height = (currentLine.endY - currentLine.startY) * displayHeight;
    ctx.rect(
      currentLine.startX * displayWidth,
      currentLine.startY * displayHeight,
      width,
      height,
    );
  } else {
    ctx.moveTo(
      currentLine.startX * displayWidth,
      currentLine.startY * displayHeight,
    );
    ctx.lineTo(
      currentLine.endX * displayWidth,
      currentLine.endY * displayHeight,
    );
  }

  ctx.stroke();
}

// Draw bounding box for a tag
export function drawTagBoundingBox(
  ctx: CanvasRenderingContext2D,
  tag: {
    x1?: number | null;
    y1?: number | null;
    x2?: number | null;
    y2?: number | null;
  },
  displayWidth: number,
  displayHeight: number,
  displayScaleX: number,
  displayScaleY: number,
) {
  const { x1, x2, y1, y2 } = tag;
  if (
    x1 !== undefined &&
    x1 !== null &&
    y1 !== undefined &&
    y1 !== null &&
    x2 !== undefined &&
    x2 !== null &&
    y2 !== undefined &&
    y2 !== null
  ) {
    ctx.beginPath();
    ctx.rect(
      x1 * displayWidth,
      y1 * displayHeight,
      (x2 - x1) * displayWidth,
      (y2 - y1) * displayHeight,
    );
    ctx.strokeStyle = "red";
    ctx.lineWidth = 1 / Math.min(displayScaleX, displayScaleY); // Adjust line width for scaling
    ctx.stroke();
  }
}

// Export angleAnchors for use in other files
export { angleAnchors };
