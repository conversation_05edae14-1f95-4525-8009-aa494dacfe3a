"use client";

import { useParams } from "next/navigation";
import { useEffect, useMemo } from "react";
import { toast } from "react-hot-toast";
import { getFilteredTags } from "~/lib/utils";
import { api } from "~/trpc/react";
import { useStore } from "./store";
import type { TagChange } from "~/lib/interface";
import { TagAction } from "~/lib/enums/enums";
import {
  DiscusHand,
  DiscusMovement,
  discusTagsUI,
  DiscusType,
} from "~/lib/enums/discus";

export const useDiscusTags = () => {
  const utils = api.useUtils();

  const params = useParams<{ id: string }>();
  const videoId = params.id;

  const tagFilterValues = useStore((state) => state.tagFilterValues);
  const currentFrame = useStore((state) => state.currentFrame);
  const filteredTags = useStore((state) => state.filteredTags);
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const discusThrow = useStore((state) => state.discusThrow);
  const setFilteredTags = useStore((state) => state.setFilteredTags);
  const setDiscusThrow = useStore((state) => state.setDiscusThrow);
  const setTagChanges = useStore((state) => state.setTagChanges);

  const { data: tags, isLoading } = api.discus.getTags.useQuery(
    { id: videoId },
    { enabled: !!videoId },
  );

  const firstThrow = tags?.throws?.[0];

  useEffect(() => {
    if (!firstThrow || !!discusThrow) return;
    setDiscusThrow(firstThrow);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [firstThrow]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const throwTags =
    tags?.tags.filter((x) => x.throwId === discusThrow?.id) ?? [];

  const newFilteredTags = useMemo(
    () =>
      getFilteredTags({
        sport: "discus",
        athleteId: selectedAthlete,
        tags: throwTags,
        tagTypes: discusTagsUI,
        filterValues: tagFilterValues,
      }),
    [selectedAthlete, throwTags, tagFilterValues],
  );

  useEffect(() => {
    if (JSON.stringify(newFilteredTags) !== JSON.stringify(filteredTags)) {
      setFilteredTags(newFilteredTags);
    }
  }, [newFilteredTags, filteredTags, setFilteredTags]);

  const { mutate: upsertTag } = api.discus.upsertTag.useMutation({
    onSuccess: () => {
      void utils.discus.getTags.invalidate({ id: videoId }); //todo
    },
    onError: (error) => {
      console.error(error);
      toast.error("Failed to add tag");
      void utils.discus.getTags.invalidate({ id: videoId });
    },
  });

  const { mutate: deleteTag } = api.discus.deleteTag.useMutation({
    onError: (error) => {
      console.error(error);
      toast.error("Failed to delete tag");
      void utils.discus.getTags.invalidate({ id: videoId });
    },
  });

  const { mutate: deleteTags } = api.discus.deleteTags.useMutation({
    onError: (error) => {
      console.error(error);
      toast.error("Failed to delete tags");
      void utils.discus.getTags.invalidate({ id: videoId });
    },
  });

  const onAddTag = (tag: string, frame?: number) => {
    if (!discusThrow || !tag) {
      toast.error("Missing discus throw");
      return;
    }

    const existingTag = throwTags.find((x) => x.tag === tag);

    upsertTag({
      id: existingTag ? +existingTag.id : undefined,
      throwId: discusThrow.id,
      tag,
      frame: frame ?? currentFrame,
    });
  };

  const onDeleteTag = (id: string) => {
    deleteTag({ id: +id });
    utils.discus.getTags.setData({ id: videoId }, (data) => {
      if (!data) return { tags: [], throws: [] };
      return {
        ...data,
        tags: data.tags.map((x) =>
          x.id === id ? { ...x, isDeleted: true } : x,
        ),
      };
    });
  };

  const onDeleteTags = (ids: string[]) => {
    deleteTags({ ids: ids.map((x) => +x) });
    utils.discus.getTags.setData({ id: videoId }, (data) => {
      if (!data) return { tags: [], throws: [] };
      return {
        ...data,
        tags: data.tags.map((x) =>
          ids.includes(x.id.toString()) ? { ...x, isDeleted: true } : x,
        ),
      };
    });
  };

  return { tags, isLoading, onAddTag, onDeleteTag, onDeleteTags };
};
