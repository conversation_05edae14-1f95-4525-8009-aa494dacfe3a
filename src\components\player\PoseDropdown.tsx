"use client";

import { But<PERSON> } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { PoseOverlayToggle } from "./PoseToggle";
import { AngleOverlayToggle } from "./PoseToggle";
import { VideoToggle } from "./PoseToggle";
import { DrawingModePicker } from "./DrawingModePicker";
import { LineColourPicker } from "./LineColourPicker";

interface DrawingToolsDropdownProps {
  setShowPose: (show: boolean) => void;
  setShowAngles: (show: boolean) => void;
  setShowVideo: (show: boolean) => void;
  showPose?: boolean;
  showAngles?: boolean;
  showVideo?: boolean;
  handleUndoLine: () => void;
  handleClearLines: () => void;
  selectedColour: "red" | "green" | "blue";
  setSelectedColour: (colour: "red" | "green" | "blue") => void;
  drawingMode: "line" | "rectangle";
  setDrawingMode: (mode: "line" | "rectangle") => void;
  strokeWidth: number;
  setStrokeWidth: (w: number) => void;
  poseCircleThickness: number;
  setPoseCircleThickness: (thickness: number) => void;
  poseLineThickness: number;
  setPoseLineThickness: (thickness: number) => void;
  zoomLevel: number;
  setZoomLevel: (level: number) => void;
}

export function PoseControlMenu({
  setShowPose,
  setShowAngles,
  setShowVideo,
  showPose = true,
  showAngles = true,
  showVideo = true,
  handleUndoLine,
  handleClearLines,
  selectedColour,
  setSelectedColour,
  drawingMode,
  setDrawingMode,
  strokeWidth,
  setStrokeWidth,
  poseCircleThickness,
  setPoseCircleThickness,
  poseLineThickness,
  setPoseLineThickness,
  zoomLevel,
  setZoomLevel,
}: DrawingToolsDropdownProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          Drawing Tools
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64 space-y-2 p-2">
        <DropdownMenuLabel>Pose & Drawing Options</DropdownMenuLabel>
        <div className="px-1">
          <div className="flex items-center gap-2">
            <PoseOverlayToggle onChange={setShowPose} value={showPose} />

            <AngleOverlayToggle onChange={setShowAngles} value={showAngles} />
          </div>
          <div className="mt-2">
            <VideoToggle onChange={setShowVideo} value={showVideo} />
          </div>
          <div className="mt-2 space-y-2">
            <div className="flex items-center gap-3">
              <label
                className="text-sm font-medium"
                htmlFor="poseCircleThicknessSelect"
              >
                Circle Thickness:
              </label>
              <select
                id="poseCircleThicknessSelect"
                className="rounded border px-2 py-1 text-sm"
                value={poseCircleThickness}
                onChange={(e) => setPoseCircleThickness(Number(e.target.value))}
              >
                {[2, 3, 4, 5, 6, 8].map((t) => (
                  <option key={t} value={t}>
                    {t}px
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-center gap-3">
              <label
                className="text-sm font-medium"
                htmlFor="poseLineThicknessSelect"
              >
                Line Thickness:
              </label>
              <select
                id="poseLineThicknessSelect"
                className="rounded border px-2 py-1 text-sm"
                value={poseLineThickness}
                onChange={(e) => setPoseLineThickness(Number(e.target.value))}
              >
                {[1, 1.5, 2, 2.5, 3, 4].map((t) => (
                  <option key={t} value={t}>
                    {t}px
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
        <DropdownMenuSeparator />
        <div className="flex justify-between gap-2 px-1">
          <Button
            variant="secondary"
            className="w-full"
            onClick={handleUndoLine}
          >
            Undo Line
          </Button>
          <Button
            variant="secondary"
            className="w-full"
            onClick={handleClearLines}
          >
            Clear All
          </Button>
        </div>
        <DropdownMenuSeparator />
        <div className="flex flex-col gap-2.5">
          <div className="px-1">
            <LineColourPicker
              selected={selectedColour}
              onChange={setSelectedColour}
            />
          </div>
          <div className="flex items-center gap-3 px-1">
            <label className="text-sm font-medium" htmlFor="strokeWidthSelect">
              Stroke Width:
            </label>
            <select
              id="strokeWidthSelect"
              className="rounded border px-2 py-1 text-sm"
              value={strokeWidth}
              onChange={(e) => setStrokeWidth(Number(e.target.value))}
            >
              {[0.5, 1, 1.5, 2].map((w) => (
                <option key={w} value={w}>
                  {w}px
                </option>
              ))}
            </select>
          </div>
          <div className="px-1">
            <DrawingModePicker
              selected={drawingMode}
              onChange={setDrawingMode}
            />
          </div>
          <div className="flex items-center gap-3 px-1">
            <label className="text-sm font-medium" htmlFor="zoomLevelSelect">
              Video Zoom:
            </label>
            <select
              id="zoomLevelSelect"
              className="rounded border px-2 py-1 text-sm"
              value={zoomLevel}
              onChange={(e) => setZoomLevel(Number(e.target.value))}
            >
              {[
                { label: "115%", value: 1.15 },
                { label: "125%", value: 1.25 },
                { label: "150%", value: 1.5 },
                { label: "200%", value: 2.0 },
              ].map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
