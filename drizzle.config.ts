import { type Config } from "drizzle-kit";

import { env } from "~/env";

export default {
  schema: [
    "./src/server/db/schema.ts",
    "./src/server/db/snowSchema.ts",
    "./src/server/db/highJumpSchema.ts",
    "./src/server/db/boxingSchema.ts",
    "./src/server/db/discusSchema.ts",
    "./src/server/db/sprintSchema.ts",
  ],
  dialect: "mysql",
  dbCredentials: {
    url: env.DATABASE_URL,
  },
  tablesFilter: ["tagger_*"],
} satisfies Config;
