export enum BoxingPunchType {
  Jab = "Jab",
  Cross = "Cross",
  LeftHook = "L Hook",
  RightHook = "R Hook",
  LeftUppercut = "L Upcut",
  RightUppercut = "R Upcut",
}

export const boxingPunchTypes = Object.values(BoxingPunchType) as [
  BoxingPunchType,
  ...BoxingPunchType[],
];

export enum BoxingPunchLocation {
  Success = "Success",
  Head = "Head",
  Unsure = "Unsure",
  Feint = "Feint",
  Clinch = "Clinch",
  Switch = "Switch",
}

export const boxingPunchLocation = Object.values(BoxingPunchLocation) as [
  BoxingPunchLocation,
  ...BoxingPunchLocation[],
];

export enum BoxingCorner {
  Red = "Red",
  Blue = "Blue",
}

export const boxingCorner = Object.values(BoxingCorner) as [
  BoxingCorner,
  ...BoxingCorner[],
];
