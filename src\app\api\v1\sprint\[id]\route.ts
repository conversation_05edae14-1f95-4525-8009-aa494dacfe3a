import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import type { NextRequest } from "next/server";
import { validateSprintBody } from "~/server/api/utils/bodyValidation";
import { checkToken } from "~/server/api/utils/permissions";
import { returnError } from "~/server/api/utils/returnError";
import { getUserIdByToken } from "~/server/api/utils/service";
import { db } from "~/server/db";
import {
  sprintingRaces,
  sprintingTags,
  sprintStrides,
} from "~/server/db/sprintSchema";

/**
 * @swagger
 * /api/v1/sprint/{id}:
 *   get:
 *     description: Get sprint race details with strides and tags for a specific video ID
 *     tags: [Sprint]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 videoId:
 *                   type: string
 *                 name:
 *                   type: string
 *                 eventGroup:
 *                   type: string
 *                 distance:
 *                   type: string
 *                 round:
 *                   type: string
 *                 position:
 *                   type: number
 *                 time:
 *                   type: string
 *                 track:
 *                   type: string
 *                 gender:
 *                   type: string
 *                 date:
 *                   type: string
 *                 wind:
 *                   type: number
 *                 tags:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       sprintingRaceId:
 *                         type: string
 *                       athleteId:
 *                         type: string
 *                       tag:
 *                         type: string
 *                       userId:
 *                         type: string
 *                       frame:
 *                         type: number
 *                 strides:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: number
 *                       sprintId:
 *                         type: string
 *                       number:
 *                         type: number
 *                       heelContact:
 *                         type: number
 *                       toeOff:
 *                         type: number
 *                       userId:
 *                         type: string
 *                       dateCreated:
 *                         type: string
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: UNAUTHORIZED
 *                 message:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: INTERNAL_SERVER_ERROR
 *                 message:
 *                   type: string
 *                   example: An unexpected error occurred
 *
 *   post:
 *     description: Create sprint race with strides and tags for a specific video ID
 *     tags: [Sprint]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               race:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                   eventGroup:
 *                     type: string
 *                   distance:
 *                     type: string
 *                   round:
 *                     type: string
 *                   position:
 *                     type: number
 *                   time:
 *                     type: string
 *                   track:
 *                     type: string
 *                   gender:
 *                     type: string
 *                   date:
 *                     type: string
 *                   wind:
 *                     type: number
 *               tags:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     athleteId:
 *                       type: string
 *                     tag:
 *                       type: string
 *                     frame:
 *                       type: number
 *               strides:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     number:
 *                       type: number
 *                     heelContact:
 *                       type: number
 *                     toeOff:
 *                       type: number
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: UNAUTHORIZED
 *                 message:
 *                   type: string
 *                   example: Invalid token
 *       400:
 *         description: Bad Request - Invalid body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: BAD_REQUEST
 *                 message:
 *                   type: string
 *                   example: Invalid Body
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: INTERNAL_SERVER_ERROR
 *                 message:
 *                   type: string
 *                   example: An unexpected error occurred
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  try {
    await checkToken(request);

    const raceWithStridesAndTags = await db.query.sprintingRaces.findFirst({
      where: eq(sprintingRaces.videoId, id),
      with: {
        strides: true,
        tags: true,
      },
    });

    return Response.json(raceWithStridesAndTags);
  } catch (e) {
    return returnError(e);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  try {
    const { tokenPayload } = await checkToken(request);
    const userId = await getUserIdByToken(tokenPayload);
    const body = await validateSprintBody(request);

    await db.transaction(async (tx) => {
      // Insert sprint race
      const result = await tx
        .insert(sprintingRaces)
        .values({
          videoId: id,
          name: body.race.name,
          eventGroup: body.race.eventGroup,
          distance: body.race.distance,
          round: body.race.round,
          position: body.race.position,
          time: body.race.time,
          track: body.race.track,
          gender: body.race.gender,
          date: body.race.date,
          wind: body.race.wind,
        })
        .$returningId();

      const raceId = result[0]?.id;
      if (!raceId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to insert sprint race",
        });
      }

      // Insert tags if any
      if (body.tags.length > 0) {
        await tx.insert(sprintingTags).values(
          body.tags.map((tag) => ({
            sprintingRaceId: raceId,
            athleteId: tag.athleteId,
            tag: tag.tag,
            userId: userId,
            frame: tag.frame,
          })),
        );
      }

      // Insert strides if any
      if (body.strides.length > 0) {
        await tx.insert(sprintStrides).values(
          body.strides.map((stride) => ({
            sprintId: raceId,
            number: stride.number,
            heelContact: stride.heelContact,
            toeOff: stride.toeOff,
            userId: userId,
          })),
        );
      }
    });

    return Response.json({ success: true });
  } catch (e) {
    return returnError(e);
  }
}
