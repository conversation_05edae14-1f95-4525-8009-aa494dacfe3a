"use client";

import Link from "next/link";
import { useParams, usePathname } from "next/navigation";
import { useStore } from "~/hooks/store";
import { api } from "~/trpc/react";

export function NextVideoButton() {
  const pathname = usePathname();
  const params = useParams<{ id: string }>();
  const videoId = params.id;
  const isVideoReviewPage = pathname?.startsWith("/validation/");

  const videoSummary = useStore((state) => state.videoSummary);

  const { data: videos } = api.videos.getVideos.useQuery(
    {
      sport: videoSummary?.sport ? [videoSummary.sport] : undefined,
    },
    {
      enabled: isVideoReviewPage,
    },
  );
  const currentVideoIndex =
    videos?.list?.findIndex((x) => x.id === videoId) ?? -1;

  let nextVideoId: string | undefined;

  if (videos?.list && currentVideoIndex !== undefined) {
    if (videos.list.length <= 1) {
    } else if (
      currentVideoIndex < 0 || //if current video not in the list
      currentVideoIndex >= videos.list.length - 1 //if current video is the last in the list
    ) {
      nextVideoId = videos.list[0]?.id;
    } else {
      nextVideoId = videos.list[currentVideoIndex + 1]?.id;
    }
  }

  const display = nextVideoId && isVideoReviewPage;

  return (
    <>
      {!display && null}
      {display && (
        <Link
          href={`/validation/${nextVideoId}`}
          className="inline-flex items-center justify-center gap-2.5 rounded-full border border-black px-5 py-2.5 leading-[17.60px] tracking-wide text-black"
        >
          Next Video {isVideoReviewPage ? "Review" : "Validation"}
        </Link>
      )}
    </>
  );
}
