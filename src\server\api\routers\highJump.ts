import { eq, inArray } from "drizzle-orm";
import { z } from "zod";
import { HighJumpSide, HighJumpType } from "~/lib/enums/highJump";
import { highJumps, highJumpStrides } from "~/server/db/highJumpSchema";
import type { RouterOutputs } from "~/trpc/react";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { TRPCError } from "@trpc/server";

type Outputs = RouterOutputs["highJump"];

export type GetHighJumpTagsOutput = Outputs["getTags"];

export const highJumpRouter = createTRPCRouter({
  getTags: protectedProcedure
    .input(z.object({ videoId: z.string() }))
    .query(async ({ input, ctx }) => {
      return ctx.db.query.highJumps.findMany({
        where: eq(highJumps.videoId, input.videoId),
        with: {
          strides: true,
        },
      });
    }),
  upsertHighJump: protectedProcedure
    .input(
      z.object({
        // id: z.number().optional(),
        videoId: z.string(),
        athleteId: z.string(),
        number: z.number(),
        startFrame: z.number(),
        endFrame: z.number(),
        approachSide: z.nativeEnum(HighJumpSide),
        height: z.number().optional(),
        success: z.boolean().optional(),
        type: z.nativeEnum(HighJumpType).optional(),
        withBox: z.boolean().optional(),
        withBar: z.boolean().optional(),
        comment: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { ...jump } = input;
      const result = await ctx.db
        .insert(highJumps)
        .values({ ...jump, userId: ctx.session.user.id })
        .onDuplicateKeyUpdate({
          set: jump,
        })
        .$returningId();

      const affectedId = result[0]?.id;
      if (!affectedId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to insert high jump",
        });
      }

      return {
        id: affectedId,
        ...input,
        type: input.type ?? HighJumpType.Flop,
        height: input.height ?? null,
        success: input.success ?? false,
        withBox: input.withBox ?? false,
        withBar: input.withBar ?? false,
        comment: input.comment ?? "",
        userId: ctx.session.user.id,
        dateCreated: new Date(),
      };
      // await ctx.db.transaction(async (tx) => {
      //   const result = await tx
      //     .insert(highJumps)
      //     .values({ ...jump, userId: ctx.session.user.id })
      //     .onDuplicateKeyUpdate({
      //       set: jump,
      //     })
      //     .$returningId();

      // const jumpId = result[0]?.id;
      // if (!jumpId) {
      //   throw new TRPCError({
      //     code: "INTERNAL_SERVER_ERROR",
      //     message: "Failed to insert high jump",
      //   });
      // }

      // if (strides && strides.length > 0) {
      //   await tx
      //     .insert(highJumpStrides)
      //     .values(
      //       strides.map((stride) => ({
      //         ...stride,
      //         jumpId,
      //         userId: ctx.session.user.id,
      //       })),
      //     )
      //     .onDuplicateKeyUpdate({
      //       set: {
      //         number: sql`values(${highJumpStrides.number})`,
      //         heelContact: sql`values(${highJumpStrides.heelContact})`,
      //         toeOff: sql`values(${highJumpStrides.toeOff})`,
      //       },
      //     });
      // }
      // });
    }),
  deleteHighJump: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.transaction(async (tx) => {
        await tx
          .delete(highJumpStrides)
          .where(eq(highJumpStrides.jumpId, input.id));
        await tx.delete(highJumps).where(eq(highJumps.id, input.id));
      });
    }),
  upsertStride: protectedProcedure
    .input(
      z.object({
        jumpId: z.number(),
        number: z.number(),
        heelContact: z.number(),
        toeOff: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const obj = { ...input, userId: ctx.session.user.id };
      await ctx.db.insert(highJumpStrides).values(obj).onDuplicateKeyUpdate({
        set: obj,
      });
    }),
  deleteStrides: protectedProcedure
    .input(z.array(z.number()))
    .mutation(async ({ ctx, input }) => {
      await ctx.db
        .delete(highJumpStrides)
        .where(inArray(highJumpStrides.id, input));
    }),
  deleteFrame: protectedProcedure
    .input(
      z.object({
        id: z.number(),
        startFrame: z.number().optional(),
        endFrame: z.number().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.db
        .update(highJumps)
        .set({
          startFrame: input.startFrame,
          endFrame: input.endFrame,
        })
        .where(eq(highJumps.id, input.id));
    }),
});
