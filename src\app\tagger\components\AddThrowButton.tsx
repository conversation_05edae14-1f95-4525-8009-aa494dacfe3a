"use client";

import toast from "react-hot-toast";
import { But<PERSON> } from "~/components/ui/button";
import { useStore } from "~/hooks/store";
import { ShotputType, ShotputMovement, ShotputHand } from "~/lib/enums/shotput";
import { api } from "~/trpc/react";

// Update the component to accept onThrowAdded without requiring updateLocalThrows
export const AddThrowButton = () => {
  const utils = api.useUtils();

  // Get the current throw from the store
  const shotputThrow = useStore((state) => state.shotputThrow);
  const videoSummary = useStore((state) => state.videoSummary);
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const setShotputThrow = useStore((state) => state.setShotputThrow);

  const { data: throws } = api.shotput.getThrows.useQuery(
    { videoId: videoSummary?.id ?? "" },
    { enabled: !!videoSummary?.id },
  );

  // Get the mutation for adding a new throw
  const { mutate: upsertShotputThrow, isPending } =
    api.shotput.upsertShotputThrow.useMutation({
      onSuccess: async (result) => {
        await utils.shotput.getThrows.invalidate();
        toast.success(`Throw added successfully`);
        setShotputThrow({
          ...result,
          weight: result.weight ?? null,
          comment: result.comment ?? null,
          isToolUsed: result.isToolUsed ?? false,
        });
      },
    });

  const handleAddThrow = async () => {
    if (!videoSummary?.id || !selectedAthlete) {
      toast.error("Missing video, athlete, or throw information");
      return;
    }
    // Find the highest throw number to ensure uniqueness
    const highestThrow =
      throws?.reduce(
        (max, throw_) => (throw_.number > max ? throw_.number : max),
        0,
      ) ?? 0;
    const nextThrowNumber = highestThrow + 1;

    upsertShotputThrow({
      videoId: videoSummary.id,
      athleteId: selectedAthlete,
      number: nextThrowNumber, // Use the calculated unique number
      // Use values from current throw
      movement: shotputThrow?.movement ?? ShotputMovement.Rotation,
      type: shotputThrow?.type ?? ShotputType.Full,
      hand: shotputThrow?.hand ?? ShotputHand.RightHand,
      weight: shotputThrow?.weight ?? 0,
      comment: shotputThrow?.comment ?? "",
      isToolUsed: shotputThrow?.isToolUsed,
    });
  };

  return (
    <Button
      onClick={handleAddThrow}
      className="mt-auto w-full rounded-full"
      disabled={isPending || !selectedAthlete || !videoSummary?.id}
    >
      Add Throw
    </Button>
  );
};
