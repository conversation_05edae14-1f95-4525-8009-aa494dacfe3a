"use client";
import { useParams } from "next/navigation";
import { SprintRaceForm } from "~/components/sprint/SprintRaceForm";
import { SprintTagsUI } from "~/components/sprint/SprintTagsUI";
import { api } from "~/trpc/react";

export const SprintTagger = () => {
  const params = useParams();
  const videoId = params.id as string;

  const { data: race } = api.sprint.getRace.useQuery({ videoId });

  return (
    <div className="flex h-full flex-col gap-2.5">
      <SprintRaceForm
        race={
          race
            ? {
                ...race,
                position: race.position.toString(),
                wind: race.wind.toString(),
              }
            : undefined
        }
      />
      {race && <SprintTagsUI />}
    </div>
  );
};
