import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Pencil, PlusIcon } from "lucide-react";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import type { z } from "zod";
import { FormItem, type FormItemProps } from "~/components/FormItem";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";
import { useStore } from "~/hooks/store";
import { FormItemType } from "~/lib/enums/enums";
import {
  SwimmingSource,
  SwimmingStartType,
  SwimmingStrokeCategory,
} from "~/lib/enums/swimming";
import type { Option } from "~/lib/interface";
import { swimmingRepFormSchema } from "~/lib/schemas";
import { api } from "~/trpc/react";
import { Selector } from "../Selector";
import { Checkbox } from "../ui/check-box";
import { SwimmingSessionSelector } from "./SwimmingSessionSelector";

type FormProps = z.infer<typeof swimmingRepFormSchema>;

interface RepFormItemProps extends FormItemProps {
  name: keyof FormProps;
}

export const EditSwimmingRep = () => {
  const params = useParams();
  const utils = api.useUtils();
  const videoId = params.id as string;

  const videoSummary = useStore((state) => state.videoSummary);
  const selectedSwimmingSession = useStore(
    (state) => state.selectedSwimmingSession,
  );
  const selectedSwimmingRace = useStore((state) => state.selectedSwimmingRace);
  const selectedSwimmingRep = useStore((state) => state.selectedSwimmingRep);
  const setSelectedSwimmingRep = useStore(
    (state) => state.setSelectedSwimmingRep,
  );

  const [editingRepId, setEditingRepId] = useState<string>("");

  let athleteOptions: Option[] = [];
  if (selectedSwimmingSession) {
    if (selectedSwimmingSession.athlete) {
      athleteOptions = [
        {
          label:
            selectedSwimmingSession.athlete.first_name +
            " " +
            selectedSwimmingSession.athlete.last_name,
          value: selectedSwimmingSession.athlete_id ?? "",
        },
      ];
    } else if (selectedSwimmingSession.relay_team) {
      athleteOptions = selectedSwimmingSession.relay_team.athletes.map(
        (athlete) => ({
          label: athlete.athlete.first_name + " " + athlete.athlete.last_name,
          value: athlete.athlete_id,
        }),
      );
    }
  }

  const formItems: RepFormItemProps[] = [
    {
      name: "athlete_id",
      title: "Athlete",
      type: FormItemType.select,
      placeholder: "Select Athlete",
      className: "grid",
      required: true,
      disabled: !!selectedSwimmingSession?.athlete_id,
      options: athleteOptions,
    },
    {
      name: "stroke_category",
      title: "Stroke Category",
      type: FormItemType.select,
      placeholder: "Select Stroke Category",
      className: "grid",
      options: Object.values(SwimmingStrokeCategory).map((category) => ({
        label: category.replaceAll("_", " "),
        value: category,
      })),
    },
    {
      name: "start_type",
      title: "Start Type",
      type: FormItemType.select,
      placeholder: "Select Start Type",
      className: "grid",
      options: Object.values(SwimmingStartType).map((type) => ({
        label: type,
        value: type,
      })),
    },
    {
      name: "race_suit",
      title: "Race Suit",
      type: FormItemType.checkbox,
      className: "grid",
      options: [
        {
          label: "Yes",
          value: true as unknown as string,
        },
      ],
      CustomRender: (field) => (
        <Checkbox
          checked={field.value as unknown as boolean}
          onCheckedChange={(checked) => {
            field.onChange(checked as unknown as string);
          }}
        />
      ),
    },
    {
      name: "distance",
      title: "Distance",
      type: FormItemType.text,
      className: "grid",
    },
    {
      name: "reaction_time",
      title: "Reaction Time",
      type: FormItemType.number,
      className: "grid",
    },
    {
      name: "piece_number",
      title: "Piece Number",
      type: FormItemType.text,
      className: "grid",
      disabled: true,
    },
    {
      name: "set_number",
      title: "Set Number",
      type: FormItemType.text,
      className: "grid",
      disabled: true,
    },
    {
      name: "rep_number",
      title: "Rep Number",
      type: FormItemType.text,
      className: "grid",
      disabled: true,
    },
    {
      name: "duration",
      title: "Duration",
      type: FormItemType.text,
      className: "grid",
      disabled: true,
    },
    {
      name: "speed",
      title: "Speed",
      type: FormItemType.text,
      className: "grid",
      disabled: true,
    },
  ];

  const initialFormData: FormProps = {
    video_id: videoId,
    session_id: selectedSwimmingSession?.session_id ?? "",
    athlete_id: "",
    athlete_name: "",
    stroke_category: SwimmingStrokeCategory.FREESTYLE,
    start_type: SwimmingStartType.DIVE,
    race_suit: true,
    piece_number: "1",
    set_number: "1",
    rep_number: "1",
    duration: "",
    distance: "",
    speed: "",
    source: SwimmingSource.VIDEO_ANALYSIS,
  };

  const { data: reps } = api.swimmingPta.getReps.useQuery(
    {
      sessionId: selectedSwimmingSession?.session_id ?? "",
    },
    {
      enabled: !!selectedSwimmingSession?.session_id,
    },
  );

  useEffect(() => {
    const hasSelectedRep =
      selectedSwimmingRep &&
      reps?.find((x) => x.id === selectedSwimmingRep?.id);
    if (hasSelectedRep) return;
    if (reps && reps.length > 0) {
      setSelectedSwimmingRep(reps[0]);
    } else {
      setSelectedSwimmingRep(undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reps]);

  const { mutate: upsertRep, isPending } =
    api.swimmingPta.upsertRep.useMutation({
      onSuccess: (data) => {
        toast.success("Rep created successfully");
        void utils.swimmingPta.getReps.invalidate();
        setEditingRepId("");
        setSelectedSwimmingRep(data);
      },
      onError: () => {
        toast.error("Failed to create rep");
      },
    });

  const form = useForm<FormProps>({
    resolver: zodResolver(swimmingRepFormSchema),
    defaultValues: { ...initialFormData },
  });

  useEffect(() => {
    if (selectedSwimmingSession?.athlete_id) {
      form.setValue("athlete_id", selectedSwimmingSession.athlete_id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedSwimmingSession]);

  const onAddRep = () => {
    const maxPieceNumber =
      reps?.reduce((max, rep) => {
        return Math.max(max, rep.piece_number ?? 0);
      }, 0) ?? 0;
    const nextNumber = maxPieceNumber + 1;
    form.reset({
      ...initialFormData,
      id: undefined,
      piece_number: nextNumber.toString(),
      rep_number: nextNumber.toString(),
      stroke_category:
        selectedSwimmingRace?.stroke_category ??
        SwimmingStrokeCategory.FREESTYLE,
      athlete_id: selectedSwimmingSession?.athlete_id ?? "",
    });
    setEditingRepId("new");
  };

  const onEditRep = () => {
    if (!selectedSwimmingRep) return;
    form.reset({
      ...selectedSwimmingRep,
      duration: (selectedSwimmingRep.duration ?? "").toString(),
      distance: (selectedSwimmingRep.distance ?? "").toString(),
      speed: (selectedSwimmingRep.speed ?? "").toString(),
      piece_number: (selectedSwimmingRep.piece_number ?? "").toString(),
      set_number: (selectedSwimmingRep.set_number ?? "").toString(),
      rep_number: (selectedSwimmingRep.rep_number ?? "").toString(),
      session_id: selectedSwimmingSession?.session_id ?? "",
      video_id: videoId,
      athlete_id:
        selectedSwimmingSession?.athlete_id ?? selectedSwimmingRep.athlete_id,
      athlete_name: selectedSwimmingRep.athlete_name,
      stroke_category: selectedSwimmingRep.stroke_category,
      start_type: selectedSwimmingRep.start_type,
      race_suit: selectedSwimmingRep.race_suit,
      reaction_time: selectedSwimmingRep.start?.reaction_time.toString(),
    });
    setEditingRepId(selectedSwimmingRep.id ?? "");
  };

  const onSubmit = (data: FormProps) => {
    if (!selectedSwimmingSession?.session_id || !data.athlete_id) {
      toast.error("Please select a session and athlete");
      return;
    }
    const editingRep = reps?.find((x) => x.id === selectedSwimmingRep?.id);

    const firstLap = editingRep?.laps?.[0];
    const firstLapStrokeType = firstLap?.stroke_type;
    let startStrokeType = firstLapStrokeType;
    if (!startStrokeType) {
      const repStrokeCategory =
        editingRep?.stroke_category ?? data.stroke_category;
      startStrokeType =
        repStrokeCategory === SwimmingStrokeCategory.INDIVIDUAL_MEDLEY
          ? SwimmingStrokeCategory.BUTTERFLY
          : repStrokeCategory;
    }

    upsertRep({
      id: selectedSwimmingRep?.id ?? undefined,
      ...data,
      session_id: selectedSwimmingSession.session_id,
      athlete_name:
        videoSummary?.athletes?.find(
          (athlete) => athlete.athleteId === data.athlete_id,
        )?.name ?? "",
      reaction_time: undefined,

      start:
        data.reaction_time && startStrokeType
          ? {
              reaction_time: data.reaction_time,
              stroke_type: startStrokeType,
            }
          : null,
    });
  };

  return (
    <div className="grid gap-2.5">
      <div className="flex items-end gap-2">
        <Selector
          containerClassName="grid gap-2.5"
          className="w-96 bg-white"
          label="Rep"
          options={reps
            ?.sort((a, b) => a.rep_number - b.rep_number)
            .map((rep) => ({
              label: `Rep ${rep.rep_number}: ${rep.athlete_name}`,
              value: rep.id ?? "",
            }))}
          value={selectedSwimmingRep?.id ?? ""}
          onValueChange={(value) => {
            const rep = reps?.find((rep) => rep.id === value);
            if (rep) {
              setSelectedSwimmingRep(rep);
            }
          }}
        />

        <Button
          size="icon"
          variant="search"
          disabled={!selectedSwimmingRep}
          onClick={onEditRep}
        >
          <Pencil className="h-3 w-3 text-seaSalt-k40" />
        </Button>
        <Button size="icon" variant="search" onClick={onAddRep}>
          <PlusIcon className="h-3 w-3 text-seaSalt-k40" />
        </Button>
      </div>

      <Dialog
        open={editingRepId.length > 0}
        onOpenChange={(open) => {
          if (!open) {
            setEditingRepId("");
            form.reset(initialFormData);
          }
        }}
      >
        <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Rep</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className="grid gap-2">
            <SwimmingSessionSelector disabled />
          </div>
          <Form {...form}>
            <form
              className="flex flex-col gap-2"
              onSubmit={form.handleSubmit(onSubmit)}
            >
              {formItems.map((item) => (
                <FormItem key={item.name} control={form.control} {...item} />
              ))}
              <Button type="submit" loading={isPending}>
                Save
              </Button>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
};
