import { format } from "date-fns";
import { z } from "zod";
import { SwimmingStrokeCategory } from "~/lib/enums/swimming";
import {
  swimmingRaceFormSchema,
  swimmingRepFormSchema,
  swimmingSessionFormSchema,
} from "~/lib/schemas";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import type { RouterOutputs } from "~/trpc/react";
import { swimmingPta } from "../utils/pta/swimming";

type Outputs = RouterOutputs["swimmingPta"];

export type GetRacesOutput = Outputs["getRaces"];

export const swimmingPtaRouter = createTRPCRouter({
  getRaces: protectedProcedure
    .input(z.object({ competitionId: z.string().optional() }))
    .query(async ({ input }) => {
      if (!input.competitionId) {
        return [];
      }
      const races = await swimmingPta.getRaces({
        competitionId: input.competitionId,
      });
      return races;
    }),
  upsertRace: protectedProcedure
    .input(
      z.object({
        race: z.object({
          race_id: z.string().optional(),
          competition_id: z.string(),
          ...swimmingRaceFormSchema.shape,
        }),
      }),
    )
    .mutation(async ({ input }) => {
      let classification = input.race.classification;
      if (classification?.length === 0 || classification === "none") {
        classification = null;
      }
      const race = await swimmingPta.upsertRace({
        ...input.race,
        date: format(input.race.date, "yyyy-MM-dd"),
        race_distance: +input.race.race_distance,
        classification,
      });
      return race;
    }),
  getSessions: protectedProcedure
    .input(
      z.object({
        raceId: z.string().optional(),
        sessionDescription: z.string().optional(),
      }),
    )
    .query(async ({ input }) => {
      const sessions = await swimmingPta.getSessions(input);
      return sessions;
    }),
  upsertSession: protectedProcedure
    .input(
      z.object({
        sessionId: z.string().optional(),
        ...swimmingSessionFormSchema.shape,
      }),
    )
    .mutation(async ({ input }) => {
      const session = await swimmingPta.upsertSession({
        ...input,
        date: format(input.date, "yyyy-MM-dd"),
        placing: input.placing ? +input.placing : null,
        official_time: input.official_time ? +input.official_time : null,
      });
      return session;
    }),
  getReps: protectedProcedure
    .input(
      z.object({
        sessionId: z.string().optional(),
        videoId: z.string().optional(),
      }),
    )
    .query(async ({ input }) => {
      const reps = await swimmingPta.getReps(input);
      return reps;
    }),
  upsertRep: protectedProcedure
    .input(
      z.object({
        ...swimmingRepFormSchema.shape,
        start: z
          .object({
            reaction_time: z.string(),
            stroke_type: z.nativeEnum(SwimmingStrokeCategory),
          })
          .nullish(),
      }),
    )
    .mutation(async ({ input }) => {
      const rep = await swimmingPta.upsertRep({
        ...input,
        piece_number: +input.piece_number,
        set_number: +input.set_number,
        rep_number: +input.rep_number,
        duration: +input.duration,
        distance: +input.distance,
        speed: +input.speed,
        start: input.start
          ? {
              reaction_time: +input.start.reaction_time,
              stroke_type: input.start.stroke_type,
            }
          : null,
      });
      return rep;
    }),
  getLaps: protectedProcedure
    .input(z.object({ repId: z.string() }))
    .query(async ({ input }) => {
      const laps = await swimmingPta.getLap(input);
      return laps;
    }),
  upsertLaps: protectedProcedure
    .input(
      z.object({
        rep: z.object({
          ...swimmingRepFormSchema.shape,
          start: z
            .object({
              reaction_time: z.number(),
              stroke_type: z.string(),
            })
            .nullish(),
          piece_number: z.number(),
          set_number: z.number(),
          rep_number: z.number(),
          duration: z.number(),
          distance: z.number(),
          speed: z.number(),
        }),
        method: z.enum(["POST", "PUT"]),
        laps: z.array(
          z.object({
            lap_number: z.number(),
            stroke_type: z.nativeEnum(SwimmingStrokeCategory),
            lap_distance: z.number(),
            distance: z.number(),
            lap_duration: z.number(),
            duration: z.number(),
            speed: z.number(),
          }),
        ),
      }),
    )
    .mutation(async ({ input }) => {
      const lap = await swimmingPta.upsertLaps(input);
      return lap;
    }),
});
