import type {
  SwimmingLap,
  SwimmingRace,
  SwimmingRep,
  SwimmingSession,
  SwimmingSessionInput,
} from "~/lib/interfaces/swimming";
import { callPta } from "./pta";
import type { SwimCourseType } from "~/lib/enums/swimming";
import { SwimmingSource } from "~/lib/enums/swimming";
import { TRPCError } from "@trpc/server";

export const swimmingPta = {
  getRaces: async ({ competitionId }: { competitionId: string }) => {
    const url = `swimming/races?competition_id=${competitionId}`;
    const res = await callPta<SwimmingRace[]>(url, {
      method: "GET",
    });
    return res;
  },
  upsertRace: async (race: Partial<SwimmingRace>) => {
    let url = `swimming/races`;
    if (race.race_id && race.race_id !== "") {
      url += `/${race.race_id}`;
    }
    const res = await callPta<SwimmingRace>(url, {
      method: race.race_id ? "PUT" : "POST",
      body: JSON.stringify(race),
    });
    return res;
  },
  //session
  getSession: async ({ sessionId }: { sessionId: string }) => {
    const url = `swimming/sessions/${sessionId}`;
    const res = await callPta<SwimmingSession>(url, {
      method: "GET",
    });
    return res;
  },
  getSessions: async ({
    raceId,
    sessionDescription,
  }: {
    raceId?: string;
    sessionDescription?: string;
  }) => {
    let url = `swimming/sessions`;
    const params = new URLSearchParams();
    if (raceId) {
      params.set("race_id", raceId);
    }
    if (sessionDescription && sessionDescription.length > 3) {
      params.set("session_description", sessionDescription);
    }
    url += `?${params.toString()}`;

    const res = await callPta<SwimmingSession[]>(url, {
      method: "GET",
    });
    return res;
  },
  upsertSession: async ({
    sessionId,
    race_id,
    date,
    session_type,
    session_description,
    course_type,
    is_relay,
    relay,
    athleteId,
    start_time,
    placing,
    official_time,
    result_code,
  }: {
    sessionId?: string;
    race_id?: string | null;
    date: string; //yyyy-mm-dd
    session_type: string;
    session_description: string;
    course_type: SwimCourseType;
    is_relay: boolean;
    athleteId?: string | null;
    relay?: string[] | null;
    start_time: string | null; //eg. 2019-08-24T14:15:22Z
    placing: number | null;
    official_time: number | null;
    result_code: string | null;
  }) => {
    let url = "swimming/sessions/";
    if (sessionId) {
      url += sessionId;
    }
    const obj: Partial<SwimmingSessionInput> = {
      race_id,
      date,
      start_time,
      session_type,
      session_description,
      course_type,
      placing,
      official_time,
      result_code,
    };
    if (is_relay) {
      if (relay?.length !== 4) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Relay team must have 4 athletes",
        });
      }
      obj.relay_team = relay.map((id, index) => ({
        athlete_id: id,
        leg_number: index + 1,
      }));
    } else {
      if (!athleteId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Athlete is required",
        });
      }
      obj.athlete_id = athleteId;
    }

    const res = await callPta<SwimmingSession>(url, {
      method: sessionId ? "PUT" : "POST",
      body: JSON.stringify(obj),
    });

    return res;
  },
  //Rep
  getReps: async ({
    sessionId,
    videoId,
  }: {
    sessionId?: string;
    videoId?: string;
  }) => {
    let url = `swimming/reps?include=LAP_SUMMARY_DATA`;
    if (sessionId) {
      url += `&session_id=${sessionId}`;
    }
    if (videoId) {
      url += `&video_id=${videoId}`;
    }
    const res = await callPta<SwimmingRep[]>(url, {
      method: "GET",
    });
    return res;
  },
  getRep: async ({ repId }: { repId: string }) => {
    const url = `swimming/reps/${repId}`;
    const res = await callPta<SwimmingRep>(url, {
      method: "GET",
    });
    return res;
  },
  upsertRep: async ({
    id,
    video_id,
    session_id,
    athlete_id,
    stroke_category,
    start_type,
    race_suit,
    piece_number,
    set_number,
    rep_number,
    duration,
    distance,
    speed,
    start,
  }: SwimmingRep) => {
    let url = "swimming/reps/";
    if (id) {
      url += id;
    }
    const obj: Partial<SwimmingRep> = {
      video_id,
      session_id,
      athlete_id,
      stroke_category,
      start_type,
      source: SwimmingSource.VIDEO_ANALYSIS, //always video analysis
      race_suit,
      piece_number,
      set_number,
      rep_number,
      duration,
      distance,
      speed,
      start,
    };
    const res = await callPta<SwimmingRep>(url, {
      method: id ? "PUT" : "POST",
      body: JSON.stringify(obj),
    });
    return res;
  },
  //Lap
  getLap: async ({ repId }: { repId: string }) => {
    const url = `swimming/reps/${repId}/laps`;
    try {
      const res = await callPta<SwimmingLap[]>(url, {
        method: "GET",
      });
      return res;
    } catch (error) {
      console.log(error);
      return [];
    }
  },
  upsertLaps: async ({
    rep,
    laps,
    method,
  }: {
    rep: SwimmingRep;
    laps: SwimmingLap[];
    method: "POST" | "PUT";
  }) => {
    const url = `swimming/reps/${rep.id}/laps`;
    const obj: Partial<SwimmingLap>[] = laps.map((lap) => {
      return {
        lap_number: lap.lap_number,
        stroke_type: lap.stroke_type,
        lap_distance: lap.lap_distance,
        distance: lap.distance,
        lap_duration: lap.lap_duration,
        duration: lap.duration,
        speed: lap.speed,
        stroke_rate: lap.stroke_rate,
        max_stroke_rate: lap.max_stroke_rate,
        distance_per_stroke: lap.distance_per_stroke,
        stroke_count: lap.stroke_count,
        breath_count: lap.breath_count,
      };
    });
    try {
      await callPta<SwimmingLap[]>(url, {
        method,
        body: JSON.stringify(obj),
      });
    } catch (error) {
      console.error(error);
      await callPta<SwimmingLap[]>(url, {
        method: "PUT",
        body: JSON.stringify(obj),
      });
    }
    //update rep duration
    // const updateRepUrl = `swimming/reps/${rep.id}`;
    // const repDuration = laps[laps.length - 1]?.duration ?? 0;
    // const repSpeed = rep.distance / repDuration;

    // await callPta<SwimmingRep>(updateRepUrl, {
    //   method: "PUT",
    //   body: JSON.stringify({
    //     ...rep,
    //     duration: repDuration,
    //     speed: repSpeed,
    //   }),
    // });
    return laps;
  },
};
