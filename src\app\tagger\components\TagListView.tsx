import { Filter } from "lucide-react";
import { TagList } from "~/app/_components/Tags/TagList";
import { TagListVirtual } from "~/app/_components/Tags/TagListVirtual";
import { Card } from "~/components/Card";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useStore } from "~/hooks/store";
import type { TagUI } from "~/lib/interface";

export const TagListView = ({
  isVirtual,
  height,
  tagOptions,
  onRemoveTag,
}: {
  isVirtual: boolean;
  height?: number;
  tagOptions: TagUI[];
  onRemoveTag: (id: string) => void;
}) => {
  const videoSummary = useStore((state) => state.videoSummary);
  const currentFrame = useStore((state) => state.currentFrame);
  const filteredTags = useStore((state) => state.filteredTags);
  const editingTagId = useStore((state) => state.editingTagId);
  const tagFilterValues = useStore((state) => state.tagFilterValues);
  const setCurrentFrame = useStore((state) => state.setCurrentFrame);
  const setEditingTagId = useStore((state) => state.setEditingTagId);
  const setTagFilterValues = useStore((state) => state.setTagFilterValues);

  const disabled = !videoSummary;

  const onFilterClick = (filterValue: string) => {
    if (tagFilterValues.includes(filterValue)) {
      setTagFilterValues(tagFilterValues.filter((x) => x !== filterValue));
    } else {
      setTagFilterValues([...tagFilterValues, filterValue]);
    }
  };

  const onTagClick = (value: string) => {
    if (!currentFrame) return;
    const newFrame = Math.round(+value);
    setCurrentFrame(newFrame);
  };

  const tagTypes = [
    ...new Set(tagOptions.filter((x) => !!x).map((x) => x.keyword ?? x.value)),
  ];

  return (
    <div className="flex w-full flex-col gap-[5px]">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="ml-auto w-fit cursor-pointer rounded-[5px] border bg-white p-1.5">
            <Filter className="h-3.5 w-3.5 text-seaSalt-k40" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {tagTypes.map((filterValue) => {
            return (
              <DropdownMenuCheckboxItem
                key={filterValue}
                className="capitalize"
                checked={tagFilterValues.includes(filterValue)}
                onClick={() => {
                  onFilterClick(filterValue);
                }}
              >
                {filterValue}
              </DropdownMenuCheckboxItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
      <Card className="p-0">
        {isVirtual && (
          <TagListVirtual
            tags={filteredTags}
            disabled={disabled}
            editingTagId={editingTagId}
            tagOptions={tagOptions}
            onTagClick={onTagClick}
            setEditingTagId={setEditingTagId}
            onRemoveTag={onRemoveTag}
          />
        )}

        {!isVirtual && (
          <TagList
            tags={filteredTags}
            disabled={disabled}
            editingTagId={editingTagId}
            height={height}
            tagOptions={tagOptions}
            onTagClick={onTagClick}
            setEditingTagId={setEditingTagId}
            onRemoveTag={onRemoveTag}
          />
        )}
      </Card>
    </div>
  );
};
