"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogClose,
  <PERSON>alogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { api } from "~/trpc/react";
import { SnowFeatureType } from "~/lib/enums/snow";
import { Trash } from "lucide-react";
import { Input } from "../ui/input";
import { useState } from "react";
import toast from "react-hot-toast";
import { Card } from "../Card";

export const EditSlopeFeature = ({ raceId }: { raceId: string }) => {
  const utils = api.useUtils();
  const [open, setOpen] = useState(false);
  const [deletedFeatures, setDeletedFeatures] = useState<string[]>([]);

  const { data: snowFeatures } = api.snow.getSlopeFeatures.useQuery({ raceId });

  const { mutate, isPending } = api.snow.upsertSlopeFeature.useMutation({
    onSuccess: () => {
      setOpen(false);
    },
    onError: (e) => {
      toast.error(e.message);
    },
  });

  const onAddFeature = (feature: SnowFeatureType) => {
    if (!snowFeatures) return;
    let sections = "";
    if ([SnowFeatureType.jump, SnowFeatureType.rail].includes(feature)) {
      sections = "1";
    }

    const newFeature = {
      id: crypto.randomUUID(),
      featureNumber: snowFeatures.length + 1,
      type: feature,
      sections,
      raceId,
    };
    utils.snow.getSlopeFeatures.setData({ raceId }, (prev) => {
      if (!prev) return [newFeature];
      return [...prev, newFeature];
    });
  };

  const onRemoveFeature = (featureNum: number) => {
    const deletedId = snowFeatures?.find(
      (x) => x.featureNumber === featureNum,
    )?.id;
    if (deletedId && deletedId.length > 0) {
      setDeletedFeatures([...deletedFeatures, deletedId]);
    }
    utils.snow.getSlopeFeatures.setData({ raceId }, (prev) => {
      if (!prev) return;
      return prev
        .filter((x) => x.featureNumber !== featureNum)
        .map((x, i) => {
          return { ...x, featureNumber: i + 1 };
        });
    });
  };

  const onSectionsChange = (featureNum: number, section: string) => {
    utils.snow.getSlopeFeatures.setData({ raceId }, (prev) => {
      if (!prev) return;
      return prev.map((x) => {
        if (x.featureNumber === featureNum) {
          return { ...x, sections: section };
        }
        return x;
      });
    });
  };

  const onSave = () => {
    mutate({
      delete: deletedFeatures,
      upsert: snowFeatures ?? [],
    });
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        if (!isOpen) {
          void utils.snow.getSlopeFeatures.invalidate({ raceId });
        }
      }}
    >
      <DialogTrigger asChild>
        <Button variant="outline" size="xs" className="bg-white">
          Edit Feature
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-xl">
        <DialogHeader>
          <DialogTitle>Edit Feature</DialogTitle>
          <DialogDescription />
        </DialogHeader>
        <div className="flex items-center gap-4">
          <p>Insert</p>
          {Object.values(SnowFeatureType).map((x) => (
            <Button key={x} onClick={() => onAddFeature(x)} size="xs">
              {x}
            </Button>
          ))}
        </div>
        <Card className="border p-4">
          <Table>
            <TableHeader className="bg-background">
              <TableRow>
                <TableHead className="w-12"></TableHead>
                <TableHead className="w-20">Number</TableHead>
                <TableHead className="">Type</TableHead>
                <TableHead className="w-32">Sections</TableHead>
                <TableHead>Insert</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {snowFeatures?.map((feature) => {
                return (
                  <TableRow key={feature.featureNumber}>
                    <TableCell>
                      <Trash
                        className="h-4 w-4 cursor-pointer"
                        onClick={() => onRemoveFeature(feature.featureNumber)}
                      />
                    </TableCell>
                    <TableCell>{feature.featureNumber}</TableCell>
                    <TableCell>{feature.type}</TableCell>
                    <TableCell>
                      <Input
                        disabled={feature.type === SnowFeatureType.jump}
                        className="w-full uppercase"
                        value={feature.sections ?? ""}
                        onChange={(e) => {
                          const value = e.target.value.toLowerCase();
                          //rail need a number
                          if (feature.type === SnowFeatureType.rail) {
                            if (isNaN(Number(value))) return;
                          }
                          //transition need r, j, t
                          if (feature.type === SnowFeatureType.transition) {
                            if (!/^[rjt]+$/.test(value) && value.length > 0)
                              return;
                          }
                          onSectionsChange(
                            feature.featureNumber,
                            e.target.value,
                          );
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      {feature.type === SnowFeatureType.transition && (
                        <div className="flex gap-4">
                          {["j", "t", "r"].map((x) => (
                            <button
                              key={x}
                              className="uppercase"
                              onClick={() =>
                                onSectionsChange(
                                  feature.featureNumber,
                                  feature.sections + x,
                                )
                              }
                            >
                              {x}
                            </button>
                          ))}
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
          <div className="flex justify-end gap-4">
            <DialogClose asChild>
              <Button>Cancel</Button>
            </DialogClose>
            <Button loading={isPending} onClick={onSave}>
              Save
            </Button>
          </div>
        </Card>
      </DialogContent>
    </Dialog>
  );
};
