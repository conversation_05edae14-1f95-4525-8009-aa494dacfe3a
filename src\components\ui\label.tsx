"use client";

import * as React from "react";
import * as LabelPrimitive from "@radix-ui/react-label";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "~/lib/utils";

const labelVariants = cva(
  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
);

const Label = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &
    VariantProps<typeof labelVariants> & {
      text?: string | number;
      textClassName?: string;
    }
>(({ className, text, textClassName, ...props }, ref) => (
  <div className="flex items-center justify-between gap-3 text-black/60">
    <LabelPrimitive.Root
      ref={ref}
      className={cn(labelVariants(), className)}
      {...props}
    />
    {text && <p className={textClassName}>{text}</p>}
  </div>
));
Label.displayName = LabelPrimitive.Root.displayName;

export { Label };
