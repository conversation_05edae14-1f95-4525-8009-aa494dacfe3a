import { sql } from "drizzle-orm";
import { type NextRequest, NextResponse } from "next/server";
import type { CreateBodyDataRequest } from "~/lib/interfaces/keypoint";
import { BodyDataSchema } from "~/server/api/utils/apiInputs";
import { returnError } from "~/server/api/utils/returnError";
import { db } from "~/server/db";
import { bodyAngles, bodyKeypoints } from "~/server/db/schema";
import { getPoseDataWithBuffering } from "~/server/api/utils/poseData";

export const maxDuration = 300;

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const poseData = await getPoseDataWithBuffering(params.id);

    return NextResponse.json(poseData);
  } catch (error) {
    return returnError(error);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const body = (await request.json()) as CreateBodyDataRequest;

    const result = BodyDataSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: result.error.format(),
        },
        { status: 400 },
      );
    }

    const validatedData = result.data;
    const angles = validatedData.angles;

    if (angles && angles.length > 0) {
      await db
        .insert(bodyAngles)
        .values(angles.map((x) => ({ ...x, videoId: params.id })))
        .onDuplicateKeyUpdate({
          set: {
            angle: sql`values(${bodyAngles.angle})`,
            aiScore: sql`values(${bodyAngles.aiScore})`,
          },
        });
    }
    console.log("angles inserted");

    const keypoints = validatedData.keypoints;
    if (keypoints && keypoints.length > 0) {
      await db
        .insert(bodyKeypoints)
        .values(keypoints.map((x) => ({ ...x, videoId: params.id })))
        .onDuplicateKeyUpdate({
          set: {
            x: sql`values(${bodyKeypoints.x})`,
            y: sql`values(${bodyKeypoints.y})`,
            z: sql`values(${bodyKeypoints.z})`,
            aiScore: sql`values(${bodyKeypoints.aiScore})`,
          },
        });
    }
    console.log("keypoints inserted");

    return NextResponse.json(
      {
        message: "Body data created successfully",
      },
      { status: 201 },
    );
  } catch (error) {
    return returnError(error);
  }
}
