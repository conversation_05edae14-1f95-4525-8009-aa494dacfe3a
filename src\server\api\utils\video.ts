import { TRPCError } from "@trpc/server";
import { unstable_cache } from "next/cache";
import { env } from "~/env";
import { SortBy, type Sport, type VideoStatus } from "~/lib/enums/enums";
import type { Video } from "~/lib/interface";
import { getServerAuthSession } from "../../auth";
import { getServiceToken } from "./service";

export const getVideoSummary = async (id: string, token?: string) => {
  const session = await getServerAuthSession();
  const url = `${env.NEXT_PUBLIC_VIDEO_PORTAL_URL}/api/v1/video/${id}`;

  const res = await fetch(url, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + (token ?? session?.accessToken),
    },
  });
  const video = (await res.json()) as Video | undefined;
  if (!video) {
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to fetch video summary",
    });
  }
  return video;
};

export const getVideoSummaryCache = unstable_cache(
  async (id: string) => {
    return await getVideoSummary(id);
  },
  [],
  {
    tags: ["videoSummary"],
    revalidate: 30 * 60, //30 minutes
  },
);

export const callVideoPortal = async <T>(
  endpoint: string,
  {
    method,
    body,
    token,
  }: {
    method: "PUT" | "POST" | "GET" | "DELETE";
    body?: string;
    token?: string;
  },
): Promise<T> => {
  try {
    const tokenToUse = token ?? (await getServiceToken()).access_token;
    const url = `${env.NEXT_PUBLIC_VIDEO_PORTAL_URL}/api/v1/video/${endpoint}`;

    const res = await fetch(url, {
      method,
      headers: {
        Authorization: `Bearer ${tokenToUse}`,
        "Content-Type": "application/json",
      },
      body,
    });

    if (!res.ok) {
      throw new Error(`HTTP error: status: ${res.status}. ${await res.text()}`);
    }
    const json = (await res.json()) as T;
    return json;
  } catch (error) {
    let message = "Failed to call Video portal";
    if (error instanceof Error) {
      message = error.message;
    }
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message,
    });
  }
};

export const getVideos = async ({
  token,
  sport,
  searchText,
  status,
  sortBy,
  page,
}: {
  token: string;
  sport?: Sport[];
  searchText?: string;
  status?: VideoStatus;
  sortBy?: SortBy;
  page?: number;
}) => {
  try {
    let url = `${env.NEXT_PUBLIC_VIDEO_PORTAL_URL}/api/v1/video?`;

    const params = new URLSearchParams();
    if (status) {
      params.set("status", status);
    }

    params.set("sortBy", sortBy ?? SortBy.video_date);
    params.set("pageSize", "6");
    if (page) {
      params.set("page", page.toString());
    }

    if (sport) {
      sport.forEach((s) => {
        params.append("sports", s);
      });
    }
    if (searchText) {
      params.append("searchText", searchText);
    }
    url += params.toString();

    const response = await fetch(url, {
      headers: {
        Authorization: "Bearer " + token,
      },
    });

    const data = (await response.json()) as { count: number; list: Video[] };
    return data;
  } catch (error) {
    console.log(error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to fetch AI tagged videos",
    });
  }
};

export const updateVideoStatus = async (videoId: string, status = "Tagged") => {
  return await callVideoPortal<{ rowsAffected: number }>(`${videoId}/status`, {
    method: "PUT",
    body: JSON.stringify({ status }),
  });
};
