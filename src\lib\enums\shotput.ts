import type { TagUI } from "../interface";

export enum ShotputMovement {
  Rotation = "rotation",
  Glide = "glide",
}

export const shotputMovements = Object.values(ShotputMovement) as [
  ShotputMovement,
  ...ShotputMovement[],
];

export const shotputMovementOptions = [
  {
    value: ShotputMovement.Rotation,
    key: "z",
  },
  {
    value: ShotputMovement.Glide,
    key: "x",
  },
];

export enum ShotputType {
  Full = "full",
  HalfTurn = "half turn",
  SouthAfrican = "south african",
  Standing = "standing",
  DoubleStance = "double stance",
  StepThrough = "step through",
}

export const shotputTypes = Object.values(ShotputType) as [
  ShotputType,
  ...ShotputType[],
];

export const shotputTypeOptions = [
  {
    value: ShotputType.Full,
    key: "q",
  },
  {
    value: ShotputType.SouthAfrican,
    key: "w",
  },
  {
    value: ShotputType.DoubleStance,
    key: "e",
  },
  {
    value: ShotputType.HalfTurn,
    key: "a",
  },
  {
    value: ShotputType.Standing,
    key: "s",
  },
  {
    value: ShotputType.StepThrough,
    key: "d",
  },
];

export enum ShotputHand {
  RightHand = "right hand",
  LeftHand = "left hand",
}
export const shotputHands = Object.values(ShotputHand) as [
  ShotputHand,
  ...ShotputHand[],
];

export const shotputHandOptions = [
  {
    value: ShotputHand.LeftHand,
    key: "c",
  },
  {
    value: ShotputHand.RightHand,
    key: "v",
  },
];

export enum ShotputTag {
  RotationStart = "Rotation Start",
  RightToeOff = "Right Toe Off",
  LeftToeOffBack = "Left Toe Off Back",
  RightFootDown = "Right Foot Down",
  DoubleStance = "Double Stance",
  RearFootTakeOff = "Rear Foot Take Off",
  FrontFootTakeOff = "Front Foot Take Off",
  Release = "Release",
  PostRelease = "Post Release",
}

export const shotputTags: { "key phase": TagUI[] } = {
  "key phase": [
    {
      value: ShotputTag.RotationStart,
      key: "1",
      className: "bg-neonGreen-20 text-neonGreen-k40",
    },
    {
      value: ShotputTag.RightToeOff,
      key: "2",
      className: "bg-blue-20 text-blue-k40",
    },
    {
      value: ShotputTag.LeftToeOffBack,
      key: "3",
      className: "bg-fuchsia-20 text-fuchsia-k40",
    },
    {
      value: ShotputTag.RightFootDown,
      key: "4",
      className: "bg-orange-20 text-orange-k40",
    },
    undefined,
    {
      value: ShotputTag.DoubleStance,
      key: "5",
      className: "bg-purple-20 text-purple-k40",
    },
    {
      value: ShotputTag.RearFootTakeOff,
      key: "6",
      className: "bg-yellow-20 text-yellow-k40",
    },
    {
      value: ShotputTag.FrontFootTakeOff,
      key: "7",
      className: "bg-red-20 text-red-k40",
    },
    {
      value: ShotputTag.Release,
      key: "8",
      className: "bg-springGreen-20 text-springGreen-k40",
    },
    {
      value: ShotputTag.PostRelease,
      key: "9",
      className: "bg-neonGreen-k40 text-neonGreen-20",
    },
  ],
};
