"use client";

import { usePathname } from "next/navigation";
import { ChevronRight } from "lucide-react";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from "~/components/ui/breadcrumb";
import React from "react";
import Link from "next/link";

const generateBreadcrumbs = (pathname: string) => {
  const baseBreadcrumb = { href: "/", label: "Sports Analysis Tool" };

  if (pathname === "/") {
    return [baseBreadcrumb];
  }

  if (pathname === "/validation") {
    return [
      baseBreadcrumb,
      { href: "/validation", label: "Validation Tool" },
      { href: "/validation", label: "Video Queue" },
    ];
  }

  if (pathname.startsWith("/validation/")) {
    return [
      baseBreadcrumb,
      { href: "/validation", label: "Validation Tool" },
      { href: "/validation", label: "Video Queue" },
      { href: pathname, label: "Video Review" },
    ];
  }

  if (pathname === "/tagger") {
    return [
      baseBreadcrumb,
      { href: "/tagger", label: "Tagger Tool" },
      { href: "/tagger", label: "Video Queue" },
    ];
  }

  if (pathname.startsWith("/tagger/")) {
    return [
      baseBreadcrumb,
      { href: "/tagger", label: "Tagger Tool" },
      { href: "/tagger", label: "Video Queue" },
      { href: pathname, label: "Video Tagging" },
    ];
  }

  return [baseBreadcrumb];
};

export function BreadcrumbNav() {
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname);

  if (!breadcrumbs.length) return null;

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbs.map((breadcrumb, index) => (
          <React.Fragment key={breadcrumb.label}>
            <BreadcrumbItem>
              {index === breadcrumbs.length - 1 ? (
                <BreadcrumbPage className="text-sm font-medium text-gray-900">
                  {breadcrumb.label}
                </BreadcrumbPage>
              ) : (
                <BreadcrumbLink asChild>
                  <Link
                    className="text-sm text-gray-600 hover:text-gray-900"
                    href={breadcrumb.href}
                  >
                    {breadcrumb.label}
                  </Link>
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
            {index < breadcrumbs.length - 1 && (
              <BreadcrumbSeparator>
                <ChevronRight className="h-4 w-4 text-gray-400" />
              </BreadcrumbSeparator>
            )}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
