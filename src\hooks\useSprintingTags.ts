import { useParams } from "next/navigation";
import { useEffect, useMemo } from "react";
import { toast } from "react-hot-toast";
import { sprintTags } from "~/lib/enums/sprint";
import { getFilteredTags } from "~/lib/utils";
import { useStore } from "~/hooks/store";
import { api } from "~/trpc/react";
import { Sport } from "~/lib/enums/enums";

// Helper function to check if a tag is a custom distance (numeric value)
const isCustomDistance = (tag: string): boolean => {
  return !isNaN(Number(tag));
};

export const useSprintingTags = () => {
  const utils = api.useUtils();

  const params = useParams<{ id: string }>();
  const videoId = params.id;

  const currentFrame = useStore((state) => state.currentFrame);
  const filteredTags = useStore((state) => state.filteredTags);
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const sprintingRace = useStore((state) => state.sprintingRace);
  const setFilteredTags = useStore((state) => state.setFilteredTags);

  const { data: tags, isLoading } = api.sprint.getTags.useQuery(
    { id: videoId },
    { enabled: !!videoId },
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const raceTag =
    tags?.tags
      .filter((x) => x.raceId === sprintingRace?.id)
      .map((tag) => ({
        ...tag,
        videoId: videoId,
        athleteId: selectedAthlete ?? "",
        type: "race",
      })) ?? [];

  useEffect(() => {
    if (!sprintingRace?.strides || !sprintingRace?.id) return;

    const strideTags = sprintingRace.strides.flatMap((stride) => [
      {
        id: stride.id.toString(),
        type: "stride",
        tag: "Heel Contact",
        frame: stride.heelContact,
        athleteId: selectedAthlete ?? "",
        videoId: videoId,
        raceId: sprintingRace.id,
        userId: stride.userId,
      },
      {
        id: stride.id.toString(),
        type: "stride",
        tag: "Toe Off",
        frame: stride.toeOff,
        athleteId: selectedAthlete ?? "",
        videoId: videoId,
        raceId: sprintingRace.id,
        userId: stride.userId,
      },
    ]);

    const allTags = [...raceTag, ...strideTags];

    if (JSON.stringify(allTags) !== JSON.stringify(filteredTags)) {
      setFilteredTags(allTags);
    }
  }, [
    raceTag,
    filteredTags,
    setFilteredTags,
    sprintingRace?.strides,
    sprintingRace?.id,
    selectedAthlete,
    videoId,
  ]);

  const { mutate: upsertTag } = api.sprint.upsertTag.useMutation({
    onSuccess: (_data) => {
      toast.success("Tag saved successfully");
      // Force refetch the data to ensure UI is updated
      void utils.sprint.getTags.invalidate({ id: videoId });
    },
    onError: (error) => {
      console.error("Error in upsertTag:", error);
      toast.error(`Failed to add tag: ${error.message}`);
      void utils.sprint.getTags.invalidate({ id: videoId });
    },
  });

  const onAddTag = (tag: string, frame?: number, customValue?: number) => {
    if (!sprintingRace || !tag) {
      toast.error("Missing race or tag");
      return;
    }

    try {
      const frameToUse = frame ?? currentFrame;

      // Check if the tag is a custom distance (numeric value)
      if (isCustomDistance(tag)) {
        // This is a custom distance value (e.g., "75")
        // First check if there's a tag with the same value at the exact same frame
        const existingTagAtSameFrame = raceTag.find(
          (x) => x.frame === frameToUse,
        );

        // If we have a tag at the same frame, update it regardless of its value
        if (
          existingTagAtSameFrame &&
          isCustomDistance(existingTagAtSameFrame.tag)
        ) {
          const payload = {
            id: existingTagAtSameFrame.id,
            sprintingRaceId: sprintingRace.id,
            athleteId: selectedAthlete,
            tag, // Update with the new value
            frame: frameToUse,
          };

          upsertTag(payload);
        } else {
          // Otherwise, check if there's a tag with the same value (but at a different frame)
          const existingTagWithSameValue = raceTag.find((x) => x.tag === tag);

          const payload = {
            id: customValue
              ? String(customValue)
              : existingTagWithSameValue
                ? existingTagWithSameValue.id
                : undefined,
            sprintingRaceId: sprintingRace.id,
            athleteId: selectedAthlete,
            tag, // Store the actual distance value as the tag
            frame: frameToUse,
          };

          upsertTag(payload);
        }
      } else {
        // Standard behavior for all other tags - find and update existing tag
        const existingTag = raceTag.find((x) => x.tag === tag);

        const payload = {
          id: existingTag ? existingTag.id : undefined,
          sprintingRaceId: sprintingRace.id,
          athleteId: selectedAthlete,
          tag,
          frame: frameToUse,
        };

        upsertTag(payload);
      }
    } catch (error) {
      console.error("Error in onAddTag:", error);
      toast.error("Failed to add tag");
    }
  };

  const { mutate: deleteTag } = api.sprint.deleteTag.useMutation({
    onError: (error) => {
      console.error(error);
      toast.error("Failed to delete tag");
      void utils.sprint.getTags.invalidate({ id: videoId });
    },
  });

  const { mutate: deleteTags } = api.sprint.deleteTags.useMutation({
    onError: (error) => {
      console.error(error);
      toast.error("Failed to delete tags");
      void utils.sprint.getTags.invalidate({ id: videoId });
    },
  });

  const onDeleteTag = (id: string) => {
    deleteTag({ id });
    utils.sprint.getTags.setData({ id: videoId }, (data) => {
      if (!data) return undefined;
      return {
        ...data,
        tags: data.tags.filter((x) => x.id !== id),
      };
    });
  };

  const onDeleteTags = (ids: string[]) => {
    deleteTags({ ids });
    utils.sprint.getTags.setData({ id: videoId }, (data) => {
      if (!data) return undefined;
      return {
        ...data,
        tags: data.tags.filter((x) => !ids.includes(x.id)),
      };
    });
  };

  const { mutate: deleteStrides } = api.sprint.deleteStrides.useMutation({
    onError: () => {
      toast.error("Failed to delete stride");
      void utils.sprint.getRace.invalidate();
    },
  });

  const onDeleteStrides = (ids: string[]) => {
    if (!sprintingRace) return;
    deleteStrides({ ids });
    utils.sprint.getRace.setData({ videoId }, (oldData) => {
      if (!oldData) return oldData;
      return {
        ...oldData,
        strides: oldData.strides.filter(
          (stride) => !ids.includes(stride.id.toString()),
        ),
      };
    });
  };

  return {
    tags,
    isLoading,
    onAddTag,
    onDeleteTag,
    onDeleteTags,
    onDeleteStrides,
  };
};
