import { usePathname, useRouter, useSearchParams } from "next/navigation";

export function useStatusFilter({ filterKey }: { filterKey: string }) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());

  const filterValues = searchParams.getAll(filterKey);

  const onFilterClick = (filterValue: string) => {
    let newValues: string[] = [];
    const oldValues = params.getAll(filterKey);

    if (oldValues.includes(filterValue)) {
      newValues = oldValues.filter((v) => v !== filterValue);
    } else {
      newValues = [...oldValues, filterValue];
    }

    params.delete(filterKey);
    params.delete("page");
    newValues.forEach((v) => {
      params.append(filterKey, v);
    });

    router.push(`${pathname}?${params.toString()}`);
  };

  return { filterValues, onFilterClick };
}
