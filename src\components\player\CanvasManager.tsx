/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import type React from "react";

import { useState, useRef, useEffect } from "react";
import {
  drawCurrentLine,
  drawLines,
  drawTagBoundingBox,
} from "~/lib/drawingUtilts";
import type {
  CanvasManagerHandlers,
  CanvasManagerProps,
} from "~/lib/interfaces/canvasTypes";
import { type CurrentLine } from "~/lib/interfaces/drawingTypes";

import { drawPoseOverlay } from "~/lib/poseRenderer";

export function CanvasManager({
  canvasRef,
  currentFrame,
  filteredTags,
  selectedAthlete,
  showPose,
  showAngles,
  currentFramePose,
  firstSegment,
  lines,
  setLines,
  drawingMode,
  selectedColour,
  isCtrlPressed,
  strokeWidth = 2,
  poseCircleThickness = 4,
  poseLineThickness = 2,
}: CanvasManagerProps): CanvasManagerHandlers {
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentLine, setCurrentLine] = useState<CurrentLine | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  // Handle drawing interactions
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isCtrlPressed) return;

    const canvas = canvasRef.current!;
    const rect = canvas.getBoundingClientRect();

    // Normalize mouse coordinates (0 to 1)
    const x = (e.clientX - rect.left) / rect.width;
    const y = (e.clientY - rect.top) / rect.height;

    setIsDrawing(true);
    setCurrentLine({
      startX: x,
      startY: y,
      endX: x,
      endY: y,
      strokeWidth,
    });
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !isCtrlPressed || !currentLine) return;

    const canvas = canvasRef.current!;
    const rect = canvas.getBoundingClientRect();

    // Normalize mouse coordinates (0 to 1)
    const x = (e.clientX - rect.left) / rect.width;
    const y = (e.clientY - rect.top) / rect.height;

    setCurrentLine({
      ...currentLine,
      endX: x,
      endY: y,
      strokeWidth,
    });
  };

  const handleMouseUp = () => {
    if (isDrawing && currentLine) {
      setLines([
        ...lines,
        {
          ...currentLine,
          color: selectedColour,
          strokeWidth: currentLine.strokeWidth ?? strokeWidth,
        },
      ]);
      setCurrentLine(null);
    }
    setIsDrawing(false);
  };

  // Draw on canvas
  useEffect(() => {
    const canvas = canvasRef.current!;
    const ctx = canvas.getContext("2d")!;
    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;

    // Get the display dimensions (the actual size the canvas is rendered at in the DOM)
    const displayWidth = canvas.clientWidth;
    const displayHeight = canvas.clientHeight;

    // Calculate the scale ratio between the canvas dimensions and display dimensions
    const displayScaleX = displayWidth / canvasWidth;
    const displayScaleY = displayHeight / canvasHeight;

    // Clear the entire canvas
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // Set up scaling transformation to ensure proper rendering
    ctx.save();
    ctx.scale(1 / displayScaleX, 1 / displayScaleY);

    // Draw bounding box if there's a current tag
    const currentTag = filteredTags.find(
      (x) => x.frame === currentFrame && x.athleteId === selectedAthlete,
    );

    if (currentTag) {
      drawTagBoundingBox(
        ctx,
        currentTag,
        displayWidth,
        displayHeight,
        displayScaleX,
        displayScaleY,
      );
    }

    // Draw pose overlay (keypoints and skeleton) for current frame
    if (showPose && currentFramePose.keypoints.length > 0) {
      const ffmpegWidth = firstSegment?.width;
      const ffmpegHeight = firstSegment?.height;

      const videoElement = videoRef.current;
      const videoWidth = ffmpegWidth ?? videoElement?.videoWidth ?? canvasWidth;
      const videoHeight =
        ffmpegHeight ?? videoElement?.videoHeight ?? canvasHeight;

      drawPoseOverlay(
        ctx,
        currentFramePose.keypoints,
        showAngles ? currentFramePose.angles : [],
        displayWidth,
        displayHeight,
        displayScaleX,
        displayScaleY,
        videoWidth,
        videoHeight,
        poseCircleThickness,
        poseLineThickness,
      );
    }

    // Draw all saved lines
    drawLines(
      ctx,
      lines,
      displayWidth,
      displayHeight,
      displayScaleX,
      displayScaleY,
      drawingMode,
    );

    // Draw the current line being drawn
    drawCurrentLine(
      ctx,
      currentLine,
      selectedColour,
      displayWidth,
      displayHeight,
      displayScaleX,
      displayScaleY,
      drawingMode,
    );

    // Restore the canvas context to its original state
    ctx.restore();
  }, [
    currentFrame,
    filteredTags,
    showPose,
    showAngles,
    lines,
    currentLine,
    currentFramePose,
    firstSegment,
    drawingMode,
    selectedColour,
    selectedAthlete,
    poseCircleThickness,
    poseLineThickness,
  ]);

  return {
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
  };
}
