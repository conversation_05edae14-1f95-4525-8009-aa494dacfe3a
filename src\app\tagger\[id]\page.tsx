import { TRPCError } from "@trpc/server";
import { ErrorBox } from "~/app/_components/ErrorBox";
import { api, HydrateClient } from "~/trpc/server";
import { Tagger } from "../components/Tagger";

export const dynamic = "force-dynamic";
export const maxDuration = 60;

export default async function TaggerVideoHome({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const id = (await params).id;

  if (!id) {
    return <div>Invalid video ID</div>;
  }

  try {
    void api.videos.getVideoSourceStream.prefetch({ id });
    const videoSummary = await api.videos.getVideoInfo({ id });
    const poseData = await api.pose.getAllPoseDataByVideo({ videoId: id });

    // Log the size of poseData
    // const dataSize = JSON.stringify(poseData).length;
    // const dataSizeKB = (dataSize / 1024).toFixed(2);
    // const dataSizeMB = (dataSize / (1024 * 1024)).toFixed(2);
    // console.log(
    //   `PoseData size: ${dataSize} bytes (${dataSizeKB} KB, ${dataSizeMB} MB)`,
    // );

    return (
      <HydrateClient>
        <main className="h-full">
          <Tagger videoSummary={videoSummary} poseData={poseData} />
        </main>
      </HydrateClient>
    );
  } catch (error) {
    console.error(error);
    if (error instanceof TRPCError) {
      return <ErrorBox code={error.code} message={error.message} />;
    } else {
      return <div>An unexpected error occurred</div>;
    }
  }
}
