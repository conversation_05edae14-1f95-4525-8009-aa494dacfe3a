import { useHotkeys } from "react-hotkeys-hook";
import { AddTagPannel } from "~/app/validation/components/AddTagPannel";
import { Card } from "~/components/Card";
import { RepSelector } from "~/components/swimming/RepSelector";
import { useStore } from "~/hooks/store";
import { useSwimmingTags } from "~/hooks/useSwimmingTags";
import { SwimmingTag, swimmingTags } from "~/lib/enums/swimming";
import { TagListView } from "./TagListView";

export const SwimmingForm = () => {
  const { tags, onAddTag, onDeleteTag } = useSwimmingTags();

  const currentFrame = useStore((state) => state.currentFrame);
  const editingTagId = useStore((state) => state.editingTagId);
  const setEditingTagId = useStore((state) => state.setEditingTagId);

  const isVirtual = !!tags && tags.tags.length >= 1000;

  useHotkeys("1", () => onAddTag(SwimmingTag["5m"]));
  useHotkeys("2", () => onAddTag(SwimmingTag["15m"]));
  useHotkeys("3", () => onAddTag(SwimmingTag["25m"]));
  useHotkeys("4", () => onAddTag(SwimmingTag["35m"]));
  useHotkeys("5", () => onAddTag(SwimmingTag["45m"]));

  useHotkeys("q", () => onAddTag(SwimmingTag.Gun));
  useHotkeys("w", () => onAddTag(SwimmingTag.Touch));
  useHotkeys("e", () => onAddTag(SwimmingTag.StrokeCount));
  useHotkeys("r", () => onAddTag(SwimmingTag.StrokeCycle));
  useHotkeys("a", () => onAddTag(SwimmingTag.ToeOff));
  useHotkeys("s", () => onAddTag(SwimmingTag.UW));
  useHotkeys("d", () => onAddTag(SwimmingTag.Swim));
  useHotkeys("f", () => onAddTag(SwimmingTag.Breath));
  useHotkeys("enter", () => {
    if (editingTagId) {
      setEditingTagId(null);
    }
  });

  return (
    <>
      <RepSelector />
      <Card className="flex-row gap-[5px]">
        <p className="w-[53px] text-smallLabel text-black/60">Frame:</p>
        <p className="text-smallLabel font-bold">{currentFrame}</p>
      </Card>
      <TagListView
        isVirtual={isVirtual}
        height={345}
        tagOptions={[...swimmingTags.distance, ...swimmingTags.movement]}
        onRemoveTag={onDeleteTag}
      />
      <AddTagPannel tags={swimmingTags} onAddTag={onAddTag} />
    </>
  );
};
