"use client";

import { Checkbox } from "~/components/ui/check-box";
import { Label } from "~/components/ui/label";

interface PoseOverlayToggleProps {
  onChange: (showPose: boolean) => void;
  value?: boolean;
  defaultEnabled?: boolean;
}

export const PoseOverlayToggle = ({
  onChange,
  value,
  defaultEnabled = true,
}: PoseOverlayToggleProps) => {
  return (
    <div className="flex items-center space-x-2">
      <Checkbox
        id="pose-overlay-toggle"
        checked={value ?? defaultEnabled}
        onCheckedChange={(checked) => onChange(checked === true)}
      />
      <Label htmlFor="pose-overlay-toggle" className="text-xs text-black/60">
        Show Pose
      </Label>
    </div>
  );
};

interface AngleOverlayToggleProps {
  onChange: (showAngles: boolean) => void;
  value?: boolean;
  defaultEnabled?: boolean;
}

export const AngleOverlayToggle = ({
  onChange,
  value,
  defaultEnabled = true,
}: AngleOverlayToggleProps) => {
  return (
    <div className="flex items-center space-x-2">
      <Checkbox
        id="angle-overlay-toggle"
        checked={value ?? defaultEnabled}
        onCheckedChange={(checked) => onChange(checked === true)}
      />
      <Label htmlFor="angle-overlay-toggle" className="text-xs text-black/60">
        Show Angles
      </Label>
    </div>
  );
};

interface VideoToggleProps {
  onChange: (showVideo: boolean) => void;
  value?: boolean;
  defaultEnabled?: boolean;
}

export const VideoToggle = ({
  onChange,
  value,
  defaultEnabled = true,
}: VideoToggleProps) => {
  return (
    <div className="flex items-center space-x-2">
      <Checkbox
        id="video-toggle"
        checked={value ?? defaultEnabled}
        onCheckedChange={(checked) => onChange(checked === true)}
      />
      <Label htmlFor="video-toggle" className="text-xs text-black/60">
        Show Video
      </Label>
    </div>
  );
};
