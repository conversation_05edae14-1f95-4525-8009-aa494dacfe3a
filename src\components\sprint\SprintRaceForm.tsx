"use client";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Card } from "~/components/Card";
import type { z } from "zod";
import { FormItemType, Gender, genders } from "~/lib/enums/enums";
import { Form } from "~/components/ui/form";
import { FormItem, type FormItemProps } from "~/components/FormItem";
import { useStore } from "~/hooks/store";
import { api } from "~/trpc/react";
import { sprintRaceSchema } from "~/lib/schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  SprintingRaceType,
  sprintingRaceTypes,
  sprintingSprintDistances,
  sprintingHurdlesDistances,
  sprintingMiddleDistances,
  sprintingRounds,
  SprintingRound,
  SprintingDistance,
} from "~/lib/enums/sprint";
import { Button } from "~/components/ui/button";
import { toast } from "react-hot-toast";
import crypto from "crypto";
import { TaggerSprintStride } from "./TaggerSprintStride";

interface SprintRaceTypeFormProps extends FormItemProps {
  name: keyof SprintRaceProps;
}

export type SprintRaceProps = z.infer<typeof sprintRaceSchema>;

const initialFormData = {
  name: "",
  eventGroup: SprintingRaceType.Sprint,
  distance: SprintingDistance["100m"],
  round: SprintingRound.Heat,
  position: "",
  time: "",
  track: "",
  gender: Gender.Men,
  date: "",
  wind: "",
};

const getAvailableDistances = (eventGroup: SprintingRaceType) => {
  switch (eventGroup) {
    case SprintingRaceType.Sprint:
      return sprintingSprintDistances;
    case SprintingRaceType.Hurdles:
      return sprintingHurdlesDistances;
    case SprintingRaceType.MiddleDistance:
      return sprintingMiddleDistances;
    default:
      return [];
  }
};

export const SprintRaceForm = ({ race }: { race?: SprintRaceProps }) => {
  const utils = api.useUtils();
  const params = useParams();
  const videoId = params.id as string;

  const setSprintingRace = useStore((state) => state.setSprintingRace);
  const [availableDistances, setAvailableDistances] = useState<string[]>([]);

  const { data: sprintRace } = api.sprint.getRace.useQuery({
    videoId,
  });

  const form = useForm<SprintRaceProps>({
    resolver: zodResolver(sprintRaceSchema),
    defaultValues: { ...initialFormData },
  });

  const { mutate: upsertRaceForm, isPending } =
    api.sprint.upsertRace.useMutation({
      onSuccess: (result) => {
        setSprintingRace({
          ...result,
          id: result.id,
          position: +result.position,
          wind: +result.wind,
          strides: sprintRace?.strides ?? [],
        });
        void utils.sprint.getRace.invalidate();
        toast.success(
          race ? "Race updated successfully" : "Race added successfully",
        );
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });

  // Watch for event group changes to update available distances
  const eventGroup = form.watch("eventGroup") as SprintingRaceType;

  // Update available distances when event group changes
  useEffect(() => {
    if (eventGroup) {
      const newDistances = getAvailableDistances(eventGroup);
      setAvailableDistances(newDistances);

      // Only clear the distance if event group has a value and the current distance isn't in the new list
      const currentDistance = form.getValues("distance");
      if (currentDistance && !newDistances.includes(currentDistance)) {
        form.setValue("distance", newDistances[0]!);
      }
    }
  }, [eventGroup, form]);

  // Handle initial form population from race data
  useEffect(() => {
    if (race) {
      // First, set the available distances based on the race's event group
      if (race.eventGroup) {
        const distances = getAvailableDistances(race.eventGroup);
        setAvailableDistances(distances);
      }

      // Update the form values with the race data
      form.reset({
        id: race.id,
        name: race.name,
        eventGroup: race.eventGroup,
        distance: race.distance,
        round: race.round,
        position: race.position,
        time: race.time,
        track: race.track,
        gender: race.gender,
        date: race.date,
        wind: race.wind,
      });

      setSprintingRace({
        ...race,
        videoId,
        id: race.id ?? crypto.randomUUID(),
        eventGroup: race.eventGroup as SprintingRaceType,
        round: race.round as SprintingRound,
        gender: race.gender,
        position: +race.position,
        wind: +race.wind,
        strides: sprintRace?.strides ?? [],
      });
    }
  }, [race, form, setSprintingRace, videoId, sprintRace]);

  const onAddRace = () => {
    if (!videoId) {
      toast.error("No video ID provided");
      return;
    }

    // Validate form before submission
    void form.handleSubmit(
      (formValues) => {
        const currentRace = useStore.getState().sprintingRace;

        upsertRaceForm({
          id: currentRace?.id,
          videoId,
          name: formValues.name,
          eventGroup: formValues.eventGroup,
          distance: formValues.distance,
          round: formValues.round,
          position: formValues.position,
          time: formValues.time,
          track: formValues.track,
          gender: formValues.gender as Gender,
          date: formValues.date,
          wind: formValues.wind,
        });
      },
      (errors) => {
        // Show error toast for form validation errors
        const errorMessage = Object.values(errors)
          .map((error) => error.message)
          .join(", ");
        toast.error(errorMessage || "Please check your form inputs");
      },
    )();
  };

  const SprintFormItems: SprintRaceTypeFormProps[] = [
    {
      name: "name",
      title: "Name",
      type: FormItemType.text,
    },
    {
      name: "eventGroup",
      title: "Event Group",
      placeholder: "Select Event Group",
      type: FormItemType.select,
      options: sprintingRaceTypes.map((x) => ({
        label: x,
        value: x,
      })),
    },
    {
      name: "distance",
      title: "Distance",
      type: FormItemType.select,
      placeholder: "Select Distance",
      options: availableDistances.map((distance) => ({
        label: distance,
        value: distance,
      })),
    },
    {
      name: "round",
      title: "Round",
      type: FormItemType.select,
      placeholder: "Select Round",
      options: sprintingRounds.map((round) => ({
        label: round,
        value: round,
      })),
    },
    {
      name: "position",
      title: "Position",
      type: FormItemType.text,
    },
    {
      name: "time",
      title: "Time",
      type: FormItemType.text,
    },
    {
      name: "track",
      title: "Track",
      type: FormItemType.text,
    },
    {
      name: "gender",
      title: "Gender",
      type: FormItemType.select,
      placeholder: "Select Gender",
      options: genders.map((gender) => ({
        label: gender,
        value: gender,
      })),
    },
    {
      name: "date",
      title: "Date",
      type: FormItemType.text,
    },
    {
      name: "wind",
      title: "Wind",
      type: FormItemType.text,
    },
  ];

  return (
    <>
      <Form {...form}>
        <form
          className="flex flex-col gap-2.5 text-smallLabel"
          onSubmit={form.handleSubmit(onAddRace)}
        >
          <Card className="grid w-full gap-2.5">
            {SprintFormItems.map((item) => (
              <FormItem key={item.name} control={form.control} {...item} />
            ))}
            {sprintRace && <TaggerSprintStride selectedSprint={sprintRace} />}
          </Card>
          <Button
            className="col-span-2 w-full"
            type="submit"
            disabled={isPending}
          >
            {race ? "Update Race" : "Add Race"}
          </Button>
        </form>
      </Form>
    </>
  );
};
