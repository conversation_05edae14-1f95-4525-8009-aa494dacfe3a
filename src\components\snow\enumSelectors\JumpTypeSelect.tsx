"use client";

import { api } from "~/trpc/react";
import toast from "react-hot-toast";
import { EditableSelector } from "~/components/Selector";
import { useEffect } from "react";

export const JumpTypeSelect = ({
  isSnowboard,
  value,
  onSelect,
}: {
  isSnowboard: boolean;
  value: string | null;
  onSelect: (value: string) => void;
}) => {
  const utils = api.useUtils();
  const { data: options, refetch } = api.snowOptions.getJumpTypes.useQuery({
    isSnowboard,
  });

  const { mutate: add, isPending: isPendingAdd } =
    api.snowOptions.addJumpType.useMutation({
      onSuccess: () => {
        void refetch();
      },
    });

  const { mutate: remove } = api.snowOptions.deleteJumpType.useMutation({
    onError: () => {
      toast.error("Failed to remove jump type");
      void refetch();
    },
  });

  useEffect(() => {
    if (!value && options) {
      const defaultOption = options.find(
        (x) => x.label.toLowerCase() === "jump",
      );
      if (defaultOption) {
        onSelect(defaultOption.value);
      }
    }
  }, [options, value, onSelect]);

  const onAddOption = (value: string) => {
    add({ name: value, isSnowboard });
  };

  const onRemoveOption = (value: string) => {
    remove({ id: parseInt(value) });
    utils.snowOptions.getJumpTypes.setData({ isSnowboard }, (prev) => {
      return prev?.filter((x) => x.value !== value);
    });
  };

  return (
    <EditableSelector
      options={options ?? []}
      onAdd={onAddOption}
      onRemove={onRemoveOption}
      value={value}
      onSelect={onSelect}
      isPendingAdd={isPendingAdd}
    />
  );
};
