import { Pencil, Trash2 } from "lucide-react";
import { checkIsAiTag, cn } from "~/lib/utils";

export const TagItem = ({
  id,
  className,
  frame,
  tag,
  style,
  onEdit,
  onDelete,
  onClick,
  userId,
  aiConfidence,
}: {
  id: string;
  className?: string;
  frame: number;
  tag: string;
  style?: React.CSSProperties;
  onEdit: () => void;
  onDelete: () => void;
  onClick: () => void;
  userId: string;
  aiConfidence?: number | null;
}) => {
  const isAITag = checkIsAiTag(userId);

  return (
    <div
      className={cn(
        "flex h-fit w-full cursor-pointer items-center justify-between rounded-lg border px-2.5 py-[5px]",
        isAITag ? "border-black/10" : "border-black/60",
        className,
      )}
      id={id}
      style={style}
      onClick={onClick}
    >
      <div className="flex flex-1 flex-col gap-[2px] text-smallLabel">
        <div className="flex gap-[5px]">
          <p className="w-[35px] text-black/60">Frame: </p>
          <p className="text-black">{frame}</p>
        </div>
        <div className="flex gap-[5px]">
          <p className="w-[35px] text-black/60">Tag: </p>
          <p>{tag}</p>
        </div>
      </div>
      {aiConfidence && (
        <p className="mx-1 text-smallLabel">
          {(aiConfidence * 100).toFixed(0)}%
        </p>
      )}
      <div className="my-auto flex gap-[5px]">
        <Pencil className="h-[14px] w-[14px] cursor-pointer" onClick={onEdit} />
        <Trash2
          className="h-[14px] w-[14px] cursor-pointer"
          onClick={onDelete}
        />
      </div>
    </div>
  );
};
