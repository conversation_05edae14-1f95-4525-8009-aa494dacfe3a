import Image from "next/image";
import Link from "next/link";
import { BreadcrumbNav } from "./BreadcrumbRoutes";
import { NextVideoButton } from "./NextVideoButton";
import { UserButton } from "./UserButton";
import { CompleteTagButton } from "~/components/CompleteTagButton";

export function Header() {
  return (
    <div className="w-full">
      <header className="flex items-center justify-between p-2.5">
        <div className="flex items-center">
          <Link href="/" className="mr-2">
            <Image
              src="/hpsnzcircle.png"
              alt="Logo"
              width={40}
              height={40}
              className="rounded-full"
            />
          </Link>
          <BreadcrumbNav />
        </div>

        {/* <SeedButton hidden={true} /> */}
        {/* <form className="ml-4">
          <button
            formAction={async () => {
              "use server";
              await api.seed.revertDemoVideoStatus();
            }}
          >
            Revert demo videos status back to Tagged_by_AI
          </button>
        </form> */}
        {/* <form className="ml-auto mr-3">
                  <Button
                    formAction={async () => {
                      "use server";
                      console.log("revalidating videoTags");
                      revalidateTag("competition");
                      revalidateTag("videoSourceStream");
                      revalidateTag("videoSummary");
                    }}
                  >
                    Clear all server cache
                  </Button>
                </form> */}
        <div className="flex items-center space-x-2">
          <CompleteTagButton />
          <NextVideoButton />
          <UserButton />
        </div>
      </header>
    </div>
  );
}
