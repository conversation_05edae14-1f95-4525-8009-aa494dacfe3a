import { useVirtualizer } from "@tanstack/react-virtual";
import { useEffect, useRef } from "react";
import { TagItem } from "~/components/TagItem";
import type { TaglistTag, TagUI } from "~/lib/interface";

//use when there are too many tags, virtual list improve performance

export const TagListVirtual = ({
  tags,
  disabled,
  editingTagId,
  tagOptions,
  onTagClick,
  onRemoveTag,
  setEditingTagId,
}: {
  tags: TaglistTag[];
  disabled?: boolean;
  editingTagId: string | null;
  tagOptions: TagUI[];
  onTagClick: (frame: string) => void;
  onRemoveTag: (tagId: string) => void;
  setEditingTagId: (tagId: string | null) => void;
}) => {
  const listRef = useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: tags.length,
    getScrollElement: () => listRef.current,
    estimateSize: () => 38,
    overscan: 5,
  });

  useEffect(() => {
    if (editingTagId && listRef.current) {
      const index = tags.findIndex((x) => x.id === editingTagId);
      virtualizer.scrollToIndex(index);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editingTagId, tags]);

  return (
    <div className="grid flex-1 gap-1">
      <div ref={listRef} className="h-[40vh] overflow-y-auto">
        <div
          className="relative w-full"
          style={{ height: `${virtualizer.getTotalSize()}px` }}
        >
          {virtualizer.getVirtualItems().map((virtualRow) => {
            const tag = tags[virtualRow.index]!;
            const isEditing = editingTagId === tag.id;
            const className = tagOptions.find(
              (x) => x?.value.toString() === tag.tag,
            )?.className;

            return (
              <div
                key={virtualRow.key}
                data-index={virtualRow.index}
                ref={virtualizer.measureElement}
                className="absolute left-0 top-0 mt-[5px] w-full px-2.5"
                style={{
                  height: `${virtualRow.size}px`,
                  transform: `translateY(${virtualRow.start}px)`,
                }}
              >
                <TagItem
                  id={tag.id}
                  className={
                    isEditing
                      ? "bg-white text-black/60 outline outline-2 outline-offset-1 outline-seaSalt-k40"
                      : className
                  }
                  onClick={() => onTagClick(tag.frame.toString())}
                  frame={tag.frame}
                  tag={tag.tag}
                  onEdit={() => {
                    if (disabled) return;
                    setEditingTagId(editingTagId === tag.id ? null : tag.id);
                    onTagClick(tag.frame.toString());
                  }}
                  onDelete={() => {
                    if (disabled) return;
                    onRemoveTag(tag.id);
                  }}
                  userId={tag.userId}
                  aiConfidence={tag.aiConfidence}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
