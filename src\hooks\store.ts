import { create } from "zustand";
import type { TagChange, TaglistTag } from "~/lib/interface";
import type {
  SwimmingRace,
  SwimmingSession,
  SwimmingRep,
} from "~/lib/interfaces/swimming";
import type { GetVideoInfoOutput } from "~/server/api/routers/video";
import type { ShotputThrows } from "~/server/db/schema";
import type { DiscusThrows } from "~/server/db/discusSchema";
import type { GetHighJumpTagsOutput } from "~/server/api/routers/highJump";
import type { SprintingRace, SprintStrides } from "~/server/db/sprintSchema";

interface ExtendedSprintingRace extends SprintingRace {
  strides: SprintStrides[];
}

interface Store {
  currentFrame: number;
  setCurrentFrame: (currentFrame: number) => void;
  selectedSwimmingRace?: SwimmingRace | null;
  setSelectedSwimmingRace: (selectedSwimmingRace?: SwimmingRace | null) => void;
  selectedAthlete?: string;
  setSelectedAthlete: (selectedAthlete?: string) => void;
  selectedSwimmingRep?: SwimmingRep | null;
  setSelectedSwimmingRep: (selectedSwimmingRep?: SwimmingRep | null) => void;
  selectedSwimmingSession?: SwimmingSession | null;
  setSelectedSwimmingSession: (
    selectedSwimmingSession?: SwimmingSession | null,
  ) => void;
  selectedHighJump?: GetHighJumpTagsOutput[number] | null;
  setSelectedHighJump: (
    selectedHighJump?: GetHighJumpTagsOutput[number] | null,
  ) => void;
  tagFilterValues: string[];
  setTagFilterValues: (tagFilterValues: string[]) => void;
  filteredTags: TaglistTag[];
  setFilteredTags: (tags: TaglistTag[]) => void;
  videoSummary: GetVideoInfoOutput | null;
  setVideoSummary: (videoSummary: GetVideoInfoOutput | null) => void;
  editingTagId: string | null;
  setEditingTagId: (editingTagId: string | null) => void;
  shotputThrow?: ShotputThrows;
  setShotputThrow: (shotputThrow?: ShotputThrows) => void;
  sprintingRace?: ExtendedSprintingRace;
  setSprintingRace: (sprintingRace?: ExtendedSprintingRace) => void;
  discusThrow?: DiscusThrows;
  setDiscusThrow: (discusThrow?: DiscusThrows) => void;
  tagChanges: {
    humanAddedTags: TagChange[];
    humanUpdatedTags: TagChange[];
    removedAITags: TagChange[];
  };
  setTagChanges: (tagChanges: {
    humanAddedTags: TagChange[];
    humanUpdatedTags: TagChange[];
    removedAITags: TagChange[];
  }) => void;
  tagSortByAiConfidence: "asc" | "desc" | null;
  setTagSortByAiConfidence: (
    tagSortByAiConfidence: "asc" | "desc" | null,
  ) => void;
  resetStore: () => void;
}

const initalStore = {
  currentFrame: 1,
  selectedAthlete: undefined,
  selectedSwimmingRace: undefined,
  selectedSwimmingRep: undefined,
  selectedHighJump: undefined,
  tagFilterValues: [],
  filteredTags: [],
  tagChanges: {
    humanAddedTags: [],
    humanUpdatedTags: [],
    removedAITags: [],
  },
  videoSummary: null,
  editingTagId: null,
  shotputThrow: undefined,
  sprintingRace: undefined,
  discusThrow: undefined,
  tagSortByAiConfidence: null,
  selectedSwimmingSession: null,
};

export const useStore = create<Store>((set) => ({
  ...initalStore,
  setCurrentFrame: (currentFrame: number) => set({ currentFrame }),
  setSelectedSwimmingRace: (selectedSwimmingRace?: SwimmingRace | null) =>
    set({ selectedSwimmingRace }),
  setSelectedSwimmingRep: (selectedSwimmingRep?: SwimmingRep | null) =>
    set({ selectedSwimmingRep }),
  setSelectedSwimmingSession: (
    selectedSwimmingSession?: SwimmingSession | null,
  ) => set({ selectedSwimmingSession }),
  setSelectedAthlete: (selectedAthlete?: string) => set({ selectedAthlete }),
  setSelectedHighJump: (
    selectedHighJump?: GetHighJumpTagsOutput[number] | null,
  ) => set({ selectedHighJump }),
  setFilteredTags: (filteredTags: TaglistTag[]) => set({ filteredTags }),
  setTagFilterValues: (tagFilterValues: string[]) => set({ tagFilterValues }),
  setTagChanges: (tagChanges: {
    humanAddedTags: TagChange[];
    humanUpdatedTags: TagChange[];
    removedAITags: TagChange[];
  }) => set({ tagChanges }),
  setVideoSummary: (videoSummary: GetVideoInfoOutput | null) =>
    set({ videoSummary }),
  setEditingTagId: (editingTagId: string | null) => set({ editingTagId }),
  setShotputThrow: (shotputThrow?: ShotputThrows) => set({ shotputThrow }),
  setDiscusThrow: (discusThrow?: DiscusThrows) => set({ discusThrow }),
  setTagSortByAiConfidence: (tagSortByAiConfidence: "asc" | "desc" | null) =>
    set({ tagSortByAiConfidence }),
  resetStore: () => set(initalStore),
  setSprintingRace: (sprintingRace?: ExtendedSprintingRace) =>
    set({ sprintingRace }),
}));

interface HelperStore {
  helperActive: boolean;
  setHelperActive: (helperActive: boolean) => void;
}

export const useHelperStore = create<HelperStore>((set) => ({
  helperActive: false,
  setHelperActive: (active: boolean) => set({ helperActive: active }),
}));
