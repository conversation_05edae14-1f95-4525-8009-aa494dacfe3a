import { getProviders } from "next-auth/react";
import {
  type ProviderProps,
  SignInButton,
} from "~/app/_components/SignInButton";

export default async function SignIn() {
  try {
    const providers = (await getProviders()) as Record<string, ProviderProps>;

    if (!providers) {
      return <div>Failed to load providers</div>;
    }

    return (
      <div className="w-full p-2.5">
        <div className="h-full w-full rounded-xl bg-white px-2.5 py-[120px]">
          <div className="flex flex-col gap-10">
            <div className="flex flex-col items-start gap-2.5">
              <p className="text-header text-black">Sports Analysis Tool.</p>
              <p className="text-header text-black/60">
                Created by Cub Digital for HPSNZ.
              </p>
            </div>
            <div className="relative flex h-full flex-col gap-4">
              {Object.values(providers).map((provider) => (
                <div key={provider.id}>
                  <SignInButton provider={provider} />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error(error);
    return <div>Failed to load providers</div>;
  }
}
