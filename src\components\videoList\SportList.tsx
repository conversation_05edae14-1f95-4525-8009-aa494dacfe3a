"use client";

import { ArrowDownUp, ArrowUpDown, Search } from "lucide-react";
import { useSession } from "next-auth/react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { Input } from "~/components/ui/input";
import { useVideoTotalCount } from "~/hooks/video";
import { Sport, type UserRole, VideoStatus } from "~/lib/enums/enums";
import { cn, formatString, getUserSports } from "~/lib/utils";
import { SportVideos } from "./SportVideos";
import { StatusFilter } from "./StatusFilter";
import { Pill } from "../ui/pill";

const getSports = (
  roles: UserRole[],
  sort: "asc" | "desc",
  sport?: Sport[],
) => {
  const userSports = getUserSports(roles);
  const result: Sport[] = [];
  if (sport) {
    sport.forEach((x) => {
      if (userSports.includes(x)) {
        result.push(x);
      }
    });
  } else {
    result.push(...userSports);
  }

  result.sort((a, b) => {
    if (sort === "asc") {
      return a.localeCompare(b);
    } else {
      return b.localeCompare(a);
    }
  });

  return result;
};

export const SportList = () => {
  const { data: session } = useSession();
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const status = searchParams.get("status");
  const { count: totalCount, isLoading } = useVideoTotalCount();

  const isValidation = pathname === "/validation";

  const onSearchChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("search", value);
    router.push(`?${params.toString()}`);
  };

  const isListEmpty = !isLoading && totalCount === 0;
  const statusLabel = status ? formatString(status) : "videos";

  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  const sports = getSports(
    (session?.user?.roles ?? []) as UserRole[],
    sortOrder,
    isValidation
      ? [Sport.athletics, Sport.swimming]
      : [Sport.athletics, Sport.swimming, Sport.snowsports],
    //todo: : [Sport.athletics, Sport.swimming, Sport.snowsports, Sport.boxing],
  );

  return (
    <div
      className={cn(
        "col-span-12 flex flex-col",
        isListEmpty ? "gap-[70px]" : "gap-2.5",
      )}
    >
      <div className="flex w-full items-center justify-between">
        <div className="flex gap-2.5 px-2.5 py-[5px]">
          <p>Videos awaiting your review</p>
          <Pill className="flex items-center bg-black/10 text-center">
            <p className="text-smallLabel">{totalCount}</p>
          </Pill>
        </div>
        <div className="flex items-center gap-2.5">
          {!isValidation && (
            <StatusFilter
              filterKey="status"
              filterEnumValues={Object.values(VideoStatus)}
            />
          )}
          <div className="relative w-[209px]">
            <Input
              placeholder="Search"
              defaultValue={searchParams.get("search") ?? ""}
              onBlur={(e) => {
                onSearchChange(e.target.value);
              }}
              onFinish={onSearchChange}
              className="w-full pr-8 placeholder:text-seaSalt-k40"
            />
            <Search className="absolute right-2.5 top-1/2 h-[14px] w-[14px] -translate-y-1/2 text-gray-400" />
          </div>
          {sortOrder === "asc" ? (
            <ArrowUpDown
              className="icon-orderBy h-6 w-6"
              strokeWidth={1}
              onClick={() => setSortOrder("desc")}
            />
          ) : (
            <ArrowDownUp
              className="icon-orderBy h-6 w-6"
              strokeWidth={1}
              onClick={() => setSortOrder("asc")}
            />
          )}
        </div>
      </div>
      {isListEmpty && (
        <div className="flex flex-col items-start gap-2.5">
          <p className="text-header">No {statusLabel} found</p>
          <p className="text-header text-black/60">
            {status
              ? `There are currently no videos with status "${formatString(status)}".`
              : "There are currently no videos in your queue."}
          </p>
        </div>
      )}
      {!isListEmpty && (
        <div className="flex flex-col gap-2.5">
          {sports.map((sport) => (
            <SportVideos
              key={sport}
              sport={sport}
              iconPath={`/sportIcons/${sport.toLowerCase()}.svg`}
            />
          ))}
        </div>
      )}
    </div>
  );
};
