"use client";

import { api } from "~/trpc/react";
import toast from "react-hot-toast";
import { EditableSelector } from "~/components/Selector";

export const RailFeatureSelect = ({
  value,
  onSelect,
}: {
  value: string | null;
  onSelect: (value: string) => void;
}) => {
  const utils = api.useUtils();
  const { data: options, refetch } = api.snowOptions.getRailFeatures.useQuery();

  const { mutate: add, isPending: isPendingAdd } =
    api.snowOptions.addRailFeature.useMutation({
      onSuccess: () => {
        void refetch();
      },
    });

  const { mutate: remove } = api.snowOptions.deleteRailFeature.useMutation({
    onError: () => {
      toast.error("Failed to remove jump type");
      void refetch();
    },
  });

  const onAddOption = (value: string) => {
    add({ name: value });
  };

  const onRemoveOption = (value: string) => {
    remove({ id: parseInt(value) });
    utils.snowOptions.getRailFeatures.setData(undefined, (prev) => {
      return prev?.filter((x) => x.value !== value);
    });
  };

  return (
    <EditableSelector
      placeholder="Select/enter new rail feature"
      options={options ?? []}
      onAdd={onAddOption}
      onRemove={onRemoveOption}
      value={value}
      onSelect={onSelect}
      isPendingAdd={isPendingAdd}
    />
  );
};
