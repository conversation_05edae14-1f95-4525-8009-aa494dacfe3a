"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useParams } from "next/navigation";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import type { z } from "zod";
import { Card } from "~/components/Card";
import { type FormItemProps, FormItem } from "~/components/FormItem";
import { Button } from "~/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormItem as ShadFormItem,
} from "~/components/ui/form";
import { useStore } from "~/hooks/store";
import { FormItemType } from "~/lib/enums/enums";
import {
  HighJumpSide,
  highJumpSides,
  HighJumpTag,
  HighJumpType,
  highJumpTypes,
} from "~/lib/enums/highJump";
import type { TaglistTag } from "~/lib/interface";
import { highJumpSchema } from "~/lib/schemas";
import { api } from "~/trpc/react";
import { Checkbox } from "../ui/check-box";
import { Label } from "../ui/label";
import { TaggerHighJumpStride } from "./TaggerHighJumpStride";
import { useHotkeys } from "react-hotkeys-hook";

const jumpTypeHotkeys = ["1", "2", "3", "4", "5"];

const equipmentOptions = [
  {
    label: "bar",
    value: "bar",
    hotkey: ",",
  },
  {
    label: "box",
    value: "box",
    hotkey: ".",
  },
];

interface HighJumpFormItemProps extends FormItemProps {
  name: keyof HighJumpFormProps;
  className?: string;
}

export type HighJumpFormProps = z.infer<typeof highJumpSchema>;

const jumpInitialValues: HighJumpFormProps = {
  id: "",
  number: "1",
  approachSide: HighJumpSide.RightSide,
  height: "",
  success: "true",
  type: HighJumpType.Flop,
  equipment: [],
  comment: "",
  startFrame: "",
  endFrame: "",
};

export const TaggerHighJumpForm = () => {
  const utils = api.useUtils();
  const params = useParams<{ id: string }>();
  const videoId = params.id;

  const currentFrame = useStore((state) => state.currentFrame);
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const setCurrentFrame = useStore((state) => state.setCurrentFrame);
  const setFilteredTags = useStore((state) => state.setFilteredTags);
  const setSelectedHighJump = useStore((state) => state.setSelectedHighJump);

  const { data: highJumps, refetch } = api.highJump.getTags.useQuery({
    videoId,
  });

  const { mutate: upsertHighJump, isPending } =
    api.highJump.upsertHighJump.useMutation({
      onError: () => {
        void refetch();
        toast.error("Failed to add jump");
      },
      onSuccess: async (result) => {
        await utils.highJump.getTags.invalidate({ videoId });
        form.setValue("number", result.number.toString());
      },
    });

  const form = useForm<HighJumpFormProps>({
    resolver: zodResolver(highJumpSchema),
    defaultValues: { ...jumpInitialValues },
  });

  const jumpNumber = form.watch("number");
  const selectedJump = highJumps?.find((x) => x.number === +jumpNumber);

  useEffect(() => {
    if (!selectedJump) return;
    setSelectedHighJump(selectedJump);
    setCurrentFrame(selectedJump.startFrame ?? 1);

    return () => {
      setSelectedHighJump(undefined);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedJump?.startFrame]);

  useEffect(() => {
    if (selectedJump) {
      const equipment: ("bar" | "box")[] = [];
      if (selectedJump.withBox) equipment.push("box");
      if (selectedJump.withBar) equipment.push("bar");
      form.setValue("id", selectedJump.id.toString());
      form.setValue("number", selectedJump.number.toString());
      form.setValue("approachSide", selectedJump.approachSide);
      form.setValue("height", selectedJump.height?.toString() ?? "");
      form.setValue("success", selectedJump.success?.toString() ?? "true");
      form.setValue("type", selectedJump.type ?? HighJumpType.Flop);
      form.setValue("equipment", equipment);
      form.setValue("comment", selectedJump.comment ?? "");
      form.setValue("startFrame", selectedJump.startFrame?.toString() ?? "");
      form.setValue("endFrame", selectedJump.endFrame?.toString() ?? "");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedJump]);

  useEffect(() => {
    if (!selectedAthlete || !selectedJump) return;
    const tags: TaglistTag[] = [
      {
        id: `${selectedJump.id}-start`,
        tag: HighJumpTag.StartFrame,
        frame: selectedJump.startFrame,
        athleteId: selectedAthlete,
        videoId,
        userId: selectedJump.userId,
        isDeleted: false,
      },
    ];
    selectedJump.strides.forEach((stride) => {
      tags.push({
        id: stride.id.toString(),
        tag: HighJumpTag.HeelContact,
        frame: stride.heelContact,
        athleteId: selectedAthlete,
        videoId,
        userId: stride.userId,
        isDeleted: false,
      });
      tags.push({
        id: stride.id.toString(),
        tag: HighJumpTag.ToeOff,
        frame: stride.toeOff,
        athleteId: selectedAthlete,
        videoId,
        userId: stride.userId,
        isDeleted: false,
      });
    });
    tags.push({
      id: `${selectedJump.id}-end`,
      tag: HighJumpTag.EndFrame,
      frame: selectedJump.endFrame,
      athleteId: selectedAthlete,
      videoId,
      userId: selectedJump.userId,
      isDeleted: false,
    });
    tags.sort((a, b) => a.frame - b.frame);
    setFilteredTags(tags);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedAthlete, selectedJump]);

  const onAddJump = () => {
    if (!selectedAthlete) return;

    const maxJumpNumber =
      highJumps && highJumps.length > 0
        ? Math.max(...highJumps.map((x) => x.number))
        : 0;

    upsertHighJump({
      videoId,
      athleteId: selectedAthlete,
      number: maxJumpNumber + 1,
      startFrame: currentFrame,
      endFrame: currentFrame,
      approachSide: HighJumpSide.RightSide,
    });
  };

  const onSaveJump = () => {
    if (!selectedAthlete) return;
    const jump = form.getValues();

    const originalJump = highJumps?.find(
      (x) => x.number.toString() === jump.number,
    );
    if (!originalJump) return;

    const newJumpObj = {
      startFrame: +jump.startFrame,
      endFrame: +jump.endFrame,
      approachSide: jump.approachSide,
      height: +jump.height,
      success: jump.success === "true",
      type: jump.type,
      withBox: jump.equipment.includes("box"),
      withBar: jump.equipment.includes("bar"),
      comment: jump.comment,
    };

    if (
      originalJump.startFrame === newJumpObj.startFrame &&
      originalJump.endFrame === newJumpObj.endFrame &&
      originalJump.approachSide === newJumpObj.approachSide &&
      originalJump.height === newJumpObj.height &&
      originalJump.success === newJumpObj.success &&
      originalJump.type === newJumpObj.type &&
      originalJump.withBox === newJumpObj.withBox &&
      originalJump.withBar === newJumpObj.withBar &&
      originalJump.comment === newJumpObj.comment
    ) {
      return;
    }

    utils.highJump.getTags.setData({ videoId }, (prev) => {
      if (!prev) return [];
      return prev.map((x) => {
        if (x.number.toString() === jump.number) {
          return {
            ...x,
            ...newJumpObj,
          };
        }
        return x;
      });
    });

    void upsertHighJump({
      videoId,
      athleteId: selectedAthlete,
      number: +jump.number,
      ...newJumpObj,
    });
  };

  const jumpNumberItems: HighJumpFormItemProps[] = [
    {
      name: "number",
      title: "Jump Number",
      type: FormItemType.select,
      className: "grid gap-[5px]",
      options:
        highJumps?.map((x) => ({
          label: x.number.toString(),
          value: x.number.toString(),
        })) ?? [],
    },
    {
      name: "height",
      title: "Height (m)",
      type: FormItemType.number,
      className: "grid",
      onBlur: () => {
        onSaveJump();
      },
    },
  ];

  const jumpFormItems: HighJumpFormItemProps[] = [
    {
      name: "startFrame",
      title: "Start Frame",
      type: FormItemType.number,
      className: "grid grid-cols-2",
      hotkey: {
        value: "c",
        onClick: () => {
          form.setValue("startFrame", currentFrame.toString());
          onSaveJump();
        },
        className: "bg-fuchsia text-white",
      },
    },
    {
      name: "endFrame",
      title: "End Frame",
      type: FormItemType.number,
      className: "grid grid-cols-2",
      hotkey: {
        value: "v",
        onClick: () => {
          form.setValue("endFrame", currentFrame.toString());
          onSaveJump();
        },
        className: "bg-orange text-white",
      },
    },
    {
      name: "approachSide",
      title: "Approach Side",
      type: FormItemType.radio,
      className: "grid grid-cols-2",
      radioGroupClassName: "grid-cols-2",
      options: highJumpSides.map((x, index) => ({
        label: x.split(" ")[0]!,
        value: x,
        hotkey: {
          value: index === 0 ? "[" : "]",
          onClick: () => {
            form.setValue("approachSide", x);
            onSaveJump();
          },
        },
      })),
    },
    {
      name: "type",
      title: "Jump Type",
      type: FormItemType.radio,
      className: "grid grid-cols-2 items-start",
      radioGroupClassName: "grid-cols-2",
      options: highJumpTypes.map((x, index) => ({
        label: x,
        value: x,
        hotkey: {
          value: jumpTypeHotkeys[index]!,
          onClick: () => {
            form.setValue("type", x);
            onSaveJump();
          },
        },
      })),
    },
    {
      name: "equipment",
      title: "Equipment",
      type: FormItemType.checkbox,
      className: "grid grid-cols-2",
      CustomRender: () => (
        <div className="grid flex-1 grid-cols-2 gap-[5px]">
          {equipmentOptions.map((item) => (
            <FormField
              key={item.value.toString()}
              control={form.control}
              name="equipment"
              render={({ field }) => {
                return (
                  <ShadFormItem
                    key={item.value.toString()}
                    className="flex flex-row items-center space-x-3 space-y-0"
                  >
                    <FormControl>
                      <Checkbox
                        checked={(field.value as string[]).includes(
                          item.value.toString(),
                        )}
                        text={item.hotkey}
                        onCheckedChange={() => {
                          onEquipmentChange(item.value as "bar" | "box");
                        }}
                      />
                    </FormControl>
                    <FormLabel className="font-normal">{item.label}</FormLabel>
                  </ShadFormItem>
                );
              }}
            />
          ))}
        </div>
      ),
    },
    {
      name: "success",
      title: "Success",
      type: FormItemType.radio,
      className: "grid grid-cols-2",
      hotkey: {
        value: "e",
        onClick: () => {
          const currentSuccess = form.getValues("success");
          form.setValue(
            "success",
            currentSuccess === "true" ? "false" : "true",
          );
          onSaveJump();
        },
        className:
          "group-data-[state=checked]:bg-fuchsia-20 bg-seaSalt-40 data-[state=checked]:text-fuchsia-k40",
      },
      options: [
        {
          label: "yes",
          value: "true",
        },
        {
          label: "no",
          value: "false",
        },
      ],
    },
  ];

  const commentFormItems: HighJumpFormItemProps[] = [
    {
      name: "comment",
      title: "Comments",
      type: FormItemType.textarea,
      className: "grid",
      placeholder: "Enter text here",
      onBlur: () => {
        onSaveJump();
      },
    },
  ];

  const onEquipmentChange = (equipmentItem: "bar" | "box") => {
    const equipment = form.getValues("equipment");
    if (equipment.includes(equipmentItem)) {
      form.setValue(
        "equipment",
        equipment.filter((x) => x !== equipmentItem),
      );
    } else {
      form.setValue("equipment", [...equipment, equipmentItem]);
    }
    onSaveJump();
  };

  useHotkeys("comma", () => onEquipmentChange("bar"));
  useHotkeys(".", () => onEquipmentChange("box"));

  return (
    <div className="flex h-full flex-col gap-2.5">
      <Form {...form}>
        <form className="flex flex-col gap-2.5 text-smallLabel">
          {highJumps && highJumps.length > 0 && (
            <>
              <Card className="grid w-full gap-2.5">
                {jumpNumberItems.map((item) => (
                  <FormItem key={item.name} control={form.control} {...item} />
                ))}
              </Card>
              {selectedJump && (
                <TaggerHighJumpStride selectedJump={selectedJump} />
              )}
              <Card className="grid w-full gap-2.5">
                <Label className="text-smallLabel">JUMP DETAILS:</Label>
                {jumpFormItems.map((item) => (
                  <FormItem key={item.name} control={form.control} {...item} />
                ))}
              </Card>
              <Card className="grid w-full gap-2.5">
                {commentFormItems.map((item) => (
                  <FormItem key={item.name} control={form.control} {...item} />
                ))}
              </Card>
            </>
          )}
        </form>
      </Form>
      <Button
        className="col-span-2 mt-auto w-full"
        disabled={isPending}
        type="button"
        onClick={onAddJump}
      >
        Add Jump
      </Button>
    </div>
  );
};
