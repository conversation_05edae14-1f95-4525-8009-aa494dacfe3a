import { SnowFeatureType } from "./enums/snow";

export const spinAmounts = [
  "0",
  "180",
  "360",
  "540",
  "720",
  "900",
  "1080",
  "1260",
  "1440",
  "1620",
  "1800",
  "1980",
  "2160",
  "2340",
  "2520",
  "2700",
];

export const railSpinAmounts = Array.from({ length: 31 }, (_, i) =>
  (i * 90).toString(),
);

export const transitionSectionMap = {
  j: SnowFeatureType.jump,
  r: SnowFeatureType.rail,
  t: SnowFeatureType.transition,
};

export const poseConnections = [
  //head
  [10, 9],
  [9, 8],
  //9, 8 connects head to shoulders
  //8, 11 connects shoulders to right arm
  [8, 11],
  [11, 12],
  [12, 13],
  //8, 14 connects shoulders to left arm
  [8, 14],
  [14, 15],
  [15, 16],
  //torso
  [8, 7],
  [7, 0],
  //0, 4 connects torso to right leg
  [0, 4],
  //4, 5 connects right leg to right foot
  [4, 5],
  [5, 6],
  //0, 1 connects torso to left leg
  [0, 1],
  //1, 2 connects left leg to left foot
  [1, 2],
  [2, 3],
];
