import { getHTTPStatusCodeFromError } from "~/lib/errorCode";

import { TRPCError } from "@trpc/server";

export const returnError = (error: unknown) => {
  if (error instanceof TRPCError) {
    return Response.json(error, {
      status: getHTTPStatusCodeFromError(error.code),
    });
  }
  const status =
    (error as { message: string }).message === "jwt expired" ? 401 : 500;
  return Response.json(error, {
    status,
  });
};
