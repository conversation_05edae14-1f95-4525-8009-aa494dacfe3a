import { ChevronsUpDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useStatusFilter } from "~/hooks/useStatusFilter";

interface FilterProps {
  filterKey: string;
  filterEnumValues: string[];
}
//filterKey = "sports", "status" etc
//filterProps = enum values for the filterKey eg videoStatuses etc
export const StatusFilter = ({ filterKey, filterEnumValues }: FilterProps) => {
  const { filterValues, onFilterClick } = useStatusFilter({ filterKey });

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        asChild
        className="border border-gray-dark text-gray-dark hover:bg-black/10"
      >
        <div className="flex cursor-pointer items-center justify-center gap-0.5 rounded-full px-2.5 py-[5px] text-smallLabel">
          Video Status
          <ChevronsUpDown className="h-3 w-3" />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="rounded-lg border border-gray-dark">
        {filterEnumValues.map((filterValue) => (
          <DropdownMenuCheckboxItem
            key={filterValue}
            className="cursor-pointer text-xs capitalize text-gray-dark"
            checked={filterValues.includes(filterValue)}
            onClick={() => {
              onFilterClick(filterValue);
            }}
          >
            {filterValue.replaceAll("_", " ")}
          </DropdownMenuCheckboxItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
