import { eq } from "drizzle-orm";
import type { NextRequest } from "next/server";
import { validateSwimmingBody } from "~/server/api/utils/bodyValidation";
import { checkToken } from "~/server/api/utils/permissions";
import { returnError } from "~/server/api/utils/returnError";
import { getUserIdByToken } from "~/server/api/utils/service";
import { db } from "~/server/db";
import { swimmingTags } from "~/server/db/schema";

/**
 * @swagger
 * /api/v1/swimming/{id}:
 *   get:
 *     description: Get swimming tags for a video.
 *     tags: [Swimming]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   athleteId:
 *                     type: string
 *                   frame:
 *                     type: number
 *                   tag:
 *                     type: string
 *                   x1:
 *                     type: number
 *                     nullable: true
 *                   x2:
 *                     type: number
 *                     nullable: true
 *                   y1:
 *                     type: number
 *                     nullable: true
 *                   y2:
 *                     type: number
 *                     nullable: true
 *       400:
 *         description: Bad Request - Invalid video ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: string
 *                   example: BAD_REQUEST
 *                 message:
 *                   type: string
 *                   example: Invalid video id
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: string
 *                   example: UNAUTHORIZED
 *                 message:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: string
 *                   example: INTERNAL_SERVER_ERROR
 *                 message:
 *                   type: string
 *                   example: An unexpected error occurred
 *   post:
 *     description: Add swimming tags to video.
 *     tags: [Swimming]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: object
 *               required:
 *                 - athleteId
 *                 - frame
 *                 - tag
 *               properties:
 *                 athleteId:
 *                   type: string
 *                   description: The ID of the athlete
 *                 frame:
 *                   type: number
 *                   description: The frame number in the video
 *                 tag:
 *                   type: string
 *                   description: The swimming tag type
 *                 x1:
 *                   type: number
 *                   nullable: true
 *                   description: X coordinate of the first point
 *                 x2:
 *                   type: number
 *                   nullable: true
 *                   description: X coordinate of the second point
 *                 y1:
 *                   type: number
 *                   nullable: true
 *                   description: Y coordinate of the first point
 *                 y2:
 *                   type: number
 *                   nullable: true
 *                   description: Y coordinate of the second point
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: Bad Request - Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: string
 *                   example: BAD_REQUEST
 *                 message:
 *                   type: string
 *                   example: Invalid request body
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: string
 *                   example: UNAUTHORIZED
 *                 message:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: string
 *                   example: INTERNAL_SERVER_ERROR
 *                 message:
 *                   type: string
 *                   example: An unexpected error occurred
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  try {
    await checkToken(request);

    const tags = await db
      .select()
      .from(swimmingTags)
      .where(eq(swimmingTags.videoId, id));

    return Response.json(tags);
  } catch (error) {
    return returnError(error);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  try {
    const { tokenPayload } = await checkToken(request);

    const userId = await getUserIdByToken(tokenPayload);

    const body = await validateSwimmingBody(request);
    await db.insert(swimmingTags).values(
      body.map((x) => ({
        videoId: id,
        athleteId: x.athleteId,
        frame: x.frame,
        aiConfidence: x.aiConfidence,
        tag: x.tag,
        userId,
        x1: x.x1,
        x2: x.x2,
        y1: x.y1,
        y2: x.y2,
      })),
    );

    return Response.json({});
  } catch (error) {
    return returnError(error);
  }
}
