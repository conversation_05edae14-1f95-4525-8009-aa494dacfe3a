"use client";

import toast from "react-hot-toast";
import { But<PERSON> } from "~/components/ui/button";
import { useStore } from "~/hooks/store";
import { DiscusHand, DiscusMovement, DiscusType } from "~/lib/enums/discus";
import { api } from "~/trpc/react";

export const AddDiscusThrowButton = () => {
  const utils = api.useUtils();

  // Get the current throw from the store
  const discusThrow = useStore((state) => state.discusThrow);
  const videoSummary = useStore((state) => state.videoSummary);
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const setDiscusThrow = useStore((state) => state.setDiscusThrow);

  const { data: throws } = api.discus.getThrows.useQuery(
    { videoId: videoSummary?.id ?? "" },
    { enabled: !!videoSummary?.id },
  );

  // Get the mutation for adding a new throw
  const { mutate: upsertDiscusThrow, isPending } =
    api.discus.upsertDiscusThrow.useMutation({
      onSuccess: async (result) => {
        await utils.discus.getThrows.invalidate();
        toast.success(`Throw added successfully`);

        // Create a new throw object with default values but keep the new throw number
        const newThrow = {
          id: result.id,
          videoId: result.videoId,
          athleteId: result.athleteId,
          number: result.number,
          movement: DiscusMovement.Rotation,
          type: DiscusType.Full,
          hand: DiscusHand.RightHand,
          nonReverse: false,
          weight: 0,
          denfyTool: false,
          userId: result.userId,
          dateCreated: result.dateCreated,
        };

        // Update the store with the new throw
        setDiscusThrow(newThrow);
      },
    });

  const handleAddThrow = async () => {
    if (!videoSummary?.id || !selectedAthlete) {
      toast.error("Missing video, athlete, or throw information");
      return;
    }
    // Find the highest throw number to ensure uniqueness
    const highestThrow =
      throws?.reduce(
        (max, throw_) => (throw_.number > max ? throw_.number : max),
        0,
      ) ?? 0;
    const nextThrowNumber = highestThrow + 1;

    upsertDiscusThrow({
      videoId: videoSummary.id,
      athleteId: selectedAthlete,
      number: nextThrowNumber, // Use the calculated unique number
      // Use values from current throw
      movement: discusThrow?.movement ?? DiscusMovement.Rotation,
      type: discusThrow?.type ?? DiscusType.Full,
      hand: discusThrow?.hand ?? DiscusHand.RightHand,
      weight: discusThrow?.weight ?? 0,
      nonReverse: discusThrow?.nonReverse ?? false,
      denfyTool: discusThrow?.denfyTool ?? false,
    });
  };

  return (
    <Button
      onClick={handleAddThrow}
      className="mt-auto w-full rounded-full"
      disabled={isPending || !selectedAthlete || !videoSummary?.id}
    >
      Add New Throw
    </Button>
  );
};
