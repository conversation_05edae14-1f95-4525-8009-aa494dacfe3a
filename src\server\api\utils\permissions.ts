import { TRPCError } from "@trpc/server";
import type { NextRequest } from "next/server";
import jwt from "jsonwebtoken";
import type { DecodedToken } from "~/lib/interface";
import jwksClient from "jwks-rsa";
import { env } from "~/env";
import { UserRole } from "~/lib/enums/enums";

const ptaClient = jwksClient({
  jwksUri: `${env.NEXT_PUBLIC_KEYCLOAK_ROOT}/realms/hpsnz/protocol/openid-connect/certs`,
  requestHeaders: {
    "User-Agent": "custom-user-agent",
  },
});

export const checkToken = async (request: NextRequest) => {
  // const method = request.method;
  const auth = request.headers.get("authorization");
  const token = auth?.replace("Bearer ", "");

  if (!token) {
    throw new TRPCError({
      message: "No token provided",
      code: "UNAUTHORIZED",
    });
  }

  const decodedToken = jwt.decode(token, { complete: true });

  if (!decodedToken)
    throw new TRPCError({ message: "Invalid token", code: "UNAUTHORIZED" });

  const tokenPayload = decodedToken?.payload as DecodedToken;
  const signingKeys = await ptaClient.getSigningKey(decodedToken.header.kid);
  const publicKey = signingKeys.getPublicKey();

  jwt.verify(token, publicKey, {
    algorithms: ["RS256"],
  });

  const roles = tokenPayload.roles;

  if (!roles.includes(UserRole.api)) {
    throw new TRPCError({
      message: "Unauthorized",
      code: "UNAUTHORIZED",
    });
  }

  checkPTAPermission(tokenPayload, roles);
  return { token, tokenPayload };
};

export const checkPTAPermission = (
  decodedToken: DecodedToken,
  roles?: string[],
) => {
  if (
    !decodedToken.aud.includes("taggerplatform") ||
    !decodedToken.aud.includes("videoplatform") ||
    !decodedToken.aud.includes("ptaplatform")
  ) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "User does not have permission",
    });
  }
  if (roles) {
    if (roles.some((role) => !decodedToken.roles.includes(role as UserRole))) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "User does not have permission",
      });
    }
  }
};
