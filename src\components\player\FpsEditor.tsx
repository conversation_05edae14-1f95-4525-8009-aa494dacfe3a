"use client";

import { useEffect, useState } from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { Input } from "~/components/ui/input";
import { Button } from "../ui/button";
import { Label } from "../ui/label";
import { api } from "~/trpc/react";
import { useParams, useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { useStore } from "~/hooks/store";

export const FpsEditor = ({ defaultFps }: { defaultFps?: number }) => {
  const params = useParams();
  const router = useRouter();
  const videoId = params.id as string;

  const [fps, setFps] = useState(defaultFps);
  const [isOpen, setIsOpen] = useState(false);

  const videoSummary = useStore((state) => state.videoSummary);
  const dbFps = videoSummary?.fps;

  const { mutate: setVideoFps, isPending } = api.videos.setVideoFps.useMutation(
    {
      onSuccess: () => {
        router.refresh();
        setIsOpen(false);
      },
      onError: (error) => {
        toast.error(error.message);
      },
    },
  );

  useEffect(() => {
    if (isPending) return;
    if (dbFps) return;
    if (!defaultFps) return;
    if (dbFps === defaultFps) return;
    setVideoFps({ id: videoId, fps: defaultFps });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dbFps, defaultFps]);

  const onFpsChange = (value: number) => {
    setFps(value);
  };

  const onSave = () => {
    if (!fps || isPending) return;
    setVideoFps({ id: videoId, fps });
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <p className="cursor-pointer">[{defaultFps} FPS]</p>
      </PopoverTrigger>
      <PopoverContent className="grid w-24 gap-2.5">
        <Label className="text-smallLabel">FPS</Label>
        <Input
          type="number"
          value={fps}
          onChange={(e) => onFpsChange(+e.target.value)}
          onFinish={onSave}
        />
        <Button
          size="xs"
          className="w-full"
          onClick={onSave}
          loading={isPending}
        >
          Save
        </Button>
      </PopoverContent>
    </Popover>
  );
};
