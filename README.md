### Features

- Get m3u8 video frame info
- Show frame number in a video player
- Jump to any frame in a video
- Frame accurate

# Video validation tool

![](https://video-platform-git-development-hpsnz.vercel.app/_next/image?url=%2FHPSNZLOGO.png&w=256&q=75)

### FFmpeg wasm + video.js

FFmpeg to get video fps & pts & time base
Video.js to play video
All frames (number & timestamp) are calculated

## Get video info

1. Get video source stream (m3u8) file from video portal api
2. Feed m3u8 file to video.js. As tested, only H264 codec is available.
3. Read the first ts chunk of the video
4. Find fps, pts, and time base from ffmpeg

## Calculations

Since ffmpeg rounds timestamp for each frame, it is not accurate. For example if timestamp for frame 4 is 0.01234s, round the number to 4 decimal places we get: 0.0123s. But the frame locates after 0.0123s. So we need ceil instead of round here.
(frame number starts from 1, time starts from 0. So need +/- 1 in calculation)

##### Time stamp = ceil ((frame number - 1) / fps)

##### Frame number = floor (round to 3 decimal places (current time \* fps + 1))

## Video Player

- When video is seeked or play / pause, calculate the frame number
- When edit frame number, calculate the timestamp

## Server cache (If any of following data is not up to date, try clear server cache or reload page)

- PTA competition
- Video source in S3 bucket
- Video detail from video portal

## Virtual list

Improve performance when rendering large list

- Tag list (use virtual when there are more than 1000 tags)
