import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import type { NextRequest } from "next/server";
import { validateHighjumpBody } from "~/server/api/utils/bodyValidation";
import { checkToken } from "~/server/api/utils/permissions";
import { returnError } from "~/server/api/utils/returnError";
import { getUserIdByToken } from "~/server/api/utils/service";
import { db } from "~/server/db";
import { highJumps, highJumpStrides } from "~/server/db/highJumpSchema";

/**
 * @swagger
 * /api/v1/highjump/{id}:
 *   get:
 *     description: Get high jump details with strides for a specific video ID
 *     tags: [High Jump]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: number
 *                 videoId:
 *                   type: string
 *                 athleteId:
 *                   type: string
 *                 number:
 *                   type: number
 *                 startFrame:
 *                   type: number
 *                 endFrame:
 *                   type: number
 *                 approachSide:
 *                   type: string
 *                   enum: [LEFT, RIGHT]
 *                 height:
 *                   type: number
 *                   nullable: true
 *                 success:
 *                   type: boolean
 *                   nullable: true
 *                 type:
 *                   type: string
 *                   enum: [SCISSORS, FOSBURY, WESTERN_ROLL, EASTERN_CUT_OFF]
 *                   nullable: true
 *                 withBox:
 *                   type: boolean
 *                   nullable: true
 *                 withBar:
 *                   type: boolean
 *                   nullable: true
 *                 comment:
 *                   type: string
 *                   nullable: true
 *                 userId:
 *                   type: string
 *                 dateCreated:
 *                   type: string
 *                 strides:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: number
 *                       jumpId:
 *                         type: number
 *                       number:
 *                         type: number
 *                       heelContact:
 *                         type: number
 *                       toeOff:
 *                         type: number
 *                       userId:
 *                         type: string
 *                       dateCreated:
 *                         type: string
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: UNAUTHORIZED
 *                 message:
 *                   type: string
 *                   example: Invalid token
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: INTERNAL_SERVER_ERROR
 *                 message:
 *                   type: string
 *                   example: An unexpected error occurred
 *
 *   post:
 *     description: Create high jump entries with strides for a specific video ID
 *     tags: [High Jump]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               jumps:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     athleteId:
 *                       type: string
 *                     number:
 *                       type: number
 *                     startFrame:
 *                       type: number
 *                     endFrame:
 *                       type: number
 *                     approachSide:
 *                       type: string
 *                       enum: [left side, right side]
 *                     strides:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           number:
 *                             type: number
 *                           heelContact:
 *                             type: number
 *                           toeOff:
 *                             type: number
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: UNAUTHORIZED
 *                 message:
 *                   type: string
 *                   example: Invalid token
 *       400:
 *         description: Bad Request - Invalid body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: BAD_REQUEST
 *                 message:
 *                   type: string
 *                   example: Invalid Body
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: string
 *                   example: INTERNAL_SERVER_ERROR
 *                 message:
 *                   type: string
 *                   example: An unexpected error occurred
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  try {
    await checkToken(request);

    const jumpsWithStrides = await db.query.highJumps.findFirst({
      where: eq(highJumps.videoId, id),
      with: {
        strides: true,
      },
    });

    return Response.json(jumpsWithStrides);
  } catch (e) {
    return returnError(e);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const { id } = params;

  try {
    const { tokenPayload } = await checkToken(request);
    const userId = await getUserIdByToken(tokenPayload);
    const body = await validateHighjumpBody(request);

    for (const jumpData of body.jumps) {
      await db.transaction(async (tx) => {
        const result = await tx
          .insert(highJumps)
          .values({
            videoId: id,
            athleteId: jumpData.athleteId,
            number: jumpData.number,
            startFrame: jumpData.startFrame,
            endFrame: jumpData.endFrame,
            approachSide: jumpData.approachSide,
            userId: userId,
          })
          .$returningId();

        const jumpId = result[0]?.id;
        if (!jumpId) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to insert highjump jump",
          });
        }

        await tx.insert(highJumpStrides).values(
          jumpData.strides.map((x) => ({
            jumpId: x.jumpId,
            number: x.number,
            heelContact: x.heelContact,
            toeOff: x.toeOff,
            userId: userId,
          })),
        );
      });
    }
    return Response.json({});
  } catch (e) {
    return returnError(e);
  }
}
