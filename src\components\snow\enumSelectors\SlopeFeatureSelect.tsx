"use client";

import { useEffect } from "react";
import { Selector } from "~/components/Selector";
import { api } from "~/trpc/react";

export const SlopeFeatureSelect = ({
  raceId,
  value,
  onChange,
}: {
  raceId: string;
  value: string;
  onChange: (value: string) => void;
}) => {
  const { data: features } = api.snow.getSlopeFeatures.useQuery({ raceId });

  useEffect(() => {
    onChange(features?.[0]?.id ?? "");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [features]);

  const options = features?.map((x) => ({
    label: `${x.featureNumber}.${x.type}`,
    value: x.id,
  }));

  return (
    <Selector
      label="Obstacle"
      options={options}
      value={value}
      onValueChange={onChange}
    />
  );
};
