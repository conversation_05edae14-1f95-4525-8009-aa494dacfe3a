"use client";

import { useParams } from "next/navigation";
import { useEffect, useMemo } from "react";
import { toast } from "react-hot-toast";
import { shotputTags } from "~/lib/enums/shotput";
import { getFilteredTags } from "~/lib/utils";
import { api } from "~/trpc/react";
import { useStore } from "./store";
import type { TagChange } from "~/lib/interface";
import { TagAction } from "~/lib/enums/enums";

export const useShotputTags = () => {
  const utils = api.useUtils();

  const params = useParams<{ id: string }>();
  const videoId = params.id;

  const tagFilterValues = useStore((state) => state.tagFilterValues);
  const currentFrame = useStore((state) => state.currentFrame);
  const filteredTags = useStore((state) => state.filteredTags);
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const shotputThrow = useStore((state) => state.shotputThrow);
  const setFilteredTags = useStore((state) => state.setFilteredTags);
  const setShotputThrow = useStore((state) => state.setShotputThrow);
  const setTagChanges = useStore((state) => state.setTagChanges);

  const { data: tags, isLoading } = api.shotput.getTags.useQuery(
    { id: videoId },
    { enabled: !!videoId },
  );

  const firstThrow = tags?.throws?.[0];

  useEffect(() => {
    if (!firstThrow || !!shotputThrow) return;
    setShotputThrow(firstThrow);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [firstThrow]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const throwTags =
    tags?.tags.filter((x) => x.throwId === shotputThrow?.id) ?? [];

  const newFilteredTags = useMemo(
    () =>
      getFilteredTags({
        sport: "shotput",
        athleteId: selectedAthlete,
        tags: throwTags,
        tagTypes: shotputTags,
        filterValues: tagFilterValues,
      }),
    [selectedAthlete, throwTags, tagFilterValues],
  );

  useEffect(() => {
    if (JSON.stringify(newFilteredTags) !== JSON.stringify(filteredTags)) {
      setFilteredTags(newFilteredTags);
    }
  }, [newFilteredTags, filteredTags, setFilteredTags]);

  const { mutate: upsertTag } = api.shotput.upsertTag.useMutation({
    onSuccess: () => {
      void utils.shotput.getTags.invalidate({ id: videoId }); //todo
    },
    onError: (error) => {
      console.error(error);
      toast.error("Failed to add tag");
      void utils.shotput.getTags.invalidate({ id: videoId });
    },
  });

  const { mutate: deleteTag } = api.shotput.deleteTag.useMutation({
    onError: (error) => {
      console.error(error);
      toast.error("Failed to delete tag");
      void utils.shotput.getTags.invalidate({ id: videoId });
    },
  });

  const { mutate: deleteTags } = api.shotput.deleteTags.useMutation({
    onError: (error) => {
      console.error(error);
      toast.error("Failed to delete tags");
      void utils.shotput.getTags.invalidate({ id: videoId });
    },
  });

  const onAddTag = (tag: string, frame?: number) => {
    if (!shotputThrow || !tag) {
      toast.error("Missing shotput throw");
      return;
    }

    const existingTag = throwTags.find((x) => x.tag === tag);

    upsertTag({
      id: existingTag ? +existingTag.id : undefined,
      throwId: shotputThrow.id,
      tag,
      frame: frame ?? currentFrame,
    });
  };

  const onDeleteTag = (id: string) => {
    deleteTag({ id: +id });
    utils.shotput.getTags.setData({ id: videoId }, (data) => {
      if (!data) return { tags: [], throws: [] };
      return {
        ...data,
        tags: data.tags.map((x) =>
          x.id === id ? { ...x, isDeleted: true } : x,
        ),
      };
    });
  };

  const onDeleteTags = (ids: string[]) => {
    deleteTags({ ids: ids.map((x) => +x) });
    utils.shotput.getTags.setData({ id: videoId }, (data) => {
      if (!data) return { tags: [], throws: [] };
      return {
        ...data,
        tags: data.tags.map((x) =>
          ids.includes(x.id.toString()) ? { ...x, isDeleted: true } : x,
        ),
      };
    });
  };
  const removedAITags: TagChange[] = throwTags
    .filter((x) => x.isDeleted && x.athleteId)
    .map((x) => ({
      tagId: x.id,
      athleteId: [x.athleteId],
      action: TagAction.remove,
      tagType: [x.tag],
      frame: [x.frame],
    }));

  const humanAddedTags: TagChange[] = throwTags
    .filter((x) => !x.aiFrame)
    .map((x) => ({
      tagId: x.id,
      athleteId: [x.athleteId],
      action: TagAction.add,
      tagType: [x.tag],
      frame: [x.frame],
    }));

  const humanUpdatedTags: TagChange[] = throwTags
    .filter((x) => !!x.aiFrame && x.aiFrame !== x.frame)
    .map((x) => ({
      tagId: x.id,
      athleteId: [x.athleteId],
      action: TagAction.update,
      tagType: [x.tag],
      frame: [x.aiFrame ?? 0, x.frame],
    }));

  useEffect(() => {
    setTagChanges({
      humanAddedTags,
      humanUpdatedTags,
      removedAITags,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [removedAITags, humanAddedTags, humanUpdatedTags]);

  return { tags, isLoading, onAddTag, onDeleteTag, onDeleteTags };
};
