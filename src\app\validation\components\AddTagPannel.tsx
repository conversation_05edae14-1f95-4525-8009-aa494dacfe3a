import { Card } from "~/components/Card";
import { HotkeyButton } from "~/components/HotkeyButton";
import { useStore } from "~/hooks/store";
import { VideoStatus } from "~/lib/enums/enums";
import type { TagUI } from "~/lib/interface";
import { getSport } from "~/lib/utils";

export const AddTagPannel = ({
  tags,
  textClassName,
  onAddTag,
}: {
  tags: Record<string, TagUI[]>;
  textClassName?: string;
  onAddTag: (tag: string, frame?: number) => void;
}) => {
  const videoSummary = useStore((state) => state.videoSummary);
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const sport = getSport(videoSummary);

  const disabled =
    !selectedAthlete ||
    !videoSummary ||
    videoSummary.status !== VideoStatus.Tagged_by_AI;

  return (
    <Card className="gap-2.5 text-smallLabel">
      <p className="uppercase text-black/60">Manual tag updates</p>
      {Object.entries(tags).map(([key, options]) => {
        return (
          <div key={key} className="flex gap-[5px]">
            <p className="w-[53px] capitalize text-black/60">{key}:</p>
            <div className="grid flex-1 grid-cols-5 gap-x-0.5 gap-y-[5px]">
              {options.map((tag, index) => {
                if (!tag) return <div key={index} />;
                return (
                  <HotkeyButton
                    key={tag.value}
                    disabled={disabled}
                    textClassName={textClassName}
                    showDescription
                    tag={tag}
                    onClick={() => onAddTag(tag.value)}
                    sport={sport}
                  />
                );
              })}
            </div>
          </div>
        );
      })}
    </Card>
  );
};
