"use client";

type LineColour = "red" | "green" | "blue";

interface LineColourPickerProps {
  selected: LineColour;
  onChange: (colour: LineColour) => void;
}

export const LineColourPicker = ({
  selected,
  onChange,
}: LineColourPickerProps) => {
  const colours: LineColour[] = ["red", "green", "blue"];

  return (
    <div className="flex items-center gap-3">
      <label className="text-sm font-medium">Line Colour:</label>
      {colours.map((x) => (
        <label key={x} className="flex items-center gap-1 text-sm capitalize">
          <input
            type="radio"
            name="lineColor"
            value={x}
            checked={selected === x}
            onChange={() => onChange(x)}
          />
          {x}
        </label>
      ))}
    </div>
  );
};
