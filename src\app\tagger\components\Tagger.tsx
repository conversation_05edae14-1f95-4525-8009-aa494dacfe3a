"use client";

import { Parser } from "m3u8-parser";
import { useParams } from "next/navigation";
import { useEffect } from "react";
import { VideoSummaryCard } from "~/app/_components/VideoSummaryCard";
import { AthleteSelect } from "~/app/validation/components/AthleteSelect";
import { TaggerHighJumpForm } from "~/components/highJump/TaggerHighJumpForm";
import { VideoPlayer } from "~/components/player/Player";
import { SnowFormContainer } from "~/components/snow/SnowFormContainer";
import { useStore } from "~/hooks/store";
import { Sport } from "~/lib/enums/enums";
import { getAthleteOptions, getSport, getTimelineTagTypes } from "~/lib/utils";
import type { GetVideoInfoOutput } from "~/server/api/routers/video";
import { api } from "~/trpc/react";
import { TaggerShotputForm } from "./TaggerShotputForm";
import { SwimmingForm } from "./TaggerSwimmingForm";
import { TaggerBoxingForm } from "~/components/boxing/TaggerBoxingForm";
import { TaggerDiscusForm } from "~/components/discus/TaggerDiscusForm";
import { SprintTagger } from "./SprintForm";
import type { GetPoseInfoOutput } from "~/server/api/routers/pose";

export const Tagger = ({
  videoSummary,
  poseData,
}: {
  videoSummary: GetVideoInfoOutput;
  poseData: GetPoseInfoOutput;
}) => {
  const params = useParams<{ id: string }>();
  const sport = getSport(videoSummary);
  const videoId = params.id;

  const [{ m3u8Text }] = api.videos.getVideoSourceStream.useSuspenseQuery({
    id: videoId,
  });

  const parser = new Parser();
  parser.push(m3u8Text);
  parser.end();

  const m3u8Segments = parser.manifest.segments;

  const athleteOptions = getAthleteOptions(videoSummary);
  const timelineTagTypes = getTimelineTagTypes(videoSummary);

  const setVideoSummary = useStore((state) => state.setVideoSummary);
  const resetStore = useStore((state) => state.resetStore);

  //initialize on leaving page
  useEffect(() => {
    return () => {
      resetStore();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setVideoSummary(videoSummary);
  }, [videoSummary, setVideoSummary]);

  return (
    <div className="grid h-full grid-cols-12 gap-4">
      <VideoPlayer
        m3u8Segments={m3u8Segments}
        m3u8Text={m3u8Text}
        tagTypes={timelineTagTypes}
        poseData={poseData}
      />
      <div className="col-span-3 flex flex-1 flex-col gap-2.5">
        <VideoSummaryCard />
        {sport === Sport.swimming && <SwimmingForm />}
        {sport === "shotput" && <TaggerShotputForm />}

        {sport === Sport.boxing && <TaggerBoxingForm />}
        {sport === Sport.snowsports && (
          <SnowFormContainer
            raceId={videoSummary.snowSportsRaceId ?? undefined}
          />
        )}
        {sport === "discus" && (
          <>
            <AthleteSelect athleteOptions={athleteOptions} disableDelete />
            <TaggerDiscusForm />
          </>
        )}
        {sport === "highJump" && (
          <>
            <AthleteSelect athleteOptions={athleteOptions} disableDelete />
            <TaggerHighJumpForm />
          </>
        )}
        {sport === "sprinting" && (
          <>
            <h1>Sprinting Sport</h1>
            <AthleteSelect athleteOptions={athleteOptions} disableDelete />
            <SprintTagger />
          </>
        )}
      </div>
    </div>
  );
};
