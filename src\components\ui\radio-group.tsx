"use client";

import * as RadioGroupPrimitive from "@radix-ui/react-radio-group";
import * as React from "react";
import { useHotkeys } from "react-hotkeys-hook";

import { cn } from "~/lib/utils";

const RadioGroup = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>
>(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Root
      className={cn("grid gap-2", className)}
      {...props}
      ref={ref}
    />
  );
});
RadioGroup.displayName = RadioGroupPrimitive.Root.displayName;

interface RadioGroupItemProps
  extends React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item> {
  text?: string;
  // bg?: string;
  hotkey?: {
    value: string;
    onClick: () => void;
    className?: string;
  };
}

const RadioGroupItem = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Item>,
  RadioGroupItemProps
>(({ className, hotkey, ...props }, ref) => {
  useHotkeys(hotkey?.value ?? "fakeradiokey", () => hotkey?.onClick());
  return (
    <RadioGroupPrimitive.Item
      ref={ref}
      className={cn(
        "group relative aspect-square h-4 w-4 rounded-[2.5px] border border-black/10 bg-seaSalt-40 focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:text-white",
        className,
        hotkey?.className,
      )}
      onClick={() => hotkey?.onClick()}
      {...props}
    >
      <p className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-smallLabel uppercase">
        {props.text}
      </p>
      <RadioGroupPrimitive.Indicator className="flex items-center justify-center">
        <div
          className={cn(
            "h-4 w-4 rounded-[2.5px] bg-seaSalt-k40",
            hotkey?.className,
          )}
        ></div>
      </RadioGroupPrimitive.Indicator>
    </RadioGroupPrimitive.Item>
  );
});
RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;

export { RadioGroup, RadioGroupItem };
