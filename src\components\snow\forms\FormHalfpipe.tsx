"use client";

import { FormItem, type FormItemProps } from "../../FormItem";
import type { UseFormReturn } from "react-hook-form";
import type { SnowFormProps } from "../SnowFormContainer";
import { FormItemType } from "~/lib/enums/enums";
import {
  LandingDescription,
  landingDescriptions,
  snowJumpSpinDirections,
  SnowLandingType,
  snowLandingTypes,
  snowLandingZones,
  type SnowJumpSpinDirection,
  type SnowLandingZone,
} from "~/lib/enums/snow";
import { JumpTypeSelect } from "../enumSelectors/JumpTypeSelect";
import { ModifierSelect } from "../enumSelectors/ModifierSelect";
import { SpinTypeSelect } from "../enumSelectors/SpinTypeSelect";
import { spinAmounts } from "~/lib/constants";
import { GrabSelect } from "../enumSelectors/GrabSelect";
import { ExecutionSelect } from "../enumSelectors/ExecutionSelect";
import { Card } from "~/components/Card";
import { Label } from "~/components/ui/label";
import type { HalfPipeTag } from "~/server/db/snowSchema";
import { useStore } from "~/hooks/store";
import { api } from "~/trpc/react";
import { landingDescriptionFormItem } from "../enumSelectors/LandingDescription";

interface Props {
  isSnowboard: boolean;
  form: UseFormReturn<SnowFormProps>;
  tag?: HalfPipeTag;
}

interface HalfPipeFormItemProps extends FormItemProps {
  name: keyof SnowFormProps;
}

export const FormHalfPipe = ({ form, isSnowboard, tag }: Props) => {
  const isJump = isSnowboard;

  const { data: jumpTypeOptions } = api.snowOptions.getJumpTypes.useQuery({
    isSnowboard,
  });
  const { data: modifierOptions } = api.snowOptions.getModifiers.useQuery();
  const { data: spinTypeOptions } = api.snowOptions.getSpinTypes.useQuery();
  const { data: grabOptions } = api.snowOptions.getGrabs.useQuery({
    isSnowboard,
  });
  const { data: executionOptions } = api.snowOptions.getExecutions.useQuery({
    isJump: isJump,
  });

  const currentFrame = useStore((state) => state.currentFrame);
  const { setValue, getValues } = form;

  const landingType = form.watch("landingType");

  const jumpItems: HalfPipeFormItemProps[] = [
    {
      name: "amplitude",
      title: "Amplitude",
      type: FormItemType.number,
      className: "grid grid-cols-2 col-span-full",
      hotkey: {
        value: "e",
        className: "bg-fuchsia-20 text-fuchsia-k40 text-xs",
        onClick: () => {
          if (!tag) return;
          setValue("amplitude", currentFrame?.toString() ?? "");
        },
      },
    },
    {
      name: "jumpTypeId",
      title: "Jump Type",
      type: FormItemType.editSelect,
      className: "flex flex-col col-span-2 items-start gap-[5px] w-full",
      hotkey: {
        value: "r",
        className: "bg-neonGreen-20 text-neonGreen-k40 text-xs",
        onClick: () => {
          const options = jumpTypeOptions;
          if (!options?.length) return;

          const currentValue = getValues("jumpTypeId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("jumpTypeId", options[nextIndex]?.value ?? null);

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <JumpTypeSelect
          isSnowboard={isSnowboard}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
    {
      name: "jumpTakeoffModifierId",
      title: "Spin Modifier(take off)",
      type: FormItemType.editSelect,
      className: "grid col-span-full",
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <ModifierSelect value={field.value} onSelect={field.onChange} />,
      hotkey: {
        value: "t",
        className: "bg-neonGreen-20 text-neonGreen-k40 text-xs",
        onClick: () => {
          const options = modifierOptions;
          if (!options?.length) return;

          const currentValue = getValues("jumpTakeoffModifierId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("jumpTakeoffModifierId", options[nextIndex]?.value ?? null);

          if (!tag) return;
        },
      },
    },
    {
      name: "progression",
      title: "Progression",
      type: FormItemType.radio,
      className: "flex items-center col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "y",
        className:
          "text-xs group-data-[state=checked]:bg-fuchsia-20 text-black data-[state=checked]:text-fuchsia-k40",
        onClick: () => {
          const currentValue = form.getValues("progression");
          setValue("progression", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Yes", value: "1" },
        { label: "No", value: "0" },
      ],
    },
    {
      name: "switch",
      title: "Orientation",
      type: FormItemType.radio,
      className: "flex items-center col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "u",
        className:
          "group-data-[state=checked]:bg-orange-20 text-black data-[state=checked]:text-orange-k40",
        onClick: () => {
          const currentValue = form.getValues("switch");
          setValue("switch", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Switch", value: "1" },
        { label: "Forwards", value: "0" },
      ],
    },
    {
      name: "cab",
      title: "Cab",
      type: FormItemType.radio,
      className: "flex items-center col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "i",
        className:
          "group-data-[state=checked]:bg-orange-k40 text-black data-[state=checked]:text-orange-20",
        onClick: () => {
          const currentValue = form.getValues("cab");
          setValue("cab", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Yes", value: "1" },
        { label: "No", value: "0" },
      ],
    },
    {
      name: "spinDirection",
      title: "Spin Direction",
      type: FormItemType.select,
      placeholder: "Select Spin Direction",
      className: "grid",
      defaultValue: "none",
      options: snowJumpSpinDirections.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "a",
        className: "bg-purple-20 text-purple-k40 text-xs",
        onClick: () => {
          const currentValue = getValues(
            "spinDirection",
          ) as SnowJumpSpinDirection;
          const currentIndex = snowJumpSpinDirections.indexOf(
            currentValue ?? "none",
          );
          const nextIndex = (currentIndex + 1) % snowJumpSpinDirections.length;
          setValue(
            "spinDirection",
            snowJumpSpinDirections[nextIndex] as string,
          );
        },
      },
    },
    {
      name: "spinTypeId",
      title: "Spin Type",
      type: FormItemType.editSelect,
      className: "grid",
      hotkey: {
        value: "s",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = spinTypeOptions;
          if (!options?.length) return;

          const currentValue = getValues("spinTypeId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("spinTypeId", options[nextIndex]?.value ?? null);

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <SpinTypeSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "spinAmount",
      title: "Spin Amount",
      type: FormItemType.select,
      className: "grid",
      placeholder: "Select Spin Amount",
      defaultValue: "0",
      options: spinAmounts.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "d",
        className: "bg-red-20 text-red-k40 text-xs",
        onClick: () => {
          const currentValue = getValues("spinAmount");
          const currentIndex = spinAmounts.indexOf(currentValue ?? "0");
          const nextIndex = (currentIndex + 1) % spinAmounts.length;
          setValue("spinAmount", spinAmounts[nextIndex] ?? null);
        },
      },
    },
    {
      name: "spinModifierId",
      title: "Spin Modifier",
      type: FormItemType.editSelect,
      className: "grid",
      hotkey: {
        value: "f",
        className: "bg-springGreen-20 text-springGreen-k40 text-xs",
        onClick: () => {
          const options = modifierOptions;
          if (!options?.length) return;

          const currentValue = getValues("spinModifierId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("spinModifierId", options[nextIndex]?.value ?? "");

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <ModifierSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "grabTypeId",
      title: "Grab Type",
      type: FormItemType.editSelect,
      className: "grid col-span-full",
      hotkey: {
        value: "g",
        className: "bg-neonGreen-k40 text-neonGreen-20 text-xs",
        onClick: () => {
          const options = grabOptions;
          if (!options?.length) return;

          const currentValue = getValues("grabTypeId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("grabTypeId", options[nextIndex]?.value ?? "");

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <GrabSelect
          isSnowboard={isSnowboard}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
    {
      name: "grabStart",
      title: "Grab Start Frame",
      className: "grid",
      type: FormItemType.number,
      hotkey: {
        value: "3",
        className: "text-xs",
        onClick: () => {
          if (!currentFrame) return;
          setValue("grabStart", currentFrame.toString());
        },
      },
    },
    {
      name: "grabEnd",
      title: "Grab End Frame",
      className: "grid",
      type: FormItemType.number,
      hotkey: {
        value: "4",
        className: "text-xs",
        onClick: () => {
          if (!currentFrame) return;
          setValue("grabEnd", currentFrame.toString());
        },
      },
    },
    {
      name: "takeOffFrame",
      title: "Take-off Frame",
      className: "grid",
      type: FormItemType.number,
      hotkey: {
        value: "5",
        className: "text-xs",
        onClick: () => {
          if (!currentFrame) return;
          setValue("takeOffFrame", currentFrame.toString());
        },
      },
    },
    {
      name: "landingFrame",
      title: "Landing Frame",
      className: "grid",
      type: FormItemType.number,
      hotkey: {
        value: "6",
        className: "text-xs",
        onClick: () => {
          if (!currentFrame) return;
          setValue("landingFrame", currentFrame.toString());
        },
      },
    },
  ];

  const baseItems: HalfPipeFormItemProps[] = [
    {
      name: "executionId",
      title: "Execution",
      type: FormItemType.editSelect,
      className: "grid",
      hotkey: {
        value: "b",
        className: "bg-neonGreen-k40 text-neonGreen-20 text-xs",
        onClick: () => {
          const options = executionOptions;
          if (!options?.length) return;

          const currentValue = getValues("executionId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("executionId", options[nextIndex]?.value ?? "");

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <ExecutionSelect
          isJump={true}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
    {
      name: "landingZone",
      title: "Landing Zone:",
      type: FormItemType.radio,
      className: "flex items-start",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "z",
        className:
          "group-data-[state=checked]:bg-neonGreen text-black/60 text-xs data-[state=checked]:text-black/60",
        onClick: () => {
          const currentValue = form.getValues("landingZone");
          const currentIndex = snowLandingZones.indexOf(
            currentValue as SnowLandingZone,
          );
          const nextIndex = (currentIndex + 1) % snowLandingZones.length;
          setValue("landingZone", snowLandingZones[nextIndex] ?? "");
        },
      },
      options: snowLandingZones.map((x) => ({
        label: x,
        value: x,
      })),
    },
    {
      name: "landingType",
      title: "Landing Type:",
      type: FormItemType.radio,
      className: "flex items-start",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "x",
        className: "group-data-[state=checked]:bg-blue text-black text-xs",
        onClick: () => {
          const currentValue = getValues("landingType");
          const currentIndex = snowLandingTypes.indexOf(
            currentValue as SnowLandingType,
          );
          const nextIndex = (currentIndex + 1) % snowLandingTypes.length;
          setValue("landingType", snowLandingTypes[nextIndex] ?? "");
        },
      },
      options: snowLandingTypes.map((x) => ({
        label: x,
        value: x,
      })),
    },
  ];

  const items =
    landingType === SnowLandingType.none
      ? baseItems
      : [
          ...baseItems,
          landingDescriptionFormItem({
            options: landingDescriptions
              .filter((x) => x !== LandingDescription["lost ski"])
              .map((x) => ({
                label: x,
                value: x,
              })),
          }),
        ];

  return (
    <>
      <Card className="grid grid-cols-2 gap-2.5">
        <div className="col-span-full">
          <Label className="!text-smallLabel uppercase text-black/60">
            JUMP DETAILS:
          </Label>
        </div>
        {jumpItems.map((item) => (
          <FormItem key={item.name} control={form.control} {...item} />
        ))}
      </Card>
      <Card className="gap-2.5">
        <Label className="!text-smallLabel uppercase text-black/60">
          Descriptors:
        </Label>
        {items.map((item) => (
          <FormItem key={item.name} control={form.control} {...item} />
        ))}
      </Card>
    </>
  );
};
