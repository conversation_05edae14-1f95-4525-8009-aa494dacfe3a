import type { M3u8Segment } from "./interface";

const naighbourSegmentsCount = 1;

export const getSegmentsByFrameNumber = ({
  segments,
  frameNumber,
  fps,
}: {
  segments: M3u8Segment[];
  frameNumber: number;
  fps: number;
}) => {
  const frameTime = frameNumber / fps;

  let accumulatedDuration = 0;
  const forwardSegments = [];
  const backwardSegments = [];

  for (let i = 0; i < segments.length; i++) {
    const currentSegment = segments[i]!;
    accumulatedDuration += currentSegment.duration;
    if (accumulatedDuration >= frameTime) {
      forwardSegments.push({ ...currentSegment, index: i });
      for (let j = 1; j <= naighbourSegmentsCount; j++) {
        const forwardSegment = segments[i + j];
        if (!forwardSegment) break;
        forwardSegments.push({ ...forwardSegment, index: i + j });
      }
      for (let j = 1; j <= naighbourSegmentsCount; j++) {
        if (i - j < 0) break;
        const backwardSegment = segments[i - j];
        if (!backwardSegment) break;
        backwardSegments.push({ ...backwardSegment, index: i - j });
      }
      break;
    }
  }
  return { forwardSegments, backwardSegments };
};
