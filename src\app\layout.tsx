import type React from "react";
import "~/styles/globals.css";
import type { Metadata } from "next";
import { TRPCReactProvider } from "~/trpc/react";
import SessionProvider from "./SessionProvider";
import { Toaster } from "react-hot-toast";
import { Header } from "./_components/Header";
import { Figtree } from "next/font/google";

export const metadata: Metadata = {
  title: "Sports analysis tool",
  description: "",
  icons: [{ rel: "icon", url: "/favicon.png" }],
};

const figtreeFonrt = Figtree({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700", "800", "900"],
  variable: "--font-figtree",
});

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      className={`h-screen border-black/10 ${figtreeFonrt.className}`}
    >
      <body className="h-full">
        <SessionProvider>
          <TRPCReactProvider>
            <div className="flex h-full flex-col">
              <Header />
              <main className="flex flex-1">{children}</main>
            </div>
          </TRPCReactProvider>
          <Toaster />
        </SessionProvider>
      </body>
    </html>
  );
}
