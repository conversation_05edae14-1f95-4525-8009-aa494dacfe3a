import { TRPCError } from "@trpc/server";
import { and, eq, inArray, sql } from "drizzle-orm";
import { z } from "zod";
import { Gender } from "~/lib/enums/enums";
import {
  SnowDiscipline,
  SnowEvent,
  SnowFeatureType,
  SnowRailSpinDirection,
  type SnowJumpSpinDirection,
  type SnowLandingType,
  type SnowLandingZone,
} from "~/lib/enums/snow";
import type {
  NewSnowSlopeJumpTag,
  NewSnowSlopeRailTag,
  NewSnowSlopeStyleTag,
  NewSnowSlopeTransition,
  NewSnowSlopeTransitionJumpTag,
  NewSnowSlopeTransitionRailTag,
  NewSnowSlopeTransitionTransitionTag,
} from "~/server/db/snowSchema";
import {
  landingDescriptionEnum,
  snowBigAirTags,
  snowFeatures,
  snowHalfPipeTags,
  snowSlopeJumpTags,
  snowSlopeRailTags,
  snowSlopeStyleTags,
  snowSlopeTransition,
  snowSlopeTransitionJumpTags,
  snowSlopeTransitionRailTags,
  snowSlopeTransitionTransitionTags,
} from "~/server/db/snowSchema";
import type { RouterOutputs } from "~/trpc/react";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import {
  snowTagGeneralInputs,
  snowTagGeneralTagInputs,
} from "../utils/apiInputs";
import { getAccessToken } from "../utils/pta/pta";
import {
  deleteRun,
  getSnowRace,
  getSnowsportRaces,
  upsertFeature,
  upsertRun,
  upsertSnowRace,
} from "../utils/pta/snow";
import { callVideoPortal } from "../utils/video";

type Outputs = RouterOutputs["snow"];

export type GetRaceOutput = Outputs["getRace"];

export const snowRouter = createTRPCRouter({
  getRacesByCompetitionId: protectedProcedure
    .input(z.object({ competitionId: z.string() }))
    .query(async ({ input, ctx }) => {
      const token = getAccessToken(ctx);
      const races = await getSnowsportRaces({
        token,
        competitionId: input.competitionId,
      });
      return races;
    }),
  updateVideoRace: protectedProcedure
    .input(z.object({ raceId: z.string(), videoId: z.string() }))
    .mutation(async ({ input }) => {
      await callVideoPortal(input.videoId, {
        method: "PUT",
        body: JSON.stringify({ snowSportsRaceId: input.raceId }),
      });
      return input;
    }),
  getRace: protectedProcedure
    .input(
      z.object({
        raceId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const token = getAccessToken(ctx);
      const races = await getSnowRace({
        token,
        raceId: input.raceId,
      });
      return races;
    }),
  upsertRace: protectedProcedure
    .input(
      z.object({
        id: z.string().optional(),
        videoId: z.string(),
        competitionId: z.string(),
        round: z.string(),
        date: z.string(),
        gender: z.nativeEnum(Gender),
        event: z.nativeEnum(SnowEvent),
        discipline: z.nativeEnum(SnowDiscipline),
      }),
    )
    .mutation(async ({ input }) => {
      const race = await upsertSnowRace(input);
      await callVideoPortal(input.videoId, {
        method: "PUT",
        body: JSON.stringify({ snowSportsRaceId: race.event_id }),
      });

      return race;
    }),
  upsertRun: protectedProcedure
    .input(
      z.object({
        id: z.string().optional(),
        resultId: z.string(),
        run: z.number(),
        score: z.number().optional(),
        sectionScore: z.number().optional(),
        overallScore: z.number().optional(),
      }),
    )
    .mutation(({ input }) => {
      return upsertRun(input);
    }),
  deleteRun: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      await deleteRun({ id: input.id });
    }),
  getTags: protectedProcedure
    .input(
      z.object({
        runId: z.string(),
        event: z.nativeEnum(SnowEvent),
        featureTypeId: z.string().optional(),
        sectionNum: z.number().optional(),
        trickNum: z.number().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      switch (input.event) {
        case SnowEvent.HALF_PIPE:
          const { trickNum } = input;
          const halfPipeConditions = [
            eq(snowHalfPipeTags.snowSportsRunId, input.runId),
          ];
          if (trickNum) {
            halfPipeConditions.push(eq(snowHalfPipeTags.trickNum, trickNum));
          }
          return (
            (await ctx.db.query.snowHalfPipeTags.findFirst({
              where: and(...halfPipeConditions),
              with: {
                landingDescriptions: true,
              },
            })) ?? null
          );
        case SnowEvent.BIG_AIR:
          return (
            (await ctx.db.query.snowBigAirTags.findFirst({
              where: eq(snowBigAirTags.snowSportsRunId, input.runId),
              with: {
                landingDescriptions: true,
              },
            })) ?? null
          );
        case SnowEvent.SLOPESTYLE:
          const { featureTypeId, sectionNum } = input;
          const andConditions = [
            eq(snowSlopeStyleTags.snowSportsRunId, input.runId),
          ];
          if (featureTypeId) {
            andConditions.push(eq(snowSlopeStyleTags.featureId, featureTypeId));
          }
          if (sectionNum) {
            andConditions.push(eq(snowSlopeStyleTags.sectionNum, sectionNum));
          }

          const slopeStyleTag = await ctx.db.query.snowSlopeStyleTags.findFirst(
            {
              where: and(...andConditions),
              with: {
                landingDescriptions: true,
                snowSlopeRail: true,
                snowSlopeJump: true,
                snowSlopeTransition: {
                  with: {
                    snowSlopeTransitionJump: true,
                    snowSlopeTransitionRail: true,
                    snowSlopeTransitionTransition: true,
                  },
                },
              },
            },
          );
          const transitionTag = slopeStyleTag?.snowSlopeTransition;
          return slopeStyleTag
            ? {
                ...slopeStyleTag,
                ...slopeStyleTag.snowSlopeRail,
                ...slopeStyleTag.snowSlopeJump,
                ...{
                  ...(transitionTag
                    ? {
                        ...transitionTag,
                        ...transitionTag.snowSlopeTransitionJump,
                        ...transitionTag.snowSlopeTransitionRail,
                        ...transitionTag.snowSlopeTransitionTransition,
                      }
                    : {}),
                },
                snowSlopeRail: undefined,
                snowSlopeJump: undefined,
                snowSlopeTransition: undefined,
              }
            : null;
      }
    }),
  getAllHalfPipeTags: protectedProcedure
    .input(z.object({ runId: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.query.snowHalfPipeTags.findMany({
        where: eq(snowHalfPipeTags.snowSportsRunId, input.runId),
        orderBy: snowHalfPipeTags.trickNum,
      });
    }),
  getSlopeFeatures: protectedProcedure
    .input(z.object({ raceId: z.string() }))
    .query(({ ctx, input }) => {
      return ctx.db.query.snowFeatures.findMany({
        where: eq(snowFeatures.raceId, input.raceId),
        orderBy: snowFeatures.featureNumber,
      });
    }),
  upsertSlopeFeature: protectedProcedure
    .input(
      z.object({
        upsert: z.array(
          z.object({
            id: z.string().optional(),
            featureNumber: z.number(),
            type: z.nativeEnum(SnowFeatureType),
            sections: z.string().nullable(),
            raceId: z.string(),
          }),
        ),
        delete: z.array(z.string()),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        if (input.delete.length > 0) {
          await tx
            .delete(snowFeatures)
            .where(inArray(snowFeatures.id, input.delete));
        }
        if (input.upsert.length > 0) {
          await tx
            .insert(snowFeatures)
            .values(input.upsert)
            .onDuplicateKeyUpdate({
              set: {
                featureNumber: sql`values(${snowFeatures.featureNumber})`,
                type: sql`values(${snowFeatures.type})`,
                sections: sql`values(${snowFeatures.sections})`,
                raceId: sql`values(${snowFeatures.raceId})`,
              },
            });
        }
      });
    }),
  upsertHalfPipeTag: protectedProcedure
    .input(
      z.object({
        ...snowTagGeneralInputs,
        runScore: z.number().optional(), //pta
        trickNum: z.number(),
        amplitude: z.number().nullish(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { resultId, run, runScore, landingDescriptions, ...rest } = input;

      await ctx.db.transaction(async (tx) => {
        //update run score in pta
        await upsertRun({
          id: input.snowSportsRunId,
          resultId,
          run,
          score: runScore,
        });

        const ptaFeature = await upsertFeature({
          id: input.id,
          runId: input.snowSportsRunId,
          trickNum: input.trickNum,
          trick: {
            switch: rest.switch ?? false,
            cab: rest.cab ?? false,
          },
          featureNum: input.trickNum,
        });

        const id = ptaFeature.id;

        await tx
          .insert(snowHalfPipeTags)
          .values({ ...rest, id })
          .onDuplicateKeyUpdate({
            set: {
              ...rest,
              id: undefined,
            },
          });

        //reset landing description
        await tx
          .delete(landingDescriptionEnum)
          .where(eq(landingDescriptionEnum.halfpipeTagId, id));

        if (landingDescriptions && landingDescriptions.length > 0) {
          await tx.insert(landingDescriptionEnum).values(
            landingDescriptions.map((x) => ({
              halfpipeTagId: id,
              value: x,
            })),
          );
        }
      });
      return input;
    }),
  upsertBigAirTag: protectedProcedure
    .input(
      z.object({
        ...snowTagGeneralInputs,
        score: z.number().nullish(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { resultId, run, landingDescriptions, ...rest } = input;

      await ctx.db.transaction(async (tx) => {
        //update run score in pta
        await upsertRun({
          id: input.snowSportsRunId,
          resultId,
          run,
        });

        const ptaFeature = await upsertFeature({
          id: input.id,
          runId: input.snowSportsRunId,
          trickNum: 1,
          trick: {
            switch: rest.switch ?? false,
            cab: rest.cab ?? false,
          },
          featureNum: 1,
        });

        const id = ptaFeature.id;

        await tx
          .insert(snowBigAirTags)
          .values({ ...rest, id })
          .onDuplicateKeyUpdate({
            set: {
              ...rest,
              id: undefined,
            },
          });

        //reset landing description
        await tx
          .delete(landingDescriptionEnum)
          .where(eq(landingDescriptionEnum.bigAirTagId, id));

        if (landingDescriptions && landingDescriptions.length > 0) {
          await tx.insert(landingDescriptionEnum).values(
            landingDescriptions.map((x) => ({
              bigAirTagId: id,
              value: x,
            })),
          );
        }
      });
      return input;
    }),
  upsertSlopeStyleTag: protectedProcedure
    .input(
      z.object({
        videoId: z.string(),
        resultId: z.string(), // pta
        raceId: z.string(), // pta
        run: z.number(), //pta
        runScore: z.number().optional(), //pta
        overallScore: z.number().optional(), //pta
        snowSportsRunId: z.string(), // get from pta, write to DB
        tags: z.array(
          z.object({
            ...snowTagGeneralTagInputs,
            score: z.number().nullish(),
            // featureType: z.nativeEnum(SnowFeatureType), //obstacle: jump, rail, transition
            featureId: z.string(),
            sectionNum: z.number(), //section feature number
            sectionType: z.nativeEnum(SnowFeatureType), //section type: jump, rail, transition
            transitionTypeId: z.number().nullish(), //for transition transition only
            //rail fields
            rail: z
              .object({
                railFeatureId: z.number().nullable(),
                railInSpinDirection: z
                  .nativeEnum(SnowRailSpinDirection)
                  .nullable(),
                railInSpinAmount: z.number().nullable(),
                railInSpinModifierId: z.number().nullable(),
                railInGrabId: z.number().nullable(),
                railTrickId: z.number().nullable(),
                railOnSpinDirection: z
                  .nativeEnum(SnowRailSpinDirection)
                  .nullable(),
                railOnSpinAmount: z.number().nullable(),
                railOnSpinModifierId: z.number().nullable(),
                railOnGrabId: z.number().nullable(),
                railOutSpinDirection: z
                  .nativeEnum(SnowRailSpinDirection)
                  .nullable(),
                railOutSpinAmount: z.number().nullable(),
                railOutSpinModifierId: z.number().nullable(),
                railOutGrabId: z.number().nullable(),
              })
              .optional(),
          }),
        ),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { resultId, run, runScore, overallScore } = input;

      const raceFeatures = await ctx.db.query.snowFeatures.findMany({
        where: eq(snowFeatures.raceId, input.raceId),
      });
      await ctx.db.transaction(async (tx) => {
        for (const tag of input.tags) {
          const feature = raceFeatures.find((x) => x.id === tag.featureId);
          if (!feature) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: `Feature with id ${tag.featureId} not found`,
            });
          }

          const ptaFeature = await upsertFeature({
            id: tag.id,
            runId: input.snowSportsRunId,
            trickNum: tag.sectionNum,
            trick: {
              switch: tag.switch ?? false,
              cab: tag.cab ?? false,
            },
            featureNum: feature.featureNumber,
          });

          // Use the PTA-generated ID for everything
          const id = ptaFeature.id;

          // Create parent tag with the official PTA ID
          const parentTag: NewSnowSlopeStyleTag = {
            id: id,
            startFrame: tag.startFrame,
            endFrame: tag.endFrame,
            featureId: tag.featureId,
            sectionNum: tag.sectionNum, // jump and rail is always 1
            sectionType: tag.sectionType,
            snowSportsRunId: input.snowSportsRunId,
            videoId: input.videoId,
            score: tag.score,
          };

          await tx
            .insert(snowSlopeStyleTags)
            .values(parentTag)
            .onDuplicateKeyUpdate({
              set: {
                ...parentTag,
                id: undefined,
              },
            });

          //reset landing description
          await tx
            .delete(landingDescriptionEnum)
            .where(eq(landingDescriptionEnum.slopeTagId, id));

          if (tag.landingDescriptions && tag.landingDescriptions.length > 0) {
            await tx.insert(landingDescriptionEnum).values(
              tag.landingDescriptions.map((x) => ({
                slopeTagId: id,
                value: x,
              })),
            );
          }

          const featureType = feature.type;

          switch (featureType) {
            case SnowFeatureType.jump:
              const jumpObj: NewSnowSlopeJumpTag = {
                snowSlopeStyleTagId: id,
                jumpTakeoffModifierId: tag.jumpTakeoffModifierId,
                takeOffFrame: tag.takeOffFrame,
                landingFrame: tag.landingFrame,
                grabStart: tag.grabStart,
                grabEnd: tag.grabEnd,
                jumpTypeId: tag.jumpTypeId,
                spinDirection: tag.spinDirection as SnowJumpSpinDirection,
                spinTypeId: tag.spinTypeId,
                spinAmount: tag.spinAmount,
                spinModifierId: tag.spinModifierId,
                switch: tag.switch,
                cab: tag.cab,
                progression: tag.progression,
                grabTypeId: tag.grabTypeId,
                executionId: tag.executionId,
                landingZone: tag.landingZone as SnowLandingZone,
                landingType: tag.landingType as SnowLandingType,
              };
              await tx
                .insert(snowSlopeJumpTags)
                .values(jumpObj)
                .onDuplicateKeyUpdate({ set: jumpObj });
              break;
            case SnowFeatureType.rail:
              const railObj: NewSnowSlopeRailTag = {
                snowSlopeStyleTagId: id,
                takeOffFrame: tag.takeOffFrame,
                landingFrame: tag.landingFrame,
                grabStart: tag.grabStart,
                grabEnd: tag.grabEnd,
                progression: tag.progression,
                switch: tag.switch,
                cab: tag.cab,
                ...tag.rail,
                executionId: tag.executionId,
                landingZone: tag.landingZone as SnowLandingZone,
                landingType: tag.landingType as SnowLandingType,
              };
              console.log("RAIL OBJECT", railObj);
              await tx
                .insert(snowSlopeRailTags)
                .values(railObj)
                .onDuplicateKeyUpdate({ set: railObj });
              break;
            case SnowFeatureType.transition:
              const transitionObj: NewSnowSlopeTransition = {
                snowSlopeStyleTagId: id,
                progression: tag.progression,
                switch: tag.switch,
                cab: tag.cab,
                grabStart: tag.grabStart,
                grabEnd: tag.grabEnd,
                takeOffFrame: tag.takeOffFrame,
                landingFrame: tag.landingFrame,
                executionId: tag.executionId,
                landingZone: tag.landingZone as SnowLandingZone,
                landingType: tag.landingType as SnowLandingType,
              };
              await tx
                .insert(snowSlopeTransition)
                .values(transitionObj)
                .onDuplicateKeyUpdate({ set: transitionObj });
              switch (tag.sectionType) {
                case SnowFeatureType.jump:
                  const tJumpObj: NewSnowSlopeTransitionJumpTag = {
                    jumpTakeoffModifierId: tag.jumpTakeoffModifierId,
                    snowSlopeStyleTagId: id,
                    jumpTypeId: tag.jumpTypeId,
                    spinDirection: tag.spinDirection as SnowJumpSpinDirection,
                    spinTypeId: tag.spinTypeId,
                    spinAmount: tag.spinAmount,
                    spinModifierId: tag.spinModifierId,
                    grabTypeId: tag.grabTypeId,
                  };
                  await tx
                    .insert(snowSlopeTransitionJumpTags)
                    .values(tJumpObj)
                    .onDuplicateKeyUpdate({ set: tJumpObj });
                  break;
                case SnowFeatureType.rail:
                  const tRailObj: NewSnowSlopeTransitionRailTag = {
                    snowSlopeStyleTagId: id,
                    ...tag.rail,
                  };
                  await tx
                    .insert(snowSlopeTransitionRailTags)
                    .values(tRailObj)
                    .onDuplicateKeyUpdate({ set: tRailObj });
                  break;
                case SnowFeatureType.transition:
                  if (!tag.transitionTypeId) {
                    throw new TRPCError({
                      code: "BAD_REQUEST",
                      message:
                        "Transition type id is required for transition section",
                    });
                  }
                  const tTransitionObj: NewSnowSlopeTransitionTransitionTag = {
                    snowSlopeStyleTagId: id,
                    jumpTakeoffModifierId: tag.jumpTakeoffModifierId,
                    transitionTypeId: tag.transitionTypeId,
                    spinDirection: tag.spinDirection as SnowJumpSpinDirection,
                    spinTypeId: tag.spinTypeId,
                    spinAmount: tag.spinAmount,
                    spinModifierId: tag.spinModifierId,
                    grabTypeId: tag.grabTypeId,
                  };
                  await tx
                    .insert(snowSlopeTransitionTransitionTags)
                    .values(tTransitionObj)
                    .onDuplicateKeyUpdate({ set: tTransitionObj });
                  break;
              }
              break;
            default:
              break;
          }
        }

        await upsertRun({
          id: input.snowSportsRunId,
          resultId,
          run,
          score: runScore,
          overallScore,
        });
      });
    }),
});
