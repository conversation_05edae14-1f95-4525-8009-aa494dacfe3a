import { z } from "zod";
import { BoxingCorner, BoxingPunchType } from "~/lib/enums/boxing";
import { boxingPunches } from "~/server/db/boxingSchema";
import type { RouterOutputs } from "~/trpc/react";
import { createTRPCRouter, protectedProcedure } from "../trpc";

type Outputs = RouterOutputs["boxing"];

export type GetBoxingTagsOutput = Outputs["getTags"];

export const boxingRouter = createTRPCRouter({
  getTags: protectedProcedure
    .input(z.object({ videoId: z.string() }))
    .query(async ({ input, ctx }) => {
      // return ctx.db.query..findMany({
      //   where: eq(highJumps.videoId, input.videoId),
      //   with: {
      //     strides: true,
      //   },
      // });
    }),

  upsertBoxingPunch: protectedProcedure
    .input(
      z.object({
        id: z.string().optional(),
        videoId: z.string(),
        athleteId: z.string(),
        startFrame: z.number(),
        number: z.number(),
        round: z.number(),
        punch: z.nativeEnum(BoxingPunchType),
        isSuccess: z.boolean(),
        head: z.boolean(),
        unsure: z.boolean(),
        feint: z.boolean(),
        clinch: z.boolean(),
        switch: z.boolean(),
        color: z.nativeEnum(BoxingCorner),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...rest } = input;

      // Generate a new ID if one wasn't provided
      const punchId = id ?? crypto.randomUUID();
      await ctx.db
        .insert(boxingPunches)
        .values({
          ...rest,
          id: punchId,
        })
        .onDuplicateKeyUpdate({
          set: rest,
        });

      return {
        ...input,
        id: punchId,
      };
    }),
});
