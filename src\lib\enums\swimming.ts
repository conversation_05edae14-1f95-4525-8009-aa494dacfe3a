import type { TagUI } from "../interface";

export enum SwimCourseType {
  long = "LONG_COURSE",
  short = "SHORT_COURSE",
}

export const swimmingCourseTypes = Object.values(SwimCourseType) as [
  SwimCourseType,
  ...SwimCourseType[],
];

export enum SwimmingStrokeCategory {
  FREESTYLE = "FREESTYLE",
  BACKSTROKE = "BACKSTROKE",
  BREASTSTROKE = "BREASTSTROKE",
  BUTTERFLY = "BUTTERFLY",
  INDIVIDUAL_MEDLEY = "INDIVIDUAL_MEDLEY",
}

export const swimmingStrokeCategories = Object.values(
  SwimmingStrokeCategory,
) as [SwimmingStrokeCategory, ...SwimmingStrokeCategory[]];

export enum SwimmingStartType {
  DIVE = "DIVE",
  RELAY_CHANGE_OVER = "RELAY_CHANGE_OVER",
  PUSH = "PUSH",
}

export enum SwimmingSource {
  VIDEO_ANALYSIS = "VIDEO_ANALYSIS",
  HAND_TIMING = "HAND_TIMING",
}

export enum SwimmingTag {
  "5m" = "5m",
  "15m" = "15m",
  "25m" = "25m",
  "35m" = "35m",
  "45m" = "45m",
  Gun = "Gun",
  Finish = "Finish",
  StrokeCount = "Stroke Count",
  StrokeCycle = "Stroke Cycle",
  Rotation = "Rotation",
  UW = "UW",
  Swim = "Swim",
  Breath = "Breath",
  ToeOff = "Toe Off",
  Touch = "Touch",
}

export const swimmingTags: { distance: TagUI[]; movement: TagUI[] } = {
  distance: [
    {
      value: "5m",
      key: "1",
      className: "bg-seaSalt-40 border-black/30",
      keyword: "distance",
    },
    {
      value: "15m",
      key: "2",
      className: "bg-seaSalt-40 border-black/30",
      keyword: "distance",
    },
    {
      value: "25m",
      key: "3",
      className: "bg-seaSalt-40 border-black/30",
      keyword: "distance",
    },
    {
      value: "35m",
      key: "4",
      className: "bg-seaSalt-40 border-black/30",
      keyword: "distance",
    },
    {
      value: "45m",
      key: "5",
      className: "bg-seaSalt-40 border-black/30",
      keyword: "distance",
    },
  ],
  movement: [
    {
      value: SwimmingTag.Gun,
      key: "Q",
      className: "bg-neonGreen-20 text-neonGreen-k40",
    },
    {
      value: SwimmingTag.Touch,
      key: "W",
      className: "bg-blue-20 text-blue-k40",
    },
    {
      value: SwimmingTag.StrokeCount,
      key: "E",
      className: "bg-fuchsia-20 text-fuchsia-k40",
    },
    {
      value: SwimmingTag.StrokeCycle,
      key: "R",
      className: "bg-orange-20 text-orange-k40",
    },
    undefined,
    {
      value: SwimmingTag.ToeOff,
      key: "A",
      className: "bg-purple-20 text-purple-k40",
    },
    {
      value: SwimmingTag.UW,
      key: "S",
      className: "bg-yellow-20 text-yellow-k40",
    },
    {
      value: SwimmingTag.Swim,
      key: "D",
      className: "bg-red-20 text-red-k40",
    },
    {
      value: SwimmingTag.Breath,
      key: "F",
      className: "bg-springGreen-20 text-springGreen-k40",
    },
  ],
};
