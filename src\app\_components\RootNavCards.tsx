import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { env } from "~/env";

interface NavCard {
  title: string;
  href: string;
}

const cards: NavCard[] = [
  { title: "Tagger Tool", href: "/tagger" },
  { title: "Validation Tool", href: "/validation" },
  {
    title: "Video Portal",
    href: env.NEXT_PUBLIC_VIDEO_PORTAL_URL,
  },
];

export function RootNavCards() {
  return (
    <div className="grid w-full grid-cols-12 gap-2.5">
      {cards.map((card) => (
        <div key={card.href} className="col-span-4">
          <Link
            href={card.href}
            target={card.href === env.NEXT_PUBLIC_VIDEO_PORTAL_URL ? "_blank" : undefined}
            className="group flex items-center justify-between rounded-lg border bg-white px-5 py-10 pt-5 transition-all hover:cursor-pointer hover:border-black focus:bg-black/10 disabled:bg-black/10 disabled:text-black/30"
          >
            <div className="flex items-center gap-2.5">
              <div className="h-3 w-3 rounded-full bg-black" />
              <p className="text-3xl leading-9 tracking-wide">{card.title}</p>
            </div>
            <ArrowRight
              className="aspect-square h-9 w-9 transition-transform group-hover:scale-110"
              strokeWidth={1}
            />
          </Link>
        </div>
      ))}
    </div>
  );
}
