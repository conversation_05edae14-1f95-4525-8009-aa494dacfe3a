import { usePathname } from "next/navigation";
import { Sport, VideoStatus } from "~/lib/enums/enums";
import { api } from "~/trpc/react";

export const useVideoTotalCount = () => {
  const pathname = usePathname();
  const isValidation = pathname.startsWith("/validation");

  const { data, isLoading } = api.videos.getVideos.useQuery({
    status: isValidation ? VideoStatus.Tagged_by_AI : undefined,
    sport: [Sport.swimming, Sport.snowsports, Sport.athletics],
  });
  const count = data?.count ?? 0;

  return { isLoading, count };
};
