"use client";

import { useParams } from "next/navigation";
import { useState } from "react";
import toast from "react-hot-toast";
import { useHotkeys } from "react-hotkeys-hook";
import { Card } from "~/components/Card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { useStore } from "~/hooks/store";
import { api } from "~/trpc/react";
import { HotkeyButton } from "../HotkeyButton";
import type { GetSprintingStridesOutput } from "~/server/api/routers/sprinting";

export const TaggerSprintStride = ({
  selectedSprint,
}: {
  selectedSprint: GetSprintingStridesOutput;
}) => {
  const sprintId = selectedSprint?.id;
  const params = useParams<{ id: string }>();
  const videoId = params.id;
  const utils = api.useUtils();

  const currentFrame = useStore((state) => state.currentFrame);

  const [newStride, setNewStride] = useState({
    heelContact: "",
    toeOff: "",
  });

  const { mutateAsync: upsertStride } = api.sprint.upsertStride.useMutation({
    onSuccess: () => {
      void utils.sprint.getRace.invalidate({ videoId });
    },
  });

  const onSetToeOff = () => {
    setNewStride({
      ...newStride,
      toeOff: currentFrame.toString(),
    });
    if (newStride.heelContact) {
      void toast.promise(
        upsertStride({
          sprintId: sprintId ?? "",
          number: (selectedSprint?.strides.length ?? 0) + 1,
          heelContact: +newStride.heelContact,
          toeOff: currentFrame,
        }),
        {
          loading: "Adding stride...",
          success: "Stride added",
          error: "Failed to add stride",
        },
      );
    }
  };

  useHotkeys("z", () =>
    setNewStride({
      ...newStride,
      heelContact: currentFrame.toString(),
    }),
  );
  useHotkeys("x", onSetToeOff);

  return (
    <Card className="gap-2.5">
      <div className="flex items-center justify-between gap-2.5">
        <Label className="text-smallLabel">STRIDES:</Label>
      </div>
      <div className="grid grid-cols-2 gap-2.5">
        <div className="flex flex-col gap-1">
          <Label className="!text-smallLabel text-black/60">Heel Contact</Label>
          <div className="flex w-full items-center gap-[5px]">
            <Input
              type="number"
              containerClassName="flex-1"
              className="flex-1"
              value={newStride.heelContact}
              onChange={(e) =>
                setNewStride({ ...newStride, heelContact: e.target.value })
              }
            />

            <HotkeyButton
              tag={{
                key: "z",
                value: "",
                className: "h-5 w-5 bg-neonGreen text-label mx-0",
              }}
              onClick={() =>
                setNewStride({
                  ...newStride,
                  heelContact: currentFrame.toString(),
                })
              }
            />
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <Label className="!text-smallLabel text-black/60">Toe Off</Label>
          <div className="flex w-full items-center gap-[5px]">
            <Input
              type="number"
              containerClassName="flex-1"
              className="flex-1"
              value={newStride.toeOff}
              onChange={(e) =>
                setNewStride({ ...newStride, toeOff: e.target.value })
              }
            />

            <HotkeyButton
              tag={{
                key: "x",
                value: "",
                className: "h-5 w-5 bg-blue text-label text-white mx-0",
              }}
              onClick={onSetToeOff}
            />
          </div>
        </div>
      </div>
    </Card>
  );
};
