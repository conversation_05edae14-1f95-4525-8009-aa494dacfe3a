import { useState } from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { AthleteSelect } from "~/app/validation/components/AthleteSelect";
import { Card } from "~/components/Card";
import { HotkeyButton } from "~/components/HotkeyButton";
import { Input } from "~/components/ui/input";
import { useStore } from "~/hooks/store";
import { useShotputTags } from "~/hooks/useShotputTags";
import { ShotputHand, ShotputTag, shotputTags } from "~/lib/enums/shotput";
import { cn, getAthleteOptions, getTagDisplay } from "~/lib/utils";
import { AddThrowButton } from "./AddThrowButton";
import { ShotputThrowForm } from "./ShotputThrowForm";

const tagOptions = shotputTags["key phase"];

export const TaggerShotputForm = () => {
  const filteredTags = useStore((state) => state.filteredTags);
  const currentFrame = useStore((state) => state.currentFrame);
  const videoSummary = useStore((state) => state.videoSummary);
  const shotputThrow = useStore((state) => state.shotputThrow);

  const { tags, onAddTag } = useShotputTags();
  const athleteOptions = getAthleteOptions(videoSummary, tags?.throws);

  const [editingValues, setEditingValues] = useState<Record<string, string>>(
    {},
  );

  const handleInputChange = (tagValue: string, value: string) => {
    setEditingValues((prev) => ({
      ...prev,
      [tagValue]: value,
    }));
  };

  useHotkeys("1", () => onAddTag(ShotputTag.RotationStart));
  useHotkeys("2", () => onAddTag(ShotputTag.RightToeOff));
  useHotkeys("3", () => onAddTag(ShotputTag.LeftToeOffBack));
  useHotkeys("4", () => onAddTag(ShotputTag.RightFootDown));
  useHotkeys("5", () => onAddTag(ShotputTag.DoubleStance));
  useHotkeys("6", () => onAddTag(ShotputTag.RearFootTakeOff));
  useHotkeys("7", () => onAddTag(ShotputTag.FrontFootTakeOff));
  useHotkeys("8", () => onAddTag(ShotputTag.Release));
  useHotkeys("9", () => onAddTag(ShotputTag.PostRelease));

  return (
    <>
      <AthleteSelect athleteOptions={athleteOptions} />
      <div className="flex flex-1 flex-col gap-2.5">
        <ShotputThrowForm />

        <Card className="gap-2.5 text-smallLabel">
          <p className="uppercase text-black/60">Key phases</p>
          <div className="flex flex-col gap-2.5">
            {tagOptions.map((tagOption) => {
              if (!tagOption?.value) return null;

              // IMPORTANT: Make sure we're checking for the tag with the current throw number
              const existingTag = filteredTags.find(
                (tag) => tag.tag === tagOption?.value?.toString(),
              );

              const isSelected = existingTag?.frame === currentFrame;

              const displayValue =
                editingValues[tagOption.value] ?? existingTag?.frame ?? "";
              const tagDisplay = getTagDisplay({
                label: tagOption.value,
                leftHand: shotputThrow?.hand === ShotputHand.LeftHand,
                sport: "shotput",
              });

              return (
                <div
                  key={tagOption.value}
                  className="flex w-full items-start justify-between"
                  id={existingTag?.id ?? `tag-${tagOption.value}`}
                >
                  <p className="text-smallLabel text-gray">{tagDisplay}</p>
                  <div className="flex items-start gap-[5px]">
                    <Input
                      type="number"
                      className={cn(
                        "h-5",
                        isSelected && "ring-2 ring-gray ring-offset-2",
                      )}
                      value={displayValue}
                      onChange={(e) =>
                        handleInputChange(tagOption.value, e.target.value)
                      }
                      onBlur={(e) => {
                        const value = e.target.value;
                        if (+value === existingTag?.frame) return;
                        onAddTag(tagOption.value, +value);
                      }}
                      onFinish={(value) => {
                        if (+value === existingTag?.frame) return;
                        onAddTag(tagOption.value, +value);
                      }}
                      placeholder={existingTag ? "" : "Add..."}
                      title={
                        existingTag
                          ? `Edit frame for ${tagOption.value}`
                          : `Add tag ${tagOption.value} at specified frame`
                      }
                    />
                    <HotkeyButton
                      tag={tagOption}
                      onClick={() => onAddTag(tagOption.value)}
                      // disabled={isPending}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </Card>
        <AddThrowButton />
      </div>
    </>
  );
};
