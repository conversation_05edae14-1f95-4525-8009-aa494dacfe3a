import { createId } from "@paralleldrive/cuid2";
import { eq } from "drizzle-orm";
import { env } from "~/env";
import type { DecodedToken } from "~/lib/interface";
import { db } from "~/server/db";
import { accounts, users } from "~/server/db/schema";
import jwt from "jsonwebtoken";

export const getServiceToken = async () => {
  let userId = "";
  const user = await db.query.users.findFirst({
    where: eq(users.name, "service-account-cub-ptaplatform-api"),
  });
  if (user) userId = user.id;
  if (!user) {
    userId = createId();
    await db.insert(users).values({
      id: userId,
      email: "",
      name: "service-account-cub-ptaplatform-api",
    });
  }

  //get stored service token
  const account = await db.query.accounts.findFirst({
    where: eq(accounts.userId, userId),
  });

  if (
    account?.access_token &&
    account.expires_at &&
    account.expires_at * 1000 > Date.now() + 60000
  ) {
    return {
      access_token: account.access_token,
      expires_in: Math.floor((account.expires_at * 1000 - Date.now()) / 1000),
      scope: account.scope,
    };
  }

  //request new service token
  const tokenUrl = `${env.NEXT_PUBLIC_KEYCLOAK_ROOT}/realms/hpsnz/protocol/openid-connect/token`;
  const tokenRes = await fetch(tokenUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      grant_type: "client_credentials",
      client_id: "cub-taggerplatform-api",
      client_secret: env.KEYCLOAK_TAGGER_API_CLIENT_SECRET,
      scope: "api-modify api-delete",
    }),
  });
  const tokenJson = (await tokenRes.json()) as {
    access_token: string;
    expires_in: number;
    scope: string;
  };

  const decodedToken = jwt.decode(tokenJson.access_token, { complete: true })!;

  //save new service token in DB
  const expires_at = Math.round(Date.now() / 1000 + tokenJson.expires_in);
  await db
    .insert(accounts)
    .values({
      userId,
      type: "oauth",
      provider: "keycloak",
      providerAccountId: decodedToken.payload.sub as string,
      access_token: tokenJson.access_token,
      expires_at,
      scope: tokenJson.scope,
      token_type: "Bearer",
    })
    .onDuplicateKeyUpdate({
      set: {
        userId,
        access_token: tokenJson.access_token,
        expires_at,
        scope: tokenJson.scope,
        token_type: "Bearer",
      },
    });

  return tokenJson;
};

export const getUserIdByToken = async (token: DecodedToken) => {
  const { sub, name, email, preferred_username } = token;

  const existingAccount = await db.query.accounts.findFirst({
    where: (account, { eq }) => eq(account.providerAccountId, token.sub),
  });

  if (existingAccount) return existingAccount.userId;

  return await db.transaction(async (tx) => {
    const newUserId = createId();
    await tx.insert(users).values({
      name: name ?? preferred_username ?? "",
      email: email ?? "",
    });
    await tx.insert(accounts).values({
      userId: newUserId,
      type: "oauth",
      provider: "keycloak",
      providerAccountId: sub,
    });
    return newUserId;
  });
};
