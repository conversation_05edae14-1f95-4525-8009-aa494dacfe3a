"use client";

import { CircleHelp } from "lucide-react";
import { useHotkeys } from "react-hotkeys-hook";
import { useHelperStore } from "~/hooks/store";

export const HelperActivator = () => {
  const active = useHelperStore((state) => state.helperActive);
  const setActive = useHelperStore((state) => state.setHelperActive);

  useHotkeys("`", () => setActive(!active));

  return (
    <div>
      <button className="p-2" onClick={() => setActive(true)}>
        <CircleHelp className="h-6 w-6 text-gray" />
      </button>
      {active && (
        <div
          className="fixed left-0 top-0 z-50 h-screen w-screen bg-white/30 backdrop-blur-[1px]"
          onClick={() => setActive(false)}
        ></div>
      )}
    </div>
  );
};
