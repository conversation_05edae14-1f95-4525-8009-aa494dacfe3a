import { PopoverClose } from "@radix-ui/react-popover";
import type { SelectProps as ShadcnSelectProps } from "@radix-ui/react-select";
import { Check, ChevronDown, PlusIcon, Trash } from "lucide-react";
import { useState } from "react";
import { Loading } from "~/app/_components/Loading";
import { Label } from "~/components/ui/label";
import {
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  Select as ShadcnSelect,
} from "~/components/ui/select";
import type { Option } from "~/lib/interface";
import { cn } from "~/lib/utils";
import { Button } from "./ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "./ui/command";
import {
  Popover,
  PopoverContent,
  PopoverContentInDialog,
  PopoverTrigger,
} from "./ui/popover";

interface SelectProps extends ShadcnSelectProps {
  options?: Option[];
  placeholder?: string;
  className?: string;
  containerClassName?: string;
  label?: string;
}

export const Selector = ({
  options,
  placeholder,
  className,
  containerClassName,
  label,
  ...props
}: SelectProps) => (
  <div
    className={cn(
      "flex items-center justify-between gap-3",
      containerClassName,
    )}
  >
    {label && (
      <Label htmlFor={label} className="text-smallLabel">
        {label}
      </Label>
    )}
    <ShadcnSelect {...props}>
      <SelectTrigger
        className={cn("rounded-full py-[2px]", className)}
        id={label}
      >
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options?.map(({ value, label, children }) => {
          if (!children) {
            return (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            );
          }
          return (
            <SelectGroup key={value}>
              <SelectLabel>{label}</SelectLabel>
              {children.map(({ value, label }) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectGroup>
          );
        })}
      </SelectContent>
    </ShadcnSelect>
  </div>
);

export const EditableSelector = ({
  options,
  label,
  className,
  placeholder,
  value,
  isPendingAdd,
  type = "text",
  easyAdd,
  onAdd,
  onRemove,
  onSelect,
}: {
  value?: string | null;
  options: Option[];
  label?: string;
  className?: string;
  placeholder?: string;
  isPendingAdd?: boolean;
  type?: "number" | "text";
  easyAdd?: boolean;
  onAdd?: (value: string) => void;
  onRemove?: (value: string) => void;
  onSelect: (value: string) => void;
}) => {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");

  const selectedLabel = options.find((x) => value === x.value)?.label;

  const hasOptionsMatchSearch = options.some(
    (option) => option.label.toLowerCase() === search,
  );
  const canCreateInDropdown =
    search.length > 0 && !hasOptionsMatchSearch && onAdd && !easyAdd;

  return (
    <div
      className={cn(
        "flex gap-[5px]",
        label ? "flex-col items-start" : "items-center justify-between",
      )}
    >
      {label && (
        <Label htmlFor={label} className="!text-smallLabel">
          {label}
        </Label>
      )}
      <div className="flex w-full items-center justify-between">
        <Popover open={open} onOpenChange={setOpen} modal={true}>
          <PopoverTrigger asChild>
            <Button
              variant="search"
              role="combobox"
              size="xs"
              aria-expanded={open}
              className={cn("h-5 flex-1", className)}
            >
              <p
                className={cn(
                  "mr-auto capitalize",
                  !selectedLabel && "text-black/60",
                )}
              >
                {selectedLabel ?? placeholder}
              </p>
              <ChevronDown className="h-[14px] w-[14px] opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="rounded-lg p-0 text-smallLabel">
            <Command
              filter={(_, search, keywords) => {
                if (
                  keywords?.some((x) =>
                    x.toLowerCase().includes(search.toLowerCase()),
                  )
                )
                  return 1;
                return 0;
              }}
            >
              <CommandInput
                placeholder={"Search ..."}
                value={search}
                onValueChange={(value) => {
                  if (type === "number" && isNaN(Number(value))) return;
                  setSearch(value);
                }}
              />
              {canCreateInDropdown && (
                <button
                  className="w-full border-y py-2.5 hover:bg-gray-100 disabled:text-gray"
                  disabled={isPendingAdd}
                  onClick={() => {
                    onAdd(search);
                  }}
                >
                  Create {search}
                </button>
              )}
              <CommandList>
                <CommandEmpty>
                  {!canCreateInDropdown && "No item found."}
                </CommandEmpty>
                <CommandGroup>
                  {options.map((option) => (
                    <CommandItem
                      key={option.value}
                      className="group flex-1 text-smallLabel capitalize"
                      keywords={[option.label]}
                      value={option.value}
                      onSelect={(currentValue) => {
                        onSelect(currentValue);
                        setOpen(false);
                      }}
                      icon={
                        <>
                          <Check
                            className={cn(
                              "ml-auto h-4 w-4 group-hover:hidden",
                              value === option.value
                                ? "opacity-100"
                                : "opacity-0",
                            )}
                          />
                          {onRemove && (
                            <Trash
                              className="hidden h-4 w-4 cursor-pointer hover:text-red group-hover:flex"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                onRemove(option.value);
                              }}
                            />
                          )}
                        </>
                      }
                    >
                      <p className="flex-1">{option.label}</p>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        {easyAdd && onAdd && (
          <Button
            size="icon"
            variant="search"
            className="ml-[5px]"
            disabled={isPendingAdd}
            onClick={() => onAdd((options.length + 1).toString())}
          >
            <PlusIcon className="h-3 w-3 text-seaSalt-k40" />
          </Button>
        )}
      </div>
    </div>
  );
};

export const SearchSelector = ({
  value,
  label,
  searchText,
  setSearchText,
  className,
  containerClassName,
  options,
  onSelect,
  disabled,
  isLoading,
  popoverClassName,
}: {
  value?: string | null;
  label?: string;
  searchText: string;
  className?: string;
  containerClassName?: string;
  popoverClassName?: string;
  options: Option[];
  disabled?: boolean;
  isLoading: boolean;
  onSelect: (value: string) => void;
  setSearchText: (text: string) => void;
}) => {
  const [open, setOpen] = useState(false);
  const selectedLabel = options.find((x) => value === x.value)?.label;

  return (
    <div
      className={cn("flex items-center justify-between", containerClassName)}
    >
      {label && <Label htmlFor={label}>{label}</Label>}
      <Popover open={open} onOpenChange={setOpen} modal>
        <PopoverTrigger asChild autoFocus={open}>
          <Button
            variant="search"
            role="combobox"
            size="xs"
            disabled={disabled}
            aria-expanded={open}
            className={cn("ml-auto", className)}
          >
            <p className="mr-auto">{selectedLabel ?? "Select..."}</p>
            <ChevronDown className="h-[14px] w-[14px] opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContentInDialog
          className={cn("w-96 p-0", popoverClassName)}
          sideOffset={5}
          align="start"
        >
          <Command
            filter={(_, search, keywords) => {
              if (
                keywords?.some((x) =>
                  x.toLowerCase().includes(search.toLowerCase()),
                )
              )
                return 1;
              return 0;
            }}
          >
            <CommandInput
              placeholder="Search ..."
              value={searchText}
              onValueChange={setSearchText}
            />
            <CommandList className="relative">
              {isLoading && (
                <div className="flex h-12 w-full items-center justify-center">
                  <Loading />
                </div>
              )}
              {options?.map((option) => {
                return (
                  <div
                    key={option.value}
                    className="flex w-full items-center justify-center"
                  >
                    <PopoverClose asChild>
                      <Button
                        variant="ghost"
                        key={option.value}
                        className="h-auto w-full items-center justify-center py-1 text-label"
                        onClick={() => onSelect(option.value)}
                      >
                        <div className="w-full text-left">{option.label}</div>
                      </Button>
                    </PopoverClose>
                  </div>
                );
              })}
              {searchText.length < 3 && (
                <CommandEmpty className="w-full py-2.5 text-center text-smallLabel text-gray-500">
                  Search for items by typing at least 3 characters.
                </CommandEmpty>
              )}
              {!options && !isLoading && searchText.length >= 3 && (
                <CommandEmpty>No athlete found.</CommandEmpty>
              )}
            </CommandList>
          </Command>
        </PopoverContentInDialog>
      </Popover>
    </div>
  );
};
