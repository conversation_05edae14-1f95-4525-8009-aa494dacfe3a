"use client";

import { useState, useEffect } from "react";
import { Card } from "~/components/Card";
import { Label } from "~/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { useStore } from "~/hooks/store";
import {
  SwimCourseType,
  swimmingStrokeCategories,
  SwimmingStrokeCategory,
} from "~/lib/enums/swimming";
import type { SwimmingLap } from "~/lib/interfaces/swimming";
import { api } from "~/trpc/react";
import { Selector } from "~/components/Selector";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { toast } from "react-hot-toast";

export const EditSwimmingLaps = () => {
  const utils = api.useUtils();

  const [lapsData, setLapsData] = useState<SwimmingLap[]>([]);

  const selectedSwimmingSession = useStore(
    (state) => state.selectedSwimmingSession,
  );
  const selectedSwimmingRep = useStore((state) => state.selectedSwimmingRep);

  const { data: laps, refetch } = api.swimmingPta.getLaps.useQuery(
    {
      repId: selectedSwimmingRep?.id ?? "",
    },
    {
      enabled: !!selectedSwimmingRep?.id,
    },
  );

  const lapDistance =
    selectedSwimmingSession?.course_type === SwimCourseType.short ? 25 : 50;
  const repDistance = selectedSwimmingRep?.distance ?? 0;
  const lapCount = Math.ceil(repDistance / lapDistance);

  useEffect(() => {
    if (!selectedSwimmingRep) {
      setLapsData([]);
      return;
    }

    setLapsData(
      Array.from({ length: lapCount ?? 0 }, (_, index) => {
        const lap = laps?.find((lap) => lap.lap_number === index + 1);

        let defaultLapStrokeType = selectedSwimmingRep.stroke_category;
        if (defaultLapStrokeType === SwimmingStrokeCategory.INDIVIDUAL_MEDLEY) {
          if (lapCount <= 4) {
            const strokeTypes = [
              SwimmingStrokeCategory.BUTTERFLY,
              SwimmingStrokeCategory.BACKSTROKE,
              SwimmingStrokeCategory.BREASTSTROKE,
              SwimmingStrokeCategory.FREESTYLE,
            ];
            defaultLapStrokeType = strokeTypes[index % strokeTypes.length]!;
          } else {
            const strokeTypes = [
              SwimmingStrokeCategory.BUTTERFLY,
              SwimmingStrokeCategory.BUTTERFLY,
              SwimmingStrokeCategory.BACKSTROKE,
              SwimmingStrokeCategory.BACKSTROKE,
              SwimmingStrokeCategory.BREASTSTROKE,
              SwimmingStrokeCategory.BREASTSTROKE,
              SwimmingStrokeCategory.FREESTYLE,
              SwimmingStrokeCategory.FREESTYLE,
            ];
            defaultLapStrokeType = strokeTypes[index % strokeTypes.length]!;
          }
        }
        return {
          lap_number: index + 1,
          stroke_type: lap?.stroke_type ?? defaultLapStrokeType,
          lap_distance: lapDistance,
          distance: (index + 1) * lapDistance,
          lap_duration: lap?.lap_duration ?? 0,
          duration: lap?.duration ?? 0,
          speed: lap?.speed ?? 0,
        };
      }),
    );
  }, [laps, lapCount, lapDistance, repDistance, selectedSwimmingRep]);

  const { mutate: upsertLaps, isPending } =
    api.swimmingPta.upsertLaps.useMutation({
      onSuccess: async () => {
        toast.success("Laps updated");
        void utils.swimmingPta.getReps.invalidate({
          sessionId: selectedSwimmingSession?.session_id,
        });
        await refetch();
      },
      onError: () => {
        toast.error("Failed to update laps");
      },
    });

  const onStrokeTypeChange = (
    lapIndex: number,
    strokeType: SwimmingStrokeCategory,
  ) => {
    const updatedLaps = [...lapsData];
    updatedLaps[lapIndex]!.stroke_type = strokeType;
    setLapsData(updatedLaps);
  };

  const onDurationChange = (lapIndex: number, duration: number) => {
    const newLaps = lapsData.reduce((acc, x, index) => {
      const prevLapDuration = index === 0 ? 0 : (acc[index - 1]?.duration ?? 0);
      if (index === lapIndex) {
        return [
          ...acc,
          {
            ...x,
            lap_duration: duration,
            speed: x.lap_distance / duration,
            duration: prevLapDuration + duration,
          },
        ];
      }
      return [...acc, { ...x, duration: prevLapDuration + x.lap_duration }];
    }, [] as SwimmingLap[]);

    setLapsData(newLaps);
  };

  const onSave = () => {
    if (!selectedSwimmingRep?.id) return;

    const hasZeroDuration = lapsData.some((x) => x.lap_duration === 0);
    if (hasZeroDuration) {
      toast.error("Lap duration cannot be 0");
      return;
    }

    const isNewLaps = laps && laps.length === 0;
    upsertLaps({
      rep: selectedSwimmingRep,
      laps: lapsData,
      method: isNewLaps ? "POST" : "PUT",
    });
  };

  const strokeOptions = swimmingStrokeCategories
    .filter((x) => x !== SwimmingStrokeCategory.INDIVIDUAL_MEDLEY)
    .map((x) => ({
      label: x,
      value: x,
    }));

  const isNewLaps = laps && laps.length === 0;

  return (
    <Card>
      <div className="flex justify-between">
        <Label>Laps: ({lapCount})</Label>
        <Button
          size="sm"
          disabled={!selectedSwimmingRep || isPending}
          onClick={onSave}
          loading={isPending}
        >
          {isNewLaps ? "Create" : "Save"}
        </Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-10">Number</TableHead>
            <TableHead className="w-28">Type</TableHead>
            <TableHead className="w-20">Lap distance</TableHead>
            <TableHead className="w-10">Distance</TableHead>
            <TableHead className="w-20">Lap Duration</TableHead>
            <TableHead className="w-10">Duration</TableHead>
            <TableHead className="w-10">Speed</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {lapsData.map((lap, index) => {
            return (
              <TableRow key={index}>
                <TableCell>{lap.lap_number}</TableCell>
                <TableCell>
                  <Selector
                    options={strokeOptions}
                    value={lap.stroke_type}
                    onValueChange={(value) => {
                      onStrokeTypeChange(
                        index,
                        value as SwimmingStrokeCategory,
                      );
                    }}
                  />
                </TableCell>
                <TableCell>{lap.lap_distance}</TableCell>
                <TableCell>{lap.distance}</TableCell>
                <TableCell>
                  <Input
                    type="number"
                    value={lap.lap_duration}
                    onChange={(e) => {
                      onDurationChange(index, +e.target.value);
                    }}
                  />
                </TableCell>
                <TableCell>{lap.duration}</TableCell>
                <TableCell>{lap.speed.toFixed(2)}</TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </Card>
  );
};
