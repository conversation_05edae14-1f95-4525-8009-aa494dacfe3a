"use client";

import { api } from "~/trpc/react";
import toast from "react-hot-toast";
import { EditableSelector } from "~/components/Selector";

export const SpinTypeSelect = ({
  value,
  onSelect,
}: {
  value: string | null;
  onSelect: (value: string) => void;
}) => {
  const utils = api.useUtils();
  const { data: options, refetch } = api.snowOptions.getSpinTypes.useQuery();

  const { mutate: add, isPending: isPendingAdd } =
    api.snowOptions.addSpinType.useMutation({
      onSuccess: () => {
        void refetch();
      },
    });

  const { mutate: remove } = api.snowOptions.deleteSpinType.useMutation({
    onError: () => {
      toast.error("Failed to remove jump type");
      void refetch();
    },
  });

  const onAddOption = (value: string) => {
    add({ name: value });
  };

  const onRemoveOption = (value: string) => {
    remove({ id: parseInt(value) });
    utils.snowOptions.getSpinTypes.setData(undefined, (prev) => {
      return prev?.filter((x) => x.value !== value);
    });
  };

  return (
    <EditableSelector
      placeholder="<Optional>"
      options={options ?? []}
      onAdd={onAddOption}
      onRemove={onRemoveOption}
      value={value}
      onSelect={onSelect}
      isPendingAdd={isPendingAdd}
    />
  );
};
