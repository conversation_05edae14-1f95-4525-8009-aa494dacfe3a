"use client";

import { SearchSelector } from "~/components/Selector";
import { api } from "~/trpc/react";
import { useState, useEffect } from "react";
import { useStore } from "~/hooks/store";
import { useDebounce } from "~/hooks/useDebounce";

export const SwimmingSessionSelector = ({
  disabled,
}: {
  disabled?: boolean;
}) => {
  const [searchText, setSearchText] = useState("");
  const debouncedSearchText = useDebounce(searchText, 500);

  const selectedSwimmingRace = useStore((state) => state.selectedSwimmingRace);
  const selectedSwimmingSession = useStore(
    (state) => state.selectedSwimmingSession,
  );
  const setSelectedSwimmingSession = useStore(
    (state) => state.setSelectedSwimmingSession,
  );

  const searchActive = debouncedSearchText.length >= 3;

  const { data: sessions, isLoading: isLoadingSessions } =
    api.swimmingPta.getSessions.useQuery(
      {
        raceId: searchActive
          ? undefined
          : (selectedSwimmingRace?.race_id ?? undefined),
        sessionDescription: searchActive ? debouncedSearchText : undefined,
      },
      {
        enabled: !!selectedSwimmingRace || searchActive,
      },
    );

  useEffect(() => {
    if (sessions && sessions.length > 0 && !selectedSwimmingSession) {
      setSelectedSwimmingSession(sessions[0] ?? null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessions]);

  const options =
    sessions?.map((session) => {
      let athleteName = session.athlete
        ? session.athlete?.first_name + " " + session.athlete?.last_name
        : undefined;
      if (!athleteName && session.relay_team) {
        athleteName = session.relay_team.name;
      }
      return {
        label: `${session.session_description} - ${athleteName}`,
        value: session.session_id ?? "",
      };
    }) ?? [];

  if (!options.find((x) => x.value === selectedSwimmingSession?.session_id)) {
    options.push({
      label: selectedSwimmingSession?.session_description ?? "",
      value: selectedSwimmingSession?.session_id ?? "",
    });
  }

  return (
    <SearchSelector
      label="Session"
      containerClassName="grid gap-2.5"
      className="w-[600px]"
      popoverClassName="w-fit"
      disabled={disabled}
      value={selectedSwimmingSession?.session_id ?? ""}
      searchText={searchText}
      setSearchText={setSearchText}
      options={options}
      isLoading={isLoadingSessions}
      onSelect={(value) =>
        setSelectedSwimmingSession(
          sessions?.find((session) => session.session_id === value) ?? null,
        )
      }
    />
  );
};
