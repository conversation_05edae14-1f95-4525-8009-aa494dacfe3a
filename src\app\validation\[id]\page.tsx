import { TRPCError } from "@trpc/server";
import { ErrorBox } from "~/app/_components/ErrorBox";
import { api, HydrateClient } from "~/trpc/server";
import { ValidationTool } from "../components/ValidationTool";

export default async function ValidationVideoHome({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const id = (await params).id;

  if (!id) {
    return <div>Invalid video ID</div>;
  }

  try {
    void api.videos.getVideoSourceStream.prefetch({ id });
    const videoSummary = await api.videos.getVideoInfo({ id });
    const poseData = await api.pose.getAllPoseDataByVideo({ videoId: id });

    return (
      <HydrateClient>
        <main className="h-full">
          <ValidationTool videoSummary={videoSummary} poseData={poseData} />
        </main>
      </HydrateClient>
    );
  } catch (error) {
    console.error(error);
    if (error instanceof TRPCError) {
      return <ErrorBox code={error.code} message={error.message} />;
    } else {
      return <div>An unexpected error occurred</div>;
    }
  }
}
