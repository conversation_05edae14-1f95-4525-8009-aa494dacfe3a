"use client";

import type { UseFormReturn } from "react-hook-form";
import { Card } from "~/components/Card";
import { Label } from "~/components/ui/label";
import { useStore } from "~/hooks/store";
import { spinAmounts } from "~/lib/constants";
import { FormItemType } from "~/lib/enums/enums";
import {
  LandingDescription,
  landingDescriptions,
  SnowFeatureType,
  type SnowJumpSpinDirection,
  snowJumpSpinDirections,
  SnowLandingType,
  snowLandingTypes,
  type SnowLandingZone,
  snowLandingZones,
  SnowRailSpinDirection,
  snowRailSpinDirections,
} from "~/lib/enums/snow";
import type {
  NewSnowSlopeRailTag,
  NewSnowSlopeTransitionJumpTag,
  NewSnowSlopeTransitionTransitionTag,
} from "~/server/db/snowSchema";
import { api } from "~/trpc/react";
import { FormItem, type FormItemProps } from "../../FormItem";
import { ExecutionSelect } from "../enumSelectors/ExecutionSelect";
import { GrabSelect } from "../enumSelectors/GrabSelect";
import { JumpTypeSelect } from "../enumSelectors/JumpTypeSelect";
import { landingDescriptionFormItem } from "../enumSelectors/LandingDescription";
import { ModifierSelect } from "../enumSelectors/ModifierSelect";
import { RailFeatureSelect } from "../enumSelectors/RailFeatureSelect";
import { RailTrickSelect } from "../enumSelectors/RailTrickSelect";

import { SpinTypeSelect } from "../enumSelectors/SpinTypeSelect";
import type { SnowFormProps } from "../SnowFormContainer";
import { TransitionTypeSelect } from "../enumSelectors/TransitionTypeSelect";

interface Props {
  isSnowboard: boolean;
  section: SnowFeatureType | null;
  form: UseFormReturn<SnowFormProps>;
  jumpTag: NewSnowSlopeTransitionJumpTag;
  railTag: NewSnowSlopeRailTag;
  transitionTag: NewSnowSlopeTransitionTransitionTag;
}

interface FormItemSlopeJumpProps extends FormItemProps {
  name: keyof SnowFormProps;
}

export const FormSlopeTransition = ({
  form,
  isSnowboard,
  section,
  jumpTag,
  railTag,
  transitionTag,
}: Props) => {
  const isJump = isSnowboard;
  const { data: modifierOptions } = api.snowOptions.getModifiers.useQuery();

  const { data: grabOptions } = api.snowOptions.getGrabs.useQuery({
    isSnowboard,
  });
  const { data: railModifierOptions } = api.snowOptions.getModifiers.useQuery();
  const { data: railTrickOptions } = api.snowOptions.getRailTricks.useQuery({
    isSnowboard,
  });
  const { data: executionOptions } = api.snowOptions.getExecutions.useQuery({
    isJump: isJump,
  });
  const { data: spinTypeOptions } = api.snowOptions.getSpinTypes.useQuery();

  const { data: jumpTypeOptions } = api.snowOptions.getJumpTypes.useQuery({
    isSnowboard,
  });
  const currentFrame = useStore((state) => state.currentFrame);
  const { setValue, getValues } = form;
  const landingType = form.watch("landingType");

  const { data: railFeatures } = api.snowOptions.getRailFeatures.useQuery();
  const { data: transitionFeatures } =
    api.snowOptions.getTransitionTypes.useQuery();

  const getSectionLabel = (section: string | null) => {
    switch (section) {
      case SnowFeatureType.jump:
        return "JUMP DETAILS";
      case SnowFeatureType.rail:
        return "RAIL DETAILS";
      case SnowFeatureType.transition:
        return "TRANSITION DETAILS";
      default:
        return "RUN DETAILS";
    }
  };

  const jumpFields: FormItemSlopeJumpProps[] = [
    {
      name: "jumpTypeId",
      title: "Jump Type",
      type: FormItemType.editSelect,
      className: "flex flex-col col-span-2 items-start gap-[5px] w-full",
      hotkey: {
        value: "q",
        className: "bg-neonGreen-20 text-neonGreen-k40 text-xs",
        onClick: () => {
          const options = jumpTypeOptions;
          if (!options?.length) return;

          const currentValue = getValues("jumpTypeId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("jumpTypeId", options[nextIndex]?.value ?? null);

          if (!jumpTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <JumpTypeSelect
          isSnowboard={isSnowboard}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
    {
      name: "jumpTakeoffModifierId",
      title: "Spin Modifier(take off)",
      type: FormItemType.editSelect,
      className: "grid col-span-full",
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <ModifierSelect value={field.value} onSelect={field.onChange} />,
      hotkey: {
        value: "w",
        className: "bg-neonGreen-20 text-neonGreen-k40 text-xs",
        onClick: () => {
          const options = modifierOptions;
          if (!options?.length) return;

          const currentValue = getValues("jumpTakeoffModifierId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("jumpTakeoffModifierId", options[nextIndex]?.value ?? null);
        },
      },
    },
    {
      name: "progression",
      title: "Progression",
      type: FormItemType.radio,
      className: "flex items-center col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "e",
        className:
          "text-xs group-data-[state=checked]:bg-fuchsia-20 text-black data-[state=checked]:text-fuchsia-k40",
        onClick: () => {
          const currentValue = form.getValues("progression");
          setValue("progression", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Yes", value: "1" },
        { label: "No", value: "0" },
      ],
    },
    {
      name: "switch",
      title: "Orientation",
      type: FormItemType.radio,
      className: "flex items-center col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "r",
        className:
          "group-data-[state=checked]:bg-orange-20 text-black data-[state=checked]:text-orange-k40",
        onClick: () => {
          const currentValue = form.getValues("switch");
          setValue("switch", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Switch", value: "1" },
        { label: "Forwards", value: "0" },
      ],
    },
    {
      name: "cab",
      title: "Cab",
      type: FormItemType.radio,
      className: "flex items-center col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "t",
        className:
          "group-data-[state=checked]:bg-orange-k40 text-black data-[state=checked]:text-orange-20",
        onClick: () => {
          const currentValue = form.getValues("cab");
          setValue("cab", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Yes", value: "1" },
        { label: "No", value: "0" },
      ],
    },
    {
      name: "spinDirection",
      title: "Spin Direction",
      type: FormItemType.select,
      placeholder: "Select Spin Direction",
      className: "grid",
      defaultValue: "none",
      options: snowJumpSpinDirections.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "a",
        className: "bg-purple-20 text-purple-k40 text-xs",
        onClick: () => {
          const currentValue = getValues(
            "spinDirection",
          ) as SnowJumpSpinDirection;
          const currentIndex = snowJumpSpinDirections.indexOf(
            currentValue ?? "none",
          );
          const nextIndex = (currentIndex + 1) % snowJumpSpinDirections.length;
          setValue(
            "spinDirection",
            snowJumpSpinDirections[nextIndex] as string,
          );
        },
      },
    },
    {
      name: "spinTypeId",
      title: "Spin Type",
      type: FormItemType.editSelect,
      className: "grid",
      hotkey: {
        value: "s",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = spinTypeOptions;
          if (!options?.length) return;

          const currentValue = getValues("spinTypeId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("spinTypeId", options[nextIndex]?.value ?? null);
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <SpinTypeSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "spinAmount",
      title: "Spin Amount",
      type: FormItemType.select,
      className: "grid",
      placeholder: "Select Spin Amount",
      defaultValue: "0",
      options: spinAmounts.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "d",
        className: "bg-red-20 text-red-k40 text-xs",
        onClick: () => {
          const currentValue = getValues("spinAmount");
          const currentIndex = spinAmounts.indexOf(currentValue ?? "0");
          const nextIndex = (currentIndex + 1) % spinAmounts.length;
          setValue("spinAmount", spinAmounts[nextIndex] ?? null);
        },
      },
    },
    {
      name: "spinModifierId",
      title: "Spin Modifier",
      type: FormItemType.editSelect,
      className: "grid",
      hotkey: {
        value: "f",
        className: "bg-springGreen-20 text-springGreen-k40 text-xs",
        onClick: () => {
          const options = modifierOptions;
          if (!options?.length) return;

          const currentValue = getValues("spinModifierId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("spinModifierId", options[nextIndex]?.value ?? "");
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <ModifierSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "grabTypeId",
      title: "Grab Type",
      type: FormItemType.editSelect,
      className: "grid col-span-full",
      hotkey: {
        value: "g",
        className: "bg-neonGreen-k40 text-neonGreen-20 text-xs",
        onClick: () => {
          const options = grabOptions;
          if (!options?.length) return;

          const currentValue = getValues("grabTypeId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("grabTypeId", options[nextIndex]?.value ?? "");
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <GrabSelect
          isSnowboard={isSnowboard}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
  ];

  const railFields: FormItemSlopeJumpProps[] = [
    {
      name: "score",
      title: "Score",
      type: FormItemType.number,
      className: "grid grid-cols-2 col-span-full pb-2.5",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "h",
        className: "bg-blue-k40 text-blue-20 text-xs",
        onClick: () => {
          if (!railTag) return;
          setValue("score", currentFrame?.toString() ?? "");
        },
      },
    },
    {
      name: "railFeatureId",
      title: "Rail Feature",
      type: FormItemType.editSelect,
      className: "grid grid-cols-2 col-span-full pb-2.5",

      hotkey: {
        value: "q",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = railFeatures;
          if (!options?.length) return;

          const currentValue = getValues("railFeatureId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railFeatureId", options[nextIndex]?.value ?? null);

          if (!railTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <RailFeatureSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "progression",
      title: "Progression",
      type: FormItemType.radio,
      className: "flex items-center col-span-full pb-2.5",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "w",
        className:
          "text-xs group-data-[state=checked]:bg-fuchsia-20 text-black data-[state=checked]:text-fuchsia-k40",
        onClick: () => {
          const currentValue = form.getValues("progression");
          setValue("progression", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Yes", value: "1" },
        { label: "No", value: "0" },
      ],
    },
    {
      name: "switch",
      title: "Orientation",
      type: FormItemType.radio,
      className: "flex items-center col-span-full pb-2.5",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "e",
        className:
          "group-data-[state=checked]:bg-orange-20 text-black data-[state=checked]:text-orange-k40",
        onClick: () => {
          const currentValue = form.getValues("switch");
          setValue("switch", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Switch", value: "1" },
        { label: "Forwards", value: "0" },
      ],
    },
    {
      name: "cab",
      title: "Cab",
      type: FormItemType.radio,
      className: "flex items-center col-span-full pb-2.5",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "r",
        className:
          "group-data-[state=checked]:bg-orange-k40 text-black data-[state=checked]:text-orange-20",
        onClick: () => {
          const currentValue = form.getValues("cab");
          setValue("cab", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Yes", value: "1" },
        { label: "No", value: "0" },
      ],
    },
    {
      name: "railInSpinDirection",
      title: "Spin In Direction",
      type: FormItemType.select,
      placeholder: "Select Spin Direction",
      className: "grid",
      defaultValue: "none",
      options: snowJumpSpinDirections.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "t",
        className: "bg-purple-20 text-purple-k40 text-xs",
        onClick: () => {
          const currentValue = getValues("railInSpinDirection")!;
          const currentIndex = snowRailSpinDirections.indexOf(
            currentValue ?? SnowRailSpinDirection.none,
          );
          const nextIndex = (currentIndex + 1) % snowRailSpinDirections.length;
          setValue("railInSpinDirection", snowRailSpinDirections[nextIndex]);
        },
      },
    },

    {
      name: "railInSpinAmount",
      title: "Spin In Amount",
      type: FormItemType.select,
      className: "grid",
      placeholder: "Select Spin Amount",
      defaultValue: "0",
      options: spinAmounts.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "y",
        className: "bg-red-20 text-red-k40 text-xs",
        onClick: () => {
          const currentValue = getValues("railInSpinAmount");
          const currentIndex = spinAmounts.indexOf(currentValue ?? "0");
          const nextIndex = (currentIndex + 1) % spinAmounts.length;
          setValue("railInSpinAmount", spinAmounts[nextIndex] ?? null);
        },
      },
    },
    {
      name: "railInSpinModifierId",
      title: "Spin In Modifier",
      type: FormItemType.editSelect,
      className: "grid",
      hotkey: {
        value: "u",
        className: "bg-springGreen-20 text-springGreen-k40 text-xs",
        onClick: () => {
          const options = modifierOptions;
          if (!options?.length) return;

          const currentValue = getValues("railInSpinModifierId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railInSpinModifierId", options[nextIndex]?.value ?? "");

          if (!railTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <ModifierSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "railInGrabId",
      title: "Grab Type",
      type: FormItemType.editSelect,
      className: "grid",
      hotkey: {
        value: "i",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = grabOptions;
          if (!options?.length) return;

          const currentValue = getValues("railInGrabId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railInGrabId", options[nextIndex]?.value ?? null);

          if (!railTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <SpinTypeSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "railTrickId",
      title: "Rail Trick",
      required: true,
      className: "col-span-full",
      type: FormItemType.editSelect,
      hotkey: {
        value: "a",
        className: "bg-orange-20 text-orange-k40 text-xs",
        onClick: () => {
          const options = railTrickOptions;
          if (!options?.length) return;

          const currentValue = getValues("railTrickId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railTrickId", options[nextIndex]?.value ?? null);

          if (!railTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <RailTrickSelect
          isSnowboard={isSnowboard}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
    {
      name: "railOnSpinDirection",
      title: "Spin On Direction",
      type: FormItemType.select,
      placeholder: "Select Spin Direction",
      defaultValue: "none",
      options: snowRailSpinDirections.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "s",
        className: "bg-purple-20 text-purple-k40 text-xs",
        onClick: () => {
          const currentValue = getValues(
            "railOnSpinDirection",
          ) as SnowRailSpinDirection;
          const currentIndex = snowRailSpinDirections.indexOf(
            currentValue ?? "none",
          );
          const nextIndex = (currentIndex + 1) % snowRailSpinDirections.length;
          setValue(
            "railOnSpinDirection",
            snowRailSpinDirections[nextIndex] ?? SnowRailSpinDirection.none,
          );
        },
      },
    },
    {
      name: "railOnSpinAmount",
      title: "Spin On Amount",
      type: FormItemType.select,
      placeholder: "Select Spin Amount",
      defaultValue: "0",
      options: spinAmounts.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "d",
        className: "bg-purple-20 text-purple-k40 text-xs",
        onClick: () => {
          const currentValue = getValues(
            "railOnSpinAmount",
          ) as SnowRailSpinDirection;
          const currentIndex = spinAmounts.indexOf(currentValue ?? "none");
          const nextIndex = (currentIndex + 1) % spinAmounts.length;
          setValue("railOnSpinAmount", spinAmounts[nextIndex]);
        },
      },
    },
    {
      name: "railOnSpinModifierId",
      title: "Spin On Modifier",
      type: FormItemType.editSelect,
      hotkey: {
        value: "f",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = railModifierOptions;
          if (!options?.length) return;

          const currentValue = getValues("railOnSpinModifierId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railOnSpinModifierId", options[nextIndex]?.value ?? null);

          if (!railTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <ModifierSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "railOnGrabId",
      title: "Grab On Type",
      type: FormItemType.editSelect,
      hotkey: {
        value: "g",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = grabOptions;
          if (!options?.length) return;

          const currentValue = getValues("railOnGrabId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railOnGrabId", options[nextIndex]?.value ?? null);

          if (!railTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <GrabSelect
          isSnowboard={isSnowboard}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
    {
      name: "railOutSpinDirection",
      title: "Rail Out Spin Direction",
      type: FormItemType.select,
      placeholder: "Select Spin Direction",
      defaultValue: "none",
      hotkey: {
        value: "p",
        className: "bg-purple-20 text-purple-k40 text-xs",
        onClick: () => {
          const currentValue = getValues(
            "railOutSpinDirection",
          ) as SnowRailSpinDirection;
          const currentIndex = snowRailSpinDirections.indexOf(
            currentValue ?? "none",
          );
          const nextIndex = (currentIndex + 1) % snowRailSpinDirections.length;
          setValue(
            "railOutSpinDirection",
            snowRailSpinDirections[nextIndex] ?? SnowRailSpinDirection.none,
          );
        },
      },
      options: snowRailSpinDirections.map((x) => ({
        label: x,
        value: x,
      })),
    },
    {
      name: "railOutSpinAmount",
      title: "Rail Out Spin Amount",
      type: FormItemType.select,
      placeholder: "Select Spin Amount",
      defaultValue: "0",
      hotkey: {
        value: "l",
        className: "bg-purple-20 text-purple-k40 text-xs",
        onClick: () => {
          const currentValue = getValues(
            "railOutSpinAmount",
          ) as SnowRailSpinDirection;
          const currentIndex = spinAmounts.indexOf(currentValue ?? "none");
          const nextIndex = (currentIndex + 1) % spinAmounts.length;
          setValue("railOutSpinAmount", spinAmounts[nextIndex]);
        },
      },
      options: spinAmounts.map((x) => ({
        label: x,
        value: x,
      })),
    },
    {
      name: "railOutSpinModifierId",
      title: "Rail Out Spin Modifier",
      type: FormItemType.editSelect,
      hotkey: {
        value: "k",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = railModifierOptions;
          if (!options?.length) return;

          const currentValue = getValues("railOutSpinModifierId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railOutSpinModifierId", options[nextIndex]?.value ?? null);

          if (!railTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <ModifierSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "railOutGrabId",
      title: "Rail Out Grab Type",
      type: FormItemType.editSelect,
      hotkey: {
        value: "m",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = grabOptions;
          if (!options?.length) return;

          const currentValue = getValues("railOutGrabId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railOutGrabId", options[nextIndex]?.value ?? null);

          if (!railTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <GrabSelect
          isSnowboard={isSnowboard}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
  ];

  const transitionFields: FormItemSlopeJumpProps[] = [
    {
      name: "transitionTypeId",
      title: "Transition Type",
      type: FormItemType.editSelect,
      className: "flex flex-col col-span-2 items-start gap-[5px] w-full",
      hotkey: {
        value: "q",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = transitionFeatures;
          if (!options?.length) return;

          const currentValue = getValues("transitionTypeId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("transitionTypeId", options[nextIndex]?.value ?? null);

          if (!transitionTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <TransitionTypeSelect value={field.value} onSelect={field.onChange} />
      ),
    },
    {
      name: "jumpTakeoffModifierId",
      title: "Spin Modifier(take off)",
      type: FormItemType.editSelect,
      className: "grid col-span-full",
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <ModifierSelect value={field.value} onSelect={field.onChange} />,
      hotkey: {
        value: "w",
        className: "bg-neonGreen-20 text-neonGreen-k40 text-xs",
        onClick: () => {
          const options = modifierOptions;
          if (!options?.length) return;

          const currentValue = getValues("jumpTakeoffModifierId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("jumpTakeoffModifierId", options[nextIndex]?.value ?? null);
        },
      },
    },
    {
      name: "progression",
      title: "Progression",
      type: FormItemType.radio,
      className: "flex items-center col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "e",
        className:
          "text-xs group-data-[state=checked]:bg-fuchsia-20 text-black data-[state=checked]:text-fuchsia-k40",
        onClick: () => {
          const currentValue = form.getValues("progression");
          setValue("progression", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Yes", value: "1" },
        { label: "No", value: "0" },
      ],
    },
    {
      name: "switch",
      title: "Orientation",
      type: FormItemType.radio,
      className: "flex items-center col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "r",
        className:
          "group-data-[state=checked]:bg-orange-20 text-black data-[state=checked]:text-orange-k40",
        onClick: () => {
          const currentValue = form.getValues("switch");
          setValue("switch", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Switch", value: "1" },
        { label: "Forwards", value: "0" },
      ],
    },
    {
      name: "cab",
      title: "Cab",
      type: FormItemType.radio,
      className: "flex items-center col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "t",
        className:
          "group-data-[state=checked]:bg-orange-k40 text-black data-[state=checked]:text-orange-20",
        onClick: () => {
          const currentValue = form.getValues("cab");
          setValue("cab", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Yes", value: "1" },
        { label: "No", value: "0" },
      ],
    },
    {
      name: "spinDirection",
      title: "Spin Direction",
      type: FormItemType.select,
      placeholder: "Select Spin Direction",
      className: "grid",
      defaultValue: "none",
      options: snowJumpSpinDirections.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "a",
        className: "bg-purple-20 text-purple-k40 text-xs",
        onClick: () => {
          const currentValue = getValues(
            "spinDirection",
          ) as SnowJumpSpinDirection;
          const currentIndex = snowJumpSpinDirections.indexOf(
            currentValue ?? "none",
          );
          const nextIndex = (currentIndex + 1) % snowJumpSpinDirections.length;
          setValue(
            "spinDirection",
            snowJumpSpinDirections[nextIndex] as string,
          );
        },
      },
    },
    {
      name: "spinTypeId",
      title: "Spin Type",
      type: FormItemType.editSelect,
      className: "grid",
      hotkey: {
        value: "s",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = spinTypeOptions;
          if (!options?.length) return;

          const currentValue = getValues("spinTypeId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("spinTypeId", options[nextIndex]?.value ?? null);

          if (!transitionTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <SpinTypeSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "spinAmount",
      title: "Spin Amount",
      type: FormItemType.select,
      className: "grid",
      placeholder: "Select Spin Amount",
      defaultValue: "0",
      options: spinAmounts.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "d",
        className: "bg-red-20 text-red-k40 text-xs",
        onClick: () => {
          const currentValue = getValues("spinAmount");
          const currentIndex = spinAmounts.indexOf(currentValue ?? "0");
          const nextIndex = (currentIndex + 1) % spinAmounts.length;
          setValue("spinAmount", spinAmounts[nextIndex] ?? null);
        },
      },
    },
    {
      name: "spinModifierId",
      title: "Spin Modifier",
      type: FormItemType.editSelect,
      className: "grid",
      hotkey: {
        value: "f",
        className: "bg-springGreen-20 text-springGreen-k40 text-xs",
        onClick: () => {
          const options = modifierOptions;
          if (!options?.length) return;

          const currentValue = getValues("spinModifierId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("spinModifierId", options[nextIndex]?.value ?? "");

          if (!transitionTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <ModifierSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "grabTypeId",
      title: "Grab Type",
      type: FormItemType.editSelect,
      className: "grid col-span-full",
      hotkey: {
        value: "g",
        className: "bg-neonGreen-k40 text-neonGreen-20 text-xs",
        onClick: () => {
          const options = grabOptions;
          if (!options?.length) return;

          const currentValue = getValues("grabTypeId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("grabTypeId", options[nextIndex]?.value ?? "");

          if (!transitionTag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <GrabSelect
          isSnowboard={isSnowboard}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
  ];

  const baseItems: FormItemSlopeJumpProps[] = [
    {
      name: "score",
      title: "Score",
      type: FormItemType.number,
      className: "grid grid-cols-2 col-span-full pb-2.5",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "h",
        className: "bg-blue-k40 text-blue-20 text-xs",
        onClick: () => {
          if (!railTag) return;
          setValue("score", currentFrame?.toString() ?? "");
        },
      },
    },
    {
      name: "grabStart",
      title: "Grab Start Frame",
      className: "grid",
      type: FormItemType.number,
      hotkey: {
        value: "3",
        className: "text-xs",
        onClick: () => {
          if (!currentFrame) return;
          setValue("grabStart", currentFrame.toString());
        },
      },
    },
    {
      name: "grabEnd",
      title: "Grab End Frame",
      className: "grid",
      type: FormItemType.number,
      hotkey: {
        value: "4",
        className: "text-xs",
        onClick: () => {
          if (!currentFrame) return;
          setValue("grabEnd", currentFrame.toString());
        },
      },
    },
    {
      name: "takeOffFrame",
      title: "Take-off Frame",
      className: "grid",
      type: FormItemType.number,
      hotkey: {
        value: "5",
        className: "text-xs",
        onClick: () => {
          if (!currentFrame) return;
          setValue("takeOffFrame", currentFrame.toString());
        },
      },
    },
    {
      name: "landingFrame",
      title: "Landing Frame",
      className: "grid",
      type: FormItemType.number,
      hotkey: {
        value: "6",
        className: "text-xs",
        onClick: () => {
          if (!currentFrame) return;
          setValue("landingFrame", currentFrame.toString());
        },
      },
    },

    {
      name: "executionId",
      title: "Execution",
      type: FormItemType.editSelect,
      className: "col-span-full",
      hotkey: {
        value: "b",
        className: "bg-neonGreen-k40 text-neonGreen-20 text-xs",
        onClick: () => {
          const options = executionOptions;
          if (!options?.length) return;

          const currentValue = getValues("executionId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("executionId", options[nextIndex]?.value ?? "");
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <ExecutionSelect
          isJump={true}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
    {
      name: "landingZone",
      title: "Landing Zone:",
      type: FormItemType.radio,
      className: "flex items-start  col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "z",
        className:
          "group-data-[state=checked]:bg-neonGreen text-black/60 text-xs data-[state=checked]:text-black/60",
        onClick: () => {
          const currentValue = form.getValues("landingZone");
          const currentIndex = snowLandingZones.indexOf(
            currentValue as SnowLandingZone,
          );
          const nextIndex = (currentIndex + 1) % snowLandingZones.length;
          setValue("landingZone", snowLandingZones[nextIndex] ?? "");
        },
      },
      options: snowLandingZones.map((x) => ({
        label: x,
        value: x,
      })),
    },
    {
      name: "landingType",
      title: "Landing Type:",
      type: FormItemType.radio,
      className: "flex items-start  col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "x",
        className: "group-data-[state=checked]:bg-blue text-black text-xs",
        onClick: () => {
          const currentValue = getValues("landingType");
          const currentIndex = snowLandingTypes.indexOf(
            currentValue as SnowLandingType,
          );
          const nextIndex = (currentIndex + 1) % snowLandingTypes.length;
          setValue("landingType", snowLandingTypes[nextIndex] ?? "");
        },
      },
      options: snowLandingTypes.map((x) => ({
        label: x,
        value: x,
      })),
    },
  ];

  const items =
    landingType === SnowLandingType.none
      ? baseItems
      : [
          ...baseItems,
          landingDescriptionFormItem({
            options: landingDescriptions
              .filter((x) => x !== LandingDescription["lost ski"])
              .map((x) => ({
                label: x,
                value: x,
              })),
          }),
        ];

  const firstItem = items[0];

  const remainingItems = items.slice(1);

  const railDisplayItems = (
    <div className="col-span-full">
      {/* Basic Details Section */}
      <div className="col-span-full mb-4">
        {railFields.slice(0, 5).map((item) => (
          <FormItem key={item.name} control={form.control} {...item} />
        ))}
      </div>

      {/* Rail In Section */}
      <div className="col-span-full mb-4">
        <Label className="mb-2 !text-smallLabel uppercase text-black/60">
          SPIN IN:
        </Label>
        <div className="grid grid-cols-2 gap-2">
          {railFields.slice(5, 9).map((item) => (
            <FormItem key={item.name} control={form.control} {...item} />
          ))}
        </div>
      </div>

      {/* Rail On Section */}
      <div className="col-span-full mb-4">
        <Label className="mb-2 !text-smallLabel uppercase text-black/60">
          SPIN ON:
        </Label>
        <div className="grid grid-cols-2 gap-2">
          {railFields.slice(9, 14).map((item) => (
            <FormItem key={item.name} control={form.control} {...item} />
          ))}
        </div>
      </div>

      {/* Rail Out Section */}
      <div className="col-span-full mb-4">
        <Label className="mb-2 !text-smallLabel uppercase text-black/60">
          SPIN OUT:
        </Label>
        <div className="grid grid-cols-2 gap-2">
          {railFields.slice(14, 18).map((item) => (
            <FormItem key={item.name} control={form.control} {...item} />
          ))}
        </div>
      </div>

      {/* Landing Details */}
      <div className="col-span-full">
        <Label className="mb-2 !text-smallLabel uppercase text-black/60">
          LANDING DETAILS:
        </Label>
        <div className="grid grid-cols-2 gap-2">
          {remainingItems.map((item) => (
            <FormItem key={item.name} control={form.control} {...item} />
          ))}
        </div>
      </div>
    </div>
  );

  let displayItems = [];

  switch (section) {
    case SnowFeatureType.jump:
      displayItems = [firstItem, ...jumpFields, ...remainingItems];
      break;
    case SnowFeatureType.rail:
      return (
        <Card className="grid grid-cols-2 gap-2.5">
          <div className="col-span-full">
            <Label className="!text-smallLabel uppercase text-black/60">
              {getSectionLabel(section)}
            </Label>
          </div>
          {railDisplayItems}
        </Card>
      );
    case SnowFeatureType.transition:
      displayItems = [firstItem, ...transitionFields, ...remainingItems];
      break;
    default:
      displayItems = items;
  }

  return (
    <Card className="grid grid-cols-2 gap-2.5">
      <div className="col-span-full">
        <Label className="!text-smallLabel uppercase text-black/60">
          {getSectionLabel(section)}
        </Label>
      </div>
      {displayItems.map(
        (item) =>
          item && <FormItem key={item.name} control={form.control} {...item} />,
      )}
    </Card>
  );
};
