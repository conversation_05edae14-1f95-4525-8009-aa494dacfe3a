import { db } from "~/server/db";
import { swimmingTags } from "~/server/db/schema";

export const seedSnow = async () => {
  const videoId = "k7gktj9n0s5g1s0cmqj7zd2f"; //long snow sport video
  console.log("Seeding snowsport data");
  // await seedSplit();
  // await seedRaceAthletes();
  const tagFrames = Array.from({ length: 450 }, (_, i) => i * 250);
  console.log(tagFrames);
  await db.insert(swimmingTags).values(
    tagFrames.map((frame) => ({
      videoId,
      athleteId: "5c2c7177-78bf-4ff5-a5b7-a02b6660b01b",
      frame,
      tag: "Stroke Count",
      userId: "1",
    })),
  );
  await db.insert(swimmingTags).values(
    tagFrames.map((frame) => ({
      videoId,
      athleteId: "5c2c7177-78bf-4ff5-a5b7-a02b6660b01b",
      frame: frame + 10,
      tag: "Gun",
      userId: "1",
    })),
  );
  await db.insert(swimmingTags).values(
    tagFrames.map((frame) => ({
      videoId,
      athleteId: "5c2c7177-78bf-4ff5-a5b7-a02b6660b01b",
      frame: frame + 20,
      tag: "Finish",
      userId: "1",
    })),
  );
  await db.insert(swimmingTags).values(
    tagFrames.map((frame) => ({
      videoId,
      athleteId: "5c2c7177-78bf-4ff5-a5b7-a02b6660b01b",
      frame: frame + 30,
      tag: "Stroke Cycle",
      userId: "1",
    })),
  );
  await db.insert(swimmingTags).values(
    tagFrames.map((frame) => ({
      videoId,
      athleteId: "5c2c7177-78bf-4ff5-a5b7-a02b6660b01b",
      frame: frame + 40,
      tag: "Rotation",
      userId: "1",
    })),
  );
  await db.insert(swimmingTags).values(
    tagFrames.map((frame) => ({
      videoId,
      athleteId: "5c2c7177-78bf-4ff5-a5b7-a02b6660b01b",
      frame: frame + 50,
      tag: "UW",
      userId: "1",
    })),
  );
  await db.insert(swimmingTags).values(
    tagFrames.map((frame) => ({
      videoId,
      athleteId: "5c2c7177-78bf-4ff5-a5b7-a02b6660b01b",
      frame: frame + 60,
      tag: "Swim",
      userId: "1",
    })),
  );
  await db.insert(swimmingTags).values(
    tagFrames.map((frame) => ({
      videoId,
      athleteId: "5c2c7177-78bf-4ff5-a5b7-a02b6660b01b",
      frame: frame + 70,
      tag: "Breath",
      userId: "1",
    })),
  );
};
