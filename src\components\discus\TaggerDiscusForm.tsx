import { useState } from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { AthleteSelect } from "~/app/validation/components/AthleteSelect";
import { Card } from "~/components/Card";
import { HotkeyButton } from "~/components/HotkeyButton";
import { Input } from "~/components/ui/input";
import { useStore } from "~/hooks/store";
import { useDiscusTags } from "~/hooks/useDiscusTags";
import { DiscusHand, DiscusTag, discusTagsUI } from "~/lib/enums/discus";
import { cn, getAthleteOptions, getTagDisplay } from "~/lib/utils";
import { AddDiscusThrowButton } from "./AddDiscusThrowButton";
import { DiscusThrowForm } from "./DiscusThrowForm";

const tagOptions = discusTagsUI["key phase"];

export const TaggerDiscusForm = () => {
  const filteredTags = useStore((state) => state.filteredTags);
  const currentFrame = useStore((state) => state.currentFrame);
  const videoSummary = useStore((state) => state.videoSummary);
  const discusThrow = useStore((state) => state.discusThrow);

  const { tags, onAddTag } = useDiscusTags();

  const [editingValues, setEditingValues] = useState<Record<string, string>>(
    {},
  );

  const handleInputChange = (tagValue: string, value: string) => {
    setEditingValues((prev) => ({
      ...prev,
      [tagValue]: value,
    }));
  };

  useHotkeys("1", () => onAddTag(DiscusTag.WindUp));
  useHotkeys("2", () => onAddTag(DiscusTag.Entry));
  useHotkeys("3", () => onAddTag(DiscusTag.RightToeOff));
  useHotkeys("4", () => onAddTag(DiscusTag.LeftFootDown));
  useHotkeys("5", () => onAddTag(DiscusTag.RightFootDown));
  useHotkeys("6", () => onAddTag(DiscusTag.RearFootOff));
  useHotkeys("7", () => onAddTag(DiscusTag.FrontFootOff));
  useHotkeys("8", () => onAddTag(DiscusTag.Release));
  useHotkeys("9", () => onAddTag(DiscusTag.PostRelease));

  return (
    <>
      <div className="flex flex-1 flex-col gap-2.5">
        <DiscusThrowForm />

        <Card className="gap-2.5 text-smallLabel">
          <p className="uppercase text-black/60">Key phases</p>
          <div className="flex flex-col gap-2.5">
            {tagOptions.map((tagOption) => {
              if (!tagOption?.value) return null;

              // Find existing tag for the current throw
              const existingTag = filteredTags.find(
                (tag) => tag.tag === tagOption?.value?.toString(),
              );

              const isSelected = existingTag?.frame === currentFrame;

              const displayValue =
                editingValues[tagOption.value] ?? existingTag?.frame ?? "";
              const tagDisplay = getTagDisplay({
                label: tagOption.value,
                leftHand: discusThrow?.hand === DiscusHand.LeftHand,
                sport: "discus",
              });

              return (
                <div
                  key={tagOption.value}
                  className="flex w-full items-start justify-between"
                  id={existingTag?.id ?? `tag-${tagOption.value}`}
                >
                  <p className="text-smallLabel text-gray">{tagDisplay}</p>
                  <div className="flex items-start gap-[5px]">
                    <Input
                      type="number"
                      className={cn(
                        "h-5",
                        isSelected && "ring-2 ring-gray ring-offset-2",
                      )}
                      value={displayValue}
                      onChange={(e) =>
                        handleInputChange(tagOption.value, e.target.value)
                      }
                      onBlur={(e) => {
                        const value = e.target.value;
                        if (+value === existingTag?.frame) return;
                        onAddTag(tagOption.value, +value);
                      }}
                      onFinish={(value) => {
                        if (+value === existingTag?.frame) return;
                        onAddTag(tagOption.value, +value);
                      }}
                      placeholder={existingTag ? "" : "Add..."}
                      title={
                        existingTag
                          ? `Edit frame for ${tagOption.value}`
                          : `Add tag ${tagOption.value} at specified frame`
                      }
                    />
                    <HotkeyButton
                      tag={tagOption}
                      onClick={() => onAddTag(tagOption.value)}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </Card>
        <AddDiscusThrowButton />
      </div>
    </>
  );
};
