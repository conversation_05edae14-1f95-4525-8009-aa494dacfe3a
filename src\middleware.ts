import { withAuth } from "next-auth/middleware";

export default withAuth(
  // `withAuth` augments your `Request` with the user's token.
  function middleware(req) {
    //todo role validation
    const path = req.nextUrl.pathname;
  },
  {
    callbacks: {
      authorized: ({ token }) => {
        const userRoles = token?.roles as undefined | null | string[];
        if (!userRoles?.includes("analyst")) {
          return false;
        }
        return true;
      },
    },
  },
);

export const config = {
  matcher: [
    "/",
    "/tagger",
    "/tagger/:path*",
    "/validation",
    "/validation/:path*",
    "/api-doc",
  ],
};
