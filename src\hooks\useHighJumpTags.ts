"use client";

import { useParams } from "next/navigation";
import toast from "react-hot-toast";
import { api } from "~/trpc/react";
import { useStore } from "./store";

export const useHighJumpTags = () => {
  const params = useParams<{ id: string }>();
  const videoId = params.id;
  const utils = api.useUtils();
  const selectedHighJump = useStore((state) => state.selectedHighJump);

  const { mutate: deleteStrides } = api.highJump.deleteStrides.useMutation({
    onError: () => {
      toast.error("Failed to delete stride");
      void utils.highJump.getTags.invalidate();
    },
  });

  const onDeleteStrides = (ids: string[]) => {
    if (!selectedHighJump) return;
    const idsToDelete = ids.map((id) => +id);
    deleteStrides(idsToDelete);
    utils.highJump.getTags.setData({ videoId }, (data) => {
      if (!data) return [];
      return data.map((jump) =>
        jump.id === selectedHighJump.id
          ? {
              ...jump,
              strides: jump.strides.filter((y) => !idsToDelete.includes(y.id)),
            }
          : jump,
      );
    });
  };

  const { mutate: deleteFrame } = api.highJump.deleteFrame.useMutation({
    onError: () => {
      toast.error("Failed to delete frame");
      void utils.highJump.getTags.invalidate();
    },
  });

  const onDeleteFrame = (
    id: string,
    startFrame?: number,
    endFrame?: number,
  ) => {
    if (!selectedHighJump) return;
    deleteFrame({ id: +id, startFrame, endFrame });
    utils.highJump.getTags.setData({ videoId }, (data) => {
      if (!data) return [];
      return data.map((jump) =>
        jump.id === selectedHighJump.id
          ? {
              ...jump,
              startFrame: startFrame ?? jump.startFrame,
              endFrame: endFrame ?? jump.endFrame,
            }
          : jump,
      );
    });
  };

  return { onDeleteStrides, onDeleteFrame };
};
