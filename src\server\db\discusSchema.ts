import type { InferSelectModel } from "drizzle-orm";
import { relations, sql } from "drizzle-orm";
import {
  boolean,
  int,
  mysqlEnum,
  timestamp,
  unique,
  varchar,
} from "drizzle-orm/mysql-core";
import { discusHand, discusMovements, discusTypes } from "~/lib/enums/discus";
import { createTable } from "./schema";

export const discusThrows = createTable(
  "discus_throws",
  {
    id: int("id").primaryKey().autoincrement(),
    videoId: varchar("video_id", { length: 255 }).notNull(),
    athleteId: varchar("athlete_id", { length: 255 }).notNull(),
    number: int("number").notNull(),
    movement: mysqlEnum(discusMovements).notNull(),
    type: mysqlEnum(discusTypes).notNull(),
    hand: mysqlEnum(discusHand).notNull(),
    nonReverse: boolean("non_reverse").notNull().default(false),
    weight: int("weight"),
    denfyTool: boolean("denfy_tool").notNull().default(false),
    userId: varchar("user_id", { length: 255 }).notNull(),
    dateCreated: timestamp("date_created", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
  },
  (table) => [unique().on(table.videoId, table.number)],
);

export type DiscusThrows = InferSelectModel<typeof discusThrows>;

export const discusThrowsRelations = relations(discusThrows, ({ many }) => ({
  phases: many(discusTags),
}));

export const discusTags = createTable(
  "discus_tags",
  {
    id: int("id").primaryKey().autoincrement(),
    throwId: int("throw_id"),
    tag: varchar("phase", { length: 100 }).notNull(),
    aiFrame: int("ai_frame"),
    frame: int("frame").notNull(),
    userId: varchar("user_id", { length: 255 }).notNull(),
    isDeleted: boolean("is_deleted").notNull().default(false), // if an AI tag is deleted, this will be true
  },
  (table) => [unique().on(table.throwId, table.tag)],
);

export type DiscusTags = InferSelectModel<typeof discusTags>;

export const discusTagsRelations = relations(discusTags, ({ one }) => ({
  throw: one(discusThrows, {
    fields: [discusTags.throwId],
    references: [discusThrows.id],
  }),
}));
