import type { TagUI } from "../interface";

export enum HighJumpType {
  Flop = "flop",
  Sit = "sit",
  RunThru = "run thru",
  Scissor = "scissor",
  Drill = "drill",
}

export const highJumpTypes = Object.values(HighJumpType) as [
  HighJumpType,
  ...HighJumpType[],
];

export enum HighJumpSide {
  LeftSide = "left side",
  RightSide = "right side",
}
export const highJumpSides = Object.values(HighJumpSide) as [
  HighJumpSide,
  ...HighJumpSide[],
];

export enum HighJumpTag {
  HeelContact = "Heel Contact",
  ToeOff = "Toe Off",
  StartFrame = "Start Frame",
  EndFrame = "End Frame",
}

export const highJumpTags: { jump: TagUI[]; stride: TagUI[] } = {
  jump: [
    {
      value: HighJumpTag.StartFrame,
      label: "Jump Start",
      key: "3",
      className: "bg-fuchsia text-white border-black/30",
    },
    {
      value: HighJumpTag.EndFrame,
      label: "Jump End",
      key: "4",
      className: "bg-orange text-white border-black/30",
    },
  ],
  stride: [
    {
      value: HighJumpTag.HeelContact,
      key: "1",
      className: "bg-neonGreen text-black/60 border-black/60",
    },
    {
      value: HighJumpTag.ToeOff,
      key: "2",
      className: "bg-blue text-white border-black/30",
    },
  ],
};
