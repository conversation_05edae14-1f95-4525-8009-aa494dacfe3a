import { and, eq, gte, lte } from "drizzle-orm";
import {
  bodyKeypoints,
  bodyAngles,
  shotputThrows,
  shotputTags,
} from "~/server/db/schema";
import { getVideoSummary } from "./video";
import { db } from "~/server/db";

export interface PoseDataWithBuffering {
  videoId: string;
  throws: Array<{
    throwId: number;
    frameRange: {
      min: number;
      max: number;
      originalMin: number;
      originalMax: number;
    };
    keypoints: Array<{
      frameNumber: number;
      keypointNum: number;
      x: number;
      y: number;
    }>;
    angles: Array<{
      frameNumber: number;
      name: string;
      angle: number;
      aiScore: number;
    }>;
  }>;
}

export async function getPoseDataWithBuffering(
  videoId: string,
): Promise<PoseDataWithBuffering> {
  // Get video info to access FPS
  const videoInfo = await getVideoSummary(videoId);
  const fps = videoInfo?.fps ?? 30; // Default to 30 fps if not available

  const throws = await db.query.shotputThrows.findMany({
    where: eq(shotputThrows.videoId, videoId),
    with: {
      phases: {
        where: eq(shotputTags.isDeleted, false),
      },
    },
  });

  const throwsWithKeypoints = [];

  for (const throwData of throws) {
    if (!throwData.phases || throwData.phases.length === 0) {
      continue;
    }

    const frames = throwData.phases.map((phase) => phase.frame);
    const minFrame = Math.min(...frames);
    const maxFrame = Math.max(...frames);

    // Buffer by FPS (1 second before and after)
    const bufferedMinFrame = Math.max(0, minFrame - fps);
    const bufferedMaxFrame = maxFrame + fps;

    const [keypoints, angles] = await Promise.all([
      db.query.bodyKeypoints.findMany({
        where: and(
          eq(bodyKeypoints.videoId, videoId),
          gte(bodyKeypoints.frameNumber, bufferedMinFrame),
          lte(bodyKeypoints.frameNumber, bufferedMaxFrame),
        ),
        columns: {
          frameNumber: true,
          keypointNum: true,
          x: true,
          y: true,
          // Just in case we need in future
          // z: true,
          // aiScore: true,
        },
      }),

      db.query.bodyAngles.findMany({
        where: and(
          eq(bodyAngles.videoId, videoId),
          gte(bodyAngles.frameNumber, bufferedMinFrame),
          lte(bodyAngles.frameNumber, bufferedMaxFrame),
        ),
        columns: {
          frameNumber: true,
          name: true,
          angle: true,
          aiScore: true,
        },
      }),
    ]);

    throwsWithKeypoints.push({
      throwId: throwData.id,
      frameRange: {
        min: bufferedMinFrame,
        max: bufferedMaxFrame,
        originalMin: minFrame,
        originalMax: maxFrame,
      },
      keypoints,
      angles,
    });
  }

  return {
    videoId,
    throws: throwsWithKeypoints,
  };
}
