"use client";

import Link from "next/link";
import type { GetVideosOutput } from "~/server/api/routers/video";
import dayjs from "dayjs";
import Image from "next/image";
import { api } from "~/trpc/react";
import { Skeleton } from "~/components/ui/skeleton";
import { usePathname } from "next/navigation";

interface VideoPreviewProps {
  video: GetVideosOutput["list"][0];
}

export const VideoCard = ({ video }: VideoPreviewProps) => {
  const pathname = usePathname();

  const { data, isLoading } = api.videos.getPlayback.useQuery(
    { id: video.id },
    {
      staleTime: Infinity,
    },
  );

  return (
    <Link
      href={`${pathname}/${video.id}`}
      className="col-span-2 flex flex-col items-start gap-2.5"
    >
      {isLoading && (
        <Skeleton className="flex aspect-video w-full items-center justify-center rounded-[5px]" />
      )}
      {data && (
        <div className="relative aspect-video w-full rounded-[5px] bg-black">
          <Image
            src={data?.thumbnailUrl}
            alt={video.id}
            fill
            className="rounded-[5px]"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
      )}
      <div className="flex flex-col items-start gap-[5px] self-stretch">
        <p className="line-clamp-2 h-[26px] truncate whitespace-normal break-all text-xs leading-3 tracking-tight">
          {video.title}
        </p>
        <p className="text-smallLabel text-gray">
          Added on {dayjs(video.createdAt).format("DD/MM/YYYY")}
        </p>
      </div>
    </Link>
  );
};
