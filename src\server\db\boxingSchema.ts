import { boolean, int, mysqlEnum, varchar } from "drizzle-orm/mysql-core";
import { createTable } from "./schema";
import type { InferSelectModel } from "drizzle-orm";
import { boxingCorner, boxingPunchTypes } from "~/lib/enums/boxing";

export const boxingPunches = createTable("boxing", {
  id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  videoId: varchar("video_id", { length: 255 }).notNull(),
  athleteId: varchar("athlete_id", { length: 255 }).notNull(),
  startFrame: int("startFrame").notNull(),
  number: int("number").notNull(),
  round: int("round").notNull(),
  punch: mysqlEnum(boxingPunchTypes),
  isSuccess: boolean("").notNull().default(false),
  head: boolean("head").notNull().default(false),
  unsure: boolean("unsure").notNull().default(false),
  feint: boolean("feint").notNull().default(false),
  clinch: boolean("clinch").notNull().default(false),
  switch: boolean("switch").notNull().default(false),
  color: mysqlEnum(boxingCorner),
});

export type BoxingPunches = InferSelectModel<typeof boxingPunches>;
