import type { TagUI } from "../interface";

export enum DiscusMovement {
  Rotation = "rotation",
  Glide = "glide",
}

export const discusMovements = Object.values(DiscusMovement) as [
  DiscusMovement,
  ...DiscusMovement[],
];

export const discusMovementOptions = [
  {
    value: DiscusMovement.Rotation,
    key: "z",
  },
  {
    value: DiscusMovement.Glide,
    key: "x",
  },
];

export enum DiscusType {
  Full = "Full",
  Standing = "Standing",
  Half = "Half",
  SouthAfrican = "South African",
  StepIn = "Step in",
  FullWalking = "Full Walking",
}

export const discusTypes = Object.values(DiscusType) as [
  DiscusType,
  ...DiscusType[],
];

export const discusTypeOptions = [
  {
    value: DiscusType.Full,
    key: "q",
  },
  {
    value: DiscusType.Standing,
    key: "w",
  },
  {
    value: DiscusType.Half,
    key: "e",
  },
  {
    value: DiscusType.SouthAfrican,
    key: "a",
  },
  {
    value: DiscusType.StepIn,
    key: "s",
  },
  {
    value: DiscusType.FullWalking,
    key: "d",
  },
];

export enum DiscusHand {
  RightHand = "right hand",
  LeftHand = "left hand",
}
export const discusHand = Object.values(DiscusHand) as [
  DiscusHand,
  ...DiscusHand[],
];

export const discusHandOptions = [
  {
    value: DiscusHand.LeftHand,
    key: "c",
  },
  {
    value: DiscusHand.RightHand,
    key: "v",
  },
];

// Tags for key phases in discus throw (same as Discus)
export enum DiscusTag {
  WindUp = "Wind Up",
  Entry = "Entry",
  RightToeOff = "Right Toe Off",
  LeftFootDown = "Left Foot Down",
  RightFootDown = "Right Foot Down",
  RearFootOff = "Rear Foot Off",
  FrontFootOff = "Front Foot Off",
  Release = "Release",
  PostRelease = "Post Release",
}

export const discusTags = Object.values(DiscusTag) as [
  DiscusTag,
  ...DiscusTag[],
];

export const discusTagsUI: { "key phase": TagUI[] } = {
  "key phase": [
    {
      value: DiscusTag.WindUp,
      key: "1",
      className: "bg-neonGreen-20 text-neonGreen-k40",
    },
    {
      value: DiscusTag.Entry,
      key: "2",
      className: "bg-blue-20 text-blue-k40",
    },
    {
      value: DiscusTag.RightToeOff,
      key: "3",
      className: "bg-blue-20 text-blue-k40",
    },
    {
      value: DiscusTag.LeftFootDown,
      key: "4",
      className: "bg-fuchsia-20 text-fuchsia-k40",
    },
    {
      value: DiscusTag.RightFootDown,
      key: "5",
      className: "bg-orange-20 text-orange-k40",
    },
    {
      value: DiscusTag.RearFootOff,
      key: "6",
      className: "bg-purple-20 text-purple-k40",
    },
    {
      value: DiscusTag.FrontFootOff,
      key: "7",
      className: "bg-yellow-20 text-yellow-k40",
    },
    {
      value: DiscusTag.Release,
      key: "8",
      className: "bg-springGreen-20 text-springGreen-k40",
    },
    {
      value: DiscusTag.PostRelease,
      key: "9",
      className: "bg-neonGreen-k40 text-neonGreen-20",
    },
  ],
};
