"use client";

import { usePathname, usePara<PERSON>, useRouter } from "next/navigation";
import { Button } from "./ui/button";
import { api } from "~/trpc/react";
import { toast } from "react-hot-toast";

export const CompleteTagButton = () => {
  const pathname = usePathname();
  const params = useParams<{ id: string }>();
  const router = useRouter();

  const videoId = params.id;
  const isTagger = videoId && pathname.startsWith("/tagger/");

  const { mutate: completeTag, isPending } = api.videos.completeTag.useMutation(
    {
      onSuccess: () => {
        toast.success("Annotation completed");
        router.refresh();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    },
  );

  return (
    <>
      {!isTagger && null}
      {isTagger && (
        <Button
          onClick={() => completeTag({ id: videoId })}
          disabled={isPending}
        >
          Complete Annotation
        </Button>
      )}
    </>
  );
};
