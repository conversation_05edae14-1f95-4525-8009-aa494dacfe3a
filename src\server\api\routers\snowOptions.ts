import { and, eq } from "drizzle-orm";
import { z } from "zod";
import {
  snowExecutions,
  snowGrabs,
  snowJumps,
  snowModifiers,
  snowRailFeatures,
  snowSpinTypes,
  snowTransitions,
  snowTricks,
} from "~/server/db/snowSchema";
import type { RouterOutputs } from "~/trpc/react";
import { createTRPCRouter, protectedProcedure } from "../trpc";

type Outputs = RouterOutputs["snowOptions"];

export type GetRaceOutput = Outputs["getJumpTypes"];

export const snowOptionsRouter = createTRPCRouter({
  //jump type
  getJumpTypes: protectedProcedure
    .input(z.object({ isSnowboard: z.boolean() }))
    .query(async ({ ctx, input }) => {
      const types = await ctx.db.query.snowJumps.findMany({
        where: and(
          eq(snowJumps.active, true),
          eq(snowJumps.isSnowboard, input.isSnowboard),
        ),
      });
      return types.map((x) => ({
        label: x.name,
        value: x.id.toString(),
      }));
    }),
  addJumpType: protectedProcedure
    .input(
      z.object({
        name: z.string(),
        isSnowboard: z.boolean(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .insert(snowJumps)
        .values(input)
        .onDuplicateKeyUpdate({
          set: {
            active: true,
          },
        });
    }),
  deleteJumpType: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .update(snowJumps)
        .set({ active: false })
        .where(eq(snowJumps.id, input.id));
    }),

  //modifiers
  getModifiers: protectedProcedure.query(async ({ ctx }) => {
    const res = await ctx.db.query.snowModifiers.findMany({
      where: eq(snowModifiers.active, true),
    });
    return res.map((x) => ({
      label: x.name,
      value: x.id.toString(),
    }));
  }),
  addModifier: protectedProcedure
    .input(z.object({ name: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .insert(snowModifiers)
        .values(input)
        .onDuplicateKeyUpdate({
          set: {
            active: true,
          },
        });
    }),
  deleteModifier: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .update(snowModifiers)
        .set({ active: false })
        .where(eq(snowModifiers.id, input.id));
    }),

  //spin type
  getSpinTypes: protectedProcedure.query(async ({ ctx }) => {
    const res = await ctx.db.query.snowSpinTypes.findMany({
      where: eq(snowSpinTypes.active, true),
    });
    return res.map((x) => ({
      label: x.name,
      value: x.id.toString(),
    }));
  }),
  addSpinType: protectedProcedure
    .input(z.object({ name: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .insert(snowSpinTypes)
        .values(input)
        .onDuplicateKeyUpdate({
          set: {
            active: true,
          },
        });
    }),
  deleteSpinType: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .update(snowSpinTypes)
        .set({ active: false })
        .where(eq(snowSpinTypes.id, input.id));
    }),

  //grabs
  getGrabs: protectedProcedure
    .input(z.object({ isSnowboard: z.boolean() }))
    .query(async ({ ctx, input }) => {
      const res = await ctx.db.query.snowGrabs.findMany({
        where: and(
          eq(snowGrabs.active, true),
          eq(snowGrabs.isSnowboard, input.isSnowboard),
        ),
      });
      return res.map((x) => ({
        label: x.name,
        value: x.id.toString(),
      }));
    }),
  addGrab: protectedProcedure
    .input(z.object({ name: z.string(), isSnowboard: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .insert(snowGrabs)
        .values(input)
        .onDuplicateKeyUpdate({
          set: {
            active: true,
          },
        });
    }),
  deleteGrab: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .update(snowGrabs)
        .set({ active: false })
        .where(eq(snowGrabs.id, input.id));
    }),

  //execution
  getExecutions: protectedProcedure
    .input(z.object({ isJump: z.boolean() }))
    .query(async ({ ctx, input }) => {
      const res = await ctx.db.query.snowExecutions.findMany({
        where: and(
          eq(snowExecutions.active, true),
          eq(snowExecutions.isJump, input.isJump),
        ),
      });
      return res.map((x) => ({
        label: x.name,
        value: x.id.toString(),
      }));
    }),
  addExecution: protectedProcedure
    .input(z.object({ name: z.string(), isJump: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .insert(snowExecutions)
        .values(input)
        .onDuplicateKeyUpdate({
          set: {
            active: true,
          },
        });
    }),
  deleteExecution: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .update(snowExecutions)
        .set({ active: false })
        .where(eq(snowExecutions.id, input.id));
    }),

  //rail feature
  getRailFeatures: protectedProcedure.query(async ({ ctx }) => {
    const res = await ctx.db.query.snowRailFeatures.findMany({
      where: eq(snowRailFeatures.active, true),
    });
    return res.map((x) => ({
      label: x.name,
      value: x.id.toString(),
    }));
  }),
  addRailFeature: protectedProcedure
    .input(z.object({ name: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .insert(snowRailFeatures)
        .values(input)
        .onDuplicateKeyUpdate({
          set: {
            active: true,
          },
        });
    }),
  deleteRailFeature: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .update(snowRailFeatures)
        .set({ active: false })
        .where(eq(snowRailFeatures.id, input.id));
    }),

  //rail trick
  getRailTricks: protectedProcedure
    .input(z.object({ isSnowboard: z.boolean() }))
    .query(async ({ ctx, input }) => {
      const res = await ctx.db.query.snowTricks.findMany({
        where: and(
          eq(snowTricks.active, true),
          eq(snowTricks.isSnowboard, input.isSnowboard),
        ),
      });
      return res.map((x) => ({
        label: x.name,
        value: x.id.toString(),
      }));
    }),
  addRailTrick: protectedProcedure
    .input(z.object({ name: z.string(), isSnowboard: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .insert(snowTricks)
        .values(input)
        .onDuplicateKeyUpdate({
          set: {
            active: true,
          },
        });
    }),
  deleteRailTrick: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .update(snowTricks)
        .set({ active: false })
        .where(eq(snowTricks.id, input.id));
    }),

  //transition type
  getTransitionTypes: protectedProcedure.query(async ({ ctx }) => {
    const res = await ctx.db.query.snowTransitions.findMany({
      where: eq(snowTransitions.active, true),
    });
    return res.map((x) => ({
      label: x.name,
      value: x.id.toString(),
    }));
  }),
  addTransitionType: protectedProcedure
    .input(z.object({ name: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .insert(snowTransitions)
        .values(input)
        .onDuplicateKeyUpdate({
          set: {
            active: true,
          },
        });
    }),
  deleteTransitionType: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .update(snowTransitions)
        .set({ active: false })
        .where(eq(snowTransitions.id, input.id));
    }),
});
