import { Dr<PERSON><PERSON>Adapter } from "@auth/drizzle-adapter";
import {
  getServerSession,
  type DefaultSession,
  type NextAuthOptions,
} from "next-auth";
import { type Adapter } from "next-auth/adapters";
import KeycloakProvider, {
  type KeycloakProfile,
} from "next-auth/providers/keycloak";
import type { OAuthConfig } from "next-auth/providers/oauth";
import { env } from "~/env";
import type { RequestTokenResponse } from "~/lib/interface";
import type { PTAAthlete } from "~/lib/interfaces/externalCall";
import { db } from "~/server/db";

import { accounts, users } from "~/server/db/schema";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      roles: string[];
      athletes?: string[];
    } & DefaultSession["user"];
    accessToken: string;
  }

  interface Profile {
    roles: string[];
  }
}

export async function requestAccessToken(refresh_token: string) {
  const tokenUrl = `${env.NEXT_PUBLIC_KEYCLOAK_ROOT}/realms/hpsnz/protocol/openid-connect/token`;
  try {
    const response = await fetch(tokenUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "refresh_token",
        refresh_token,
        client_id: env.KEYCLOAK_ID,
        client_secret: env.KEYCLOAK_SECRET,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to request access token: ${response.statusText}`);
    }

    const responseData = (await response.json()) as RequestTokenResponse;
    return responseData;
  } catch (error) {
    throw error;
  }
}

export const keycloakProvider = KeycloakProvider({
  clientId: env.KEYCLOAK_ID,
  clientSecret: env.KEYCLOAK_SECRET,
  issuer: `${env.NEXT_PUBLIC_KEYCLOAK_ROOT}/realms/hpsnz`,
  authorization: {
    params: {
      scope:
        "offline_access openid email profile video-platform-service pta-platform-service tagger-platform-service",
    },
  },
  // allowDangerousEmailAccountLinking: true,
});

const getAthletes = async ({
  token,
  name,
  sport,
  is_hp,
}: {
  token: string;
  name?: string;
  sport?: string;
  is_hp?: boolean;
}) => {
  const params = new URLSearchParams();

  if (sport) {
    params.set("sport", sport);
  }
  if (name) {
    if (name.length < 3) return;
    params.set("name", name);
  }
  if (is_hp) {
    params.set("is_hp", is_hp.toString());
  }
  params.set("active", "true");
  const res = await fetch(
    `${env.NEXT_PUBLIC_PTA_ROOT_URL}/api/v2/athletes?${params.toString()}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  const json = (await res.json()) as PTAAthlete[];

  return json;
};

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authOptions: NextAuthOptions = {
  session: {
    strategy: "jwt",
  },
  callbacks: {
    session: ({ session, token }) => {
      return {
        ...session,
        user: {
          ...session.user,
          id: token.id,
          roles: token.roles,
          athletes: token.athletes,
        },
        accessToken: token.access_token,
      };
    },
    async jwt({ user, token, profile, account }) {
      if (user) {
        token = { ...token, ...user, roles: profile?.roles };
      }
      if (account) {
        token.id_token = account.id_token;
        token.access_token = account.access_token;
        token.refresh_token = account.refresh_token;
        token.provider = account.provider;
        token.expires_at = account.expires_at;
      }

      if (!token.athletes && token.access_token) {
        try {
          const userAthletes = await getAthletes({
            token: token.access_token as string,
            is_hp: true,
          });

          if (userAthletes) {
            const userAthletesIds = userAthletes.map((a) => a.athlete_id);
            token.athletes = userAthletesIds;
          }
        } catch (error) {
          console.error("Failed to get athletes:", error);
        }
      }

      if (Date.now() < (token.expires_at as number) * 1000) {
        return token;
      }

      const newAccessToken = await requestAccessToken(
        token.refresh_token as string,
      );

      token.access_token = newAccessToken.access_token;
      token.refresh_token = newAccessToken.refresh_token ?? token.refresh_token;
      token.expires_at =
        Math.floor(Date.now() / 1000) + newAccessToken.expires_in;

      return token;
    },
    redirect: ({ baseUrl }) => {
      return baseUrl;
    },
  },
  // adapter: DrizzleAdapter(db, createTable) as Adapter,
  adapter: DrizzleAdapter(db, {
    usersTable: users,
    accountsTable: accounts,
  }) as Adapter,
  providers: [keycloakProvider],
  pages: {
    signIn: "/auth/signin",
  },
  events: {
    signOut: async ({ token }) => {
      if (token.provider === "keycloak") {
        const issuerUrl = (
          authOptions.providers.find(
            (p) => p.id === "keycloak",
          ) as OAuthConfig<KeycloakProfile>
        ).options!.issuer!;
        const logOutUrl = new URL(
          `${issuerUrl}/protocol/openid-connect/logout`,
        );
        logOutUrl.searchParams.set("id_token_hint", token.id_token as string);
        await fetch(logOutUrl);
      }
    },
  },
};

/**
 * Wrapper for `getServerSession` so that you don't need to import the `authOptions` in every file.
 *
 * @see https://next-auth.js.org/configuration/nextjs
 */
export const getServerAuthSession = () => getServerSession(authOptions);
