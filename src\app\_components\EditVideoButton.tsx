import Link from "next/link";
import { env } from "~/env";

interface EditVideoButtonProps {
  videoId: string | undefined;
  title: string;
}

export const EditVideoButton = ({ videoId, title }: EditVideoButtonProps) => {
  if (!videoId) return null;

  const url = `${env.NEXT_PUBLIC_VIDEO_PORTAL_URL}/account/videos/${videoId}`;

  return (
    <Link
      className="rounded-full border border-black px-[5px] py-[2.5px] text-smallLabel text-black hover:bg-black/10"
      target="_blank"
      href={url}
    >
      {title}
    </Link>
  );
};
