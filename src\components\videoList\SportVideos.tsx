"use client";

import {
  <PERSON><PERSON><PERSON>,
  ArrowLeft,
  ArrowRight,
  PanelBottomClose,
} from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { Pill } from "~/components/ui/pill";
import { SortBy, VideoStatus, type Sport } from "~/lib/enums/enums";
import { cn, formatString } from "~/lib/utils";
import { VideoCard } from "../../app/_components/VideoCard";
import { usePathname, useSearchParams } from "next/navigation";
import { api } from "~/trpc/react";
import { Skeleton } from "../ui/skeleton";

const pageSize = 6;

export const SportVideos = ({
  sport,
  iconPath,
}: {
  sport: Sport;
  iconPath: string;
}) => {
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const [isExpanded, setIsExpanded] = useState(false);
  const [filter, setFilter] = useState<{
    type: SortBy.name | SortBy.video_date;
    order: "asc" | "desc";
  }>({ type: SortBy.video_date, order: "asc" });
  const [page, setPage] = useState(0);

  const searchText = searchParams.get("search") ?? undefined;
  const isValidation = pathname.includes("validation");
  const status = isValidation
    ? VideoStatus.Tagged_by_AI
    : ((searchParams.get("status") as VideoStatus) ?? undefined);

  let sortBy: SortBy = filter.type;
  if (filter.order === "desc") {
    sortBy = (sortBy + "_desc") as SortBy;
  }

  const { data: videos, isFetching } = api.videos.getVideos.useQuery({
    searchText,
    sport: [sport],
    status,
    sortBy,
    page,
  });

  const handleFilterChange = (filterType: SortBy.name | SortBy.video_date) => {
    setFilter((prev) => ({
      type: filterType,
      order: prev.type === filterType && prev.order === "asc" ? "desc" : "asc",
    }));
  };

  const onPrevPage = () => {
    setPage((prevPage) => (prevPage > 0 ? prevPage - 1 : prevPage));
  };

  const onNextPage = () => {
    setPage((prevPage) => prevPage + 1);
  };

  return (
    <div className="flex flex-col rounded-xl border bg-seaSalt/40 transition-all duration-200">
      <div
        onClick={() => setIsExpanded(!isExpanded)}
        className={cn(
          "flex w-full cursor-pointer items-center justify-between px-5 py-2.5",
          isExpanded && "border-b",
        )}
      >
        <div className="flex items-center gap-2.5">
          <Image src={iconPath} alt={sport} width={20} height={20} />
          <p className="capitalize tracking-wide text-black">
            {formatString(sport)}
          </p>
          <Pill className="flex items-center bg-black/10 py-[5px] text-center">
            <p className="text-smallLabel">{videos?.count}</p>
          </Pill>
        </div>
        <div className="inline-flex w-[100px] items-center justify-start gap-[5px]">
          <PanelBottomClose
            className={cn(
              "h-[14px] w-[14px] transition-transform duration-200",
              isExpanded && "rotate-180",
            )}
          />
          <p className="text-xs leading-3 tracking-tight">
            {isExpanded ? "Hide panel" : "Expand panel"}
          </p>
        </div>
      </div>

      {isExpanded && (
        <div className="flex flex-col items-start gap-2.5 p-2.5">
          <div className="flex w-full items-start justify-between">
            <div className="flex items-center gap-[5px]">
              <Pill
                className={cn(
                  "flex cursor-pointer justify-center gap-0.5 px-2.5 py-[5px] hover:bg-black/10 hover:text-gray-dark",
                  filter.type === SortBy.video_date &&
                    "bg-gray-dark text-white",
                )}
                onClick={() => handleFilterChange(SortBy.video_date)}
              >
                <p className="text-label">Date Added</p>
                <ArrowDown
                  className={cn(
                    "h-3 w-3",
                    filter.type === SortBy.video_date &&
                      filter.order === "asc" &&
                      "rotate-180",
                  )}
                />
              </Pill>
              <Pill
                className={cn(
                  "flex cursor-pointer justify-center gap-0.5 px-2.5 py-[5px] hover:bg-black/10 hover:text-gray-dark",
                  filter.type === SortBy.name && "bg-gray-dark text-white",
                )}
                onClick={() => handleFilterChange(SortBy.name)}
              >
                <p className="text-label">Video Name</p>
                <ArrowDown
                  className={cn(
                    "h-3 w-3",
                    filter.type === SortBy.name &&
                      filter.order === "asc" &&
                      "rotate-180",
                  )}
                />
              </Pill>
            </div>
            <PaginationControls
              currentPage={page}
              onPrevPage={onPrevPage}
              onNextPage={onNextPage}
              isNextDisabled={!videos || (page + 1) * pageSize >= videos?.count}
            />
          </div>
          <div className="grid w-full grid-cols-12 gap-2.5">
            {isFetching ? (
              <>
                {Array.from({ length: pageSize ?? 0 }).map((_, index) => (
                  <div
                    key={index}
                    className="col-span-2 flex flex-col items-start gap-2.5"
                  >
                    <Skeleton className="aspect-video w-full rounded-[5px]" />
                    <div className="flex w-full flex-col items-start gap-[5px]">
                      <Skeleton className="h-[26px] w-full" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                ))}
              </>
            ) : videos?.list && videos.list.length > 0 ? (
              videos.list.map((video) => (
                <VideoCard key={video.id} video={video} />
              ))
            ) : (
              <div className="col-span-12 flex h-[130px] items-center justify-center py-4">
                <p className="text-gray-500">No videos available</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const PaginationControls = ({
  currentPage,
  onPrevPage,
  onNextPage,
  isNextDisabled,
}: {
  currentPage: number;
  onPrevPage: () => void;
  onNextPage: () => void;
  isNextDisabled: boolean;
}) => (
  <div className="flex items-center gap-[5px]">
    <button
      className="button-pagination"
      onClick={onPrevPage}
      disabled={currentPage === 0}
    >
      <ArrowLeft className="h-4 w-4" strokeWidth={1} />
    </button>
    <button
      className="button-pagination"
      onClick={onNextPage}
      disabled={isNextDisabled}
    >
      <ArrowRight className="h-4 w-4" strokeWidth={1} />
    </button>
  </div>
);
