import * as React from "react";

import { cn } from "~/lib/utils";
import { Label } from "./label";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
  onFinish?: (value: string) => void;
  label?: string;
  containerClassName?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, onFinish, label, containerClassName, ...props }, ref) => {
    return (
      <div
        className={cn(
          "flex items-center justify-between gap-3",
          containerClassName,
        )}
      >
        {label && (
          <Label htmlFor={props.id} className="!text-smallLabel">
            {label}
          </Label>
        )}
        <input
          type={type}
          className={cn(
            "flex w-16 rounded-full border border-seaSalt-k40 bg-transparent px-2.5 py-[2.5px] text-smallLabel shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-seaSalt-k40 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
            className,
          )}
          onKeyDown={(event) => {
            if (event.key === "Enter" && onFinish) {
              const value = (event.target as unknown as { value: string })
                .value;
              onFinish(value);
            }
          }}
          ref={ref}
          {...props}
        />
      </div>
    );
  },
);
Input.displayName = "Input";

export { Input };
