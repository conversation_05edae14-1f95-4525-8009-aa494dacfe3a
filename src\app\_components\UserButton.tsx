import { FileCode, Film, LogOut, User, Users } from "lucide-react";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { env } from "~/env";
import { UserRole } from "~/lib/enums/enums";
import { getServerAuthSession } from "~/server/auth";
import { SignoutButton } from "./SignoutButton";
import { Button } from "~/components/ui/button";

export async function UserButton() {
  const session = await getServerAuthSession();
  const isAdmin = session?.user.roles.includes(UserRole.admin);
  // console.log(session?.accessToken);

  if (!session) {
    return <div />;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="black">Account</Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem>
            <Film className="mr-2 h-4 w-4" />
            <Link href={env.NEXT_PUBLIC_VIDEO_PORTAL_URL} target="_blank">
              Video portal
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <User className="mr-2 h-4 w-4" />
            <Link
              href={`${env.NEXT_PUBLIC_KEYCLOAK_ROOT}/realms/hpsnz/account`}
              target="_blank"
            >
              Manage My Account
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <LogOut className="mr-2 h-4 w-4" />
            <SignoutButton />
          </DropdownMenuItem>
        </DropdownMenuGroup>
        {isAdmin && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem>
                <Users className="mr-2 h-4 w-4" />
                <Link
                  href={`${env.NEXT_PUBLIC_KEYCLOAK_ROOT}/admin/hpsnz/console/#/hpsnz/users`}
                  target="_blank"
                >
                  Manage Users
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <FileCode className="mr-2 h-4 w-4" />
                <Link href="/api-doc">API Docs</Link>
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
