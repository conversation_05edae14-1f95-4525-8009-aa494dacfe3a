"use client";

import type { UseFormReturn } from "react-hook-form";
import { SnowFeatureType } from "~/lib/enums/snow";
import type {
  NewSnowSlopeJumpTag,
  NewSnowSlopeRailTag,
  NewSnowSlopeTransition,
  NewSnowSlopeTransitionJumpTag,
  NewSnowSlopeTransitionTransitionTag,
  SnowFeature,
} from "~/server/db/snowSchema";
import type { SnowFormProps } from "../SnowFormContainer";
import { FormSlopeJump } from "./FormSlopeJump";
import { FormSlopeRail } from "./FormSlopeRail";
import { FormSlopeTransition } from "./FormSlopeTransition";
import { getSnowSectionWord } from "~/lib/utils";

interface Props {
  isSnowboard: boolean;
  feature?: SnowFeature;
  sectionNum: number;
  form: UseFormReturn<SnowFormProps>;
  slopeJumpTag?: NewSnowSlopeJumpTag;
  slopeRailTag?: NewSnowSlopeRailTag;
  slopTransitionTag?: NewSnowSlopeTransition;
  jumpTag: NewSnowSlopeTransitionJumpTag;
  railTag: NewSnowSlopeRailTag;
  transitionTag: NewSnowSlopeTransitionTransitionTag;
  setParentData: React.Dispatch<
    React.SetStateAction<{
      runScore?: string;
      overallScore?: string;
      featureTypeId?: string;
      sectionNum?: string;
      startFrame: string;
      endFrame: string;
    }>
  >;
}

export const FormSlopeParent = ({
  form,
  isSnowboard,
  feature,
  sectionNum,
  slopeJumpTag,
  slopeRailTag,
  jumpTag,
  railTag,
  transitionTag,
}: Props) => {
  const section = getSnowSectionWord(sectionNum, feature);
  return (
    <>
      <div>
        {feature?.type === SnowFeatureType.jump && (
          <FormSlopeJump
            form={form}
            isSnowboard={isSnowboard}
            tag={slopeJumpTag}
          />
        )}
      </div>
      {feature?.type === SnowFeatureType.rail && (
        <FormSlopeRail
          form={form}
          isSnowboard={isSnowboard}
          tag={slopeRailTag}
        />
      )}
      {feature?.type === SnowFeatureType.transition && (
        <FormSlopeTransition
          form={form}
          isSnowboard={isSnowboard}
          section={section}
          jumpTag={jumpTag}
          railTag={railTag}
          transitionTag={transitionTag}
        />
      )}
    </>
  );
};
