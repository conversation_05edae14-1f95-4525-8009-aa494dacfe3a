import { AlertCircle } from "lucide-react";

import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";

export const ErrorBox = ({
  code,
  message,
}: {
  code?: string;
  message?: string;
}) => {
  return (
    <Alert variant="destructive" className="max-w-96 bg-white">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>{code}</AlertTitle>
      <AlertDescription>{message}</AlertDescription>
    </Alert>
  );
};
