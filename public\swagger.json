{"openapi": "3.0.0", "info": {"title": "Swagger API DOC", "version": "1.0"}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "paths": {"/api/v1/highjump/{id}": {"get": {"description": "Get high jump details with strides for a specific video ID", "tags": ["High Jump"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "number"}, "videoId": {"type": "string"}, "athleteId": {"type": "string"}, "number": {"type": "number"}, "startFrame": {"type": "number"}, "endFrame": {"type": "number"}, "approachSide": {"type": "string", "enum": ["LEFT", "RIGHT"]}, "height": {"type": "number", "nullable": true}, "success": {"type": "boolean", "nullable": true}, "type": {"type": "string", "enum": ["SCISSORS", "FOSBURY", "WESTERN_ROLL", "EASTERN_CUT_OFF"], "nullable": true}, "withBox": {"type": "boolean", "nullable": true}, "withBar": {"type": "boolean", "nullable": true}, "comment": {"type": "string", "nullable": true}, "userId": {"type": "string"}, "dateCreated": {"type": "string"}, "strides": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "jumpId": {"type": "number"}, "number": {"type": "number"}, "heelContact": {"type": "number"}, "toeOff": {"type": "number"}, "userId": {"type": "string"}, "dateCreated": {"type": "string"}}}}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "INTERNAL_SERVER_ERROR"}, "message": {"type": "string", "example": "An unexpected error occurred"}}}}}}}}, "post": {"description": "Create high jump entries with strides for a specific video ID", "tags": ["High Jump"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"jumps": {"type": "array", "items": {"type": "object", "properties": {"athleteId": {"type": "string"}, "number": {"type": "number"}, "startFrame": {"type": "number"}, "endFrame": {"type": "number"}, "approachSide": {"type": "string", "enum": ["left side", "right side"]}, "strides": {"type": "array", "items": {"type": "object", "properties": {"number": {"type": "number"}, "heelContact": {"type": "number"}, "toeOff": {"type": "number"}}}}}}}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}}}}}}, "400": {"description": "Bad Request - Invalid body", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "BAD_REQUEST"}, "message": {"type": "string", "example": "Invalid Body"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "INTERNAL_SERVER_ERROR"}, "message": {"type": "string", "example": "An unexpected error occurred"}}}}}}}}}, "/api/v1/shotput/{id}/process": {"post": {"description": "Trigger AI to process shotput video.", "tags": ["Shotput"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/v1/shotput/{id}": {"get": {"description": "Get shotput throws with keypoints and angles for tagged frames.", "tags": ["Shotput"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "videoId": {"type": "string"}, "athleteId": {"type": "string"}, "number": {"type": "number"}, "movement": {"type": "string", "enum": ["ROTATIONAL", "GLIDE"]}, "type": {"type": "string", "enum": ["TRAINING", "COMPETITION"]}, "hand": {"type": "string", "enum": ["RIGHT", "LEFT"]}, "userId": {"type": "string"}, "phases": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "throwId": {"type": "number"}, "tag": {"type": "string", "enum": ["START", "ENTRY", "FLIGHT", "DELIVERY", "RELEASE", "RECOVERY"]}, "frame": {"type": "number"}, "aiFrame": {"type": "number"}, "userId": {"type": "string"}, "keypoints": {"type": "array", "items": {"type": "object", "properties": {"frameNumber": {"type": "number"}, "keypointNum": {"type": "number"}, "x": {"type": "number"}, "y": {"type": "number"}, "z": {"type": "number"}, "aiScore": {"type": "number"}}}}, "angles": {"type": "array", "items": {"type": "object", "properties": {"frameNumber": {"type": "number"}, "name": {"type": "string"}, "angle": {"type": "number"}, "aiScore": {"type": "number"}}}}}}}}}}}}}, "400": {"description": "Bad Request - Invalid video ID", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "BAD_REQUEST"}, "message": {"type": "string", "example": "Invalid video id"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "INTERNAL_SERVER_ERROR"}, "message": {"type": "string", "example": "An unexpected error occurred"}}}}}}}}, "post": {"description": "Add shotput tags to video.", "tags": ["Shotput"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["throws"], "properties": {"throws": {"type": "array", "items": {"type": "object", "required": ["athleteId", "number", "movement", "type", "hand", "tags"], "properties": {"athleteId": {"type": "string"}, "number": {"type": "number"}, "movement": {"type": "string", "enum": ["ROTATIONAL", "GLIDE"]}, "type": {"type": "string", "enum": ["TRAINING", "COMPETITION"]}, "hand": {"type": "string", "enum": ["RIGHT", "LEFT"]}, "tags": {"type": "array", "items": {"type": "object", "required": ["phase", "frame"], "properties": {"phase": {"type": "string", "enum": ["START", "ENTRY", "FLIGHT", "DELIVERY", "RELEASE", "RECOVERY"]}, "frame": {"type": "number"}}}}}}}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}}}}}}, "400": {"description": "Bad Request - Invalid input data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "BAD_REQUEST"}, "message": {"type": "string", "example": "Invalid request body"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "INTERNAL_SERVER_ERROR"}, "message": {"type": "string", "example": "An unexpected error occurred"}}}}}}}}}, "/api/v1/sprint/{id}": {"get": {"description": "Get sprint race details with strides and tags for a specific video ID", "tags": ["Sprint"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "videoId": {"type": "string"}, "name": {"type": "string"}, "eventGroup": {"type": "string"}, "distance": {"type": "string"}, "round": {"type": "string"}, "position": {"type": "number"}, "time": {"type": "string"}, "track": {"type": "string"}, "gender": {"type": "string"}, "date": {"type": "string"}, "wind": {"type": "number"}, "tags": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "sprintingRaceId": {"type": "string"}, "athleteId": {"type": "string"}, "tag": {"type": "string"}, "userId": {"type": "string"}, "frame": {"type": "number"}}}}, "strides": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "sprintId": {"type": "string"}, "number": {"type": "number"}, "heelContact": {"type": "number"}, "toeOff": {"type": "number"}, "userId": {"type": "string"}, "dateCreated": {"type": "string"}}}}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "INTERNAL_SERVER_ERROR"}, "message": {"type": "string", "example": "An unexpected error occurred"}}}}}}}}, "post": {"description": "Create sprint race with strides and tags for a specific video ID", "tags": ["Sprint"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"race": {"type": "object", "properties": {"name": {"type": "string"}, "eventGroup": {"type": "string"}, "distance": {"type": "string"}, "round": {"type": "string"}, "position": {"type": "number"}, "time": {"type": "string"}, "track": {"type": "string"}, "gender": {"type": "string"}, "date": {"type": "string"}, "wind": {"type": "number"}}}, "tags": {"type": "array", "items": {"type": "object", "properties": {"athleteId": {"type": "string"}, "tag": {"type": "string"}, "frame": {"type": "number"}}}}, "strides": {"type": "array", "items": {"type": "object", "properties": {"number": {"type": "number"}, "heelContact": {"type": "number"}, "toeOff": {"type": "number"}}}}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}}}}}}, "400": {"description": "Bad Request - Invalid body", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "BAD_REQUEST"}, "message": {"type": "string", "example": "Invalid Body"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "code": {"type": "string", "example": "INTERNAL_SERVER_ERROR"}, "message": {"type": "string", "example": "An unexpected error occurred"}}}}}}}}}, "/api/v1/swimming/{id}/process": {"post": {"description": "<PERSON><PERSON> AI to process swimming video.", "tags": ["Swimming"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/v1/swimming/{id}": {"get": {"description": "Get swimming tags for a video.", "tags": ["Swimming"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"athleteId": {"type": "string"}, "frame": {"type": "number"}, "tag": {"type": "string"}, "x1": {"type": "number", "nullable": true}, "x2": {"type": "number", "nullable": true}, "y1": {"type": "number", "nullable": true}, "y2": {"type": "number", "nullable": true}}}}}}}, "400": {"description": "Bad Request - Invalid video ID", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "example": "BAD_REQUEST"}, "message": {"type": "string", "example": "Invalid video id"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "example": "INTERNAL_SERVER_ERROR"}, "message": {"type": "string", "example": "An unexpected error occurred"}}}}}}}}, "post": {"description": "Add swimming tags to video.", "tags": ["Swimming"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "required": ["athleteId", "frame", "tag"], "properties": {"athleteId": {"type": "string", "description": "The ID of the athlete"}, "frame": {"type": "number", "description": "The frame number in the video"}, "tag": {"type": "string", "description": "The swimming tag type"}, "x1": {"type": "number", "nullable": true, "description": "X coordinate of the first point"}, "x2": {"type": "number", "nullable": true, "description": "X coordinate of the second point"}, "y1": {"type": "number", "nullable": true, "description": "Y coordinate of the first point"}, "y2": {"type": "number", "nullable": true, "description": "Y coordinate of the second point"}}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}}}}}}, "400": {"description": "Bad Request - Invalid input data", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "example": "BAD_REQUEST"}, "message": {"type": "string", "example": "Invalid request body"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "example": "UNAUTHORIZED"}, "message": {"type": "string", "example": "Invalid token"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "example": "INTERNAL_SERVER_ERROR"}, "message": {"type": "string", "example": "An unexpected error occurred"}}}}}}}}}}, "tags": []}