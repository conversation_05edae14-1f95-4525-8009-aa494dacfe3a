"use client";

import type { ReactNode } from "react";
import type { Control, FieldValues, Path } from "react-hook-form";
import { FormItemType } from "~/lib/enums/enums";
import type { Option } from "~/lib/interface";
import { cn } from "~/lib/utils";
import { EditableSelector, Selector } from "./Selector";
import {
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  FormItem as Item,
} from "./ui/form";
import { Input } from "./ui/input";
import { RadioGroup, RadioGroupItem } from "./ui/radio-group";
import { Checkbox } from "./ui/check-box";
import { Textarea } from "./ui/textarea";
import { HotkeyButton } from "./HotkeyButton";
import { useHotkeys } from "react-hotkeys-hook";
import { DatePicker } from "./ui/date-picker";

export interface FormItemProps {
  title: string;
  type: FormItemType;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  defaultValue?: string;
  options?: Option[];
  isPendingAdd?: boolean;
  hotkey?: {
    value: string;
    className?: string;
    onClick: () => void;
  };
  subHotkey?: {
    value: string;
    className?: string;
    onClick: () => void;
  };
  index?: number;
  className?: string;
  radioGroupClassName?: string;
  labelClassName?: string;
  CustomRender?: (field: {
    value: string;
    onChange: (value: string) => void;
    control?: Control;
  }) => ReactNode;
  onAddOption?: (value: string) => void;
  onRemoveOption?: (value: string) => void;
  onBlur?: (value: string) => void;
}

interface FormField<T extends FieldValues> extends FormItemProps {
  control: Control<T>;
  name: Path<T>;
}

export const FormItem = <T extends FieldValues>({
  name,
  type,
  title,
  placeholder,
  required,
  control,
  options,
  isPendingAdd,
  disabled,
  className,
  radioGroupClassName,
  labelClassName,
  hotkey,
  subHotkey,
  CustomRender,
  onAddOption,
  onRemoveOption,
  onBlur,
}: FormField<T>) => {
  useHotkeys(hotkey?.value ?? "fakekey", () => hotkey?.onClick());
  useHotkeys(subHotkey?.value ?? "fakekey2", () => subHotkey?.onClick());

  return (
    <FormField
      control={control}
      name={name}
      disabled={disabled}
      render={({ field }) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { ref, ...restField } = field;
        return (
          <Item className={cn("gap-[5px]", className)}>
            <FormLabel
              className={cn(
                "font-normal",
                required &&
                  "after:ml-0.5 after:font-black after:text-red-500 after:content-['*']",
                labelClassName,
              )}
            >
              {title}
            </FormLabel>

            <div className="flex w-full items-center">
              {CustomRender && (
                <div className="flex-1">{CustomRender(field)}</div>
              )}
              {!CustomRender && (
                <div className="flex w-full items-center">
                  {[FormItemType.text, FormItemType.number].includes(type) && (
                    <div className="flex-1">
                      <FormControl>
                        <Input
                          {...field}
                          className="w-full text-[10px] font-normal !leading-[10px] tracking-tight"
                          placeholder={placeholder}
                          type={type}
                          value={(field.value as string) ?? ""}
                          defaultValue={undefined}
                          onBlur={() => onBlur?.(field.value as string)}
                        />
                      </FormControl>
                    </div>
                  )}
                  {type === FormItemType.select && (
                    <div className="flex-1">
                      <FormControl>
                        <Selector
                          {...restField}
                          options={options}
                          placeholder={placeholder}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value as string}
                        />
                      </FormControl>
                    </div>
                  )}
                  {type === FormItemType.editSelect && (
                    <FormControl>
                      <EditableSelector
                        options={options ?? []}
                        onAdd={onAddOption}
                        onRemove={onRemoveOption}
                        label="Execution"
                        value={field.value}
                        onSelect={field.onChange}
                        isPendingAdd={isPendingAdd}
                      />
                    </FormControl>
                  )}
                  {type === FormItemType.radio && options && (
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className={cn(
                          "grid flex-1 grid-cols-2",
                          radioGroupClassName,
                        )}
                      >
                        {options?.map(({ label, value, hotkey: optionKey }) => (
                          <Item
                            key={value.toString()}
                            className="flex items-center space-x-3 space-y-0"
                          >
                            <FormControl>
                              <RadioGroupItem
                                value={value.toString()}
                                text={optionKey?.value ?? hotkey?.value}
                                hotkey={optionKey ?? hotkey}
                                className="h-5 w-5"
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {label}
                            </FormLabel>
                          </Item>
                        ))}
                      </RadioGroup>
                    </FormControl>
                  )}
                  {type === FormItemType.boolean && (
                    <Checkbox
                      checked={field.value === "true"}
                      onCheckedChange={(checked) => {
                        field.onChange(checked ? "true" : "false");
                      }}
                    />
                  )}
                  {type === FormItemType.checkbox && options && (
                    <div
                      id={`form-checkbox-group`}
                      className="grid flex-1 grid-cols-2 gap-[5px]"
                    >
                      {options.map((item) => (
                        <FormField
                          key={item.value.toString()}
                          control={control}
                          name={name}
                          render={({ field }) => {
                            return (
                              <Item
                                key={item.value.toString()}
                                className="flex flex-row items-center space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    id={item.value.toString()}
                                    checked={(
                                      field.value as string[]
                                    )?.includes(item.value.toString())}
                                    text={hotkey?.value}
                                    bg={hotkey?.className}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([
                                            ...field.value,
                                            item.value.toString(),
                                          ])
                                        : field.onChange(
                                            (field.value as string[]).filter(
                                              (value) => value !== item.value,
                                            ),
                                          );
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {item.label}
                                </FormLabel>
                              </Item>
                            );
                          }}
                        />
                      ))}
                    </div>
                  )}
                  {type === FormItemType.textarea && (
                    <FormControl>
                      <Textarea
                        {...field}
                        className="min-w-24"
                        placeholder={placeholder}
                        onBlur={() => onBlur?.(field.value as string)}
                      />
                    </FormControl>
                  )}
                  {type === FormItemType.date && (
                    <FormControl>
                      <DatePicker
                        date={field.value}
                        setDate={field.onChange}
                        disabled={disabled}
                      />
                    </FormControl>
                  )}
                </div>
              )}
              {hotkey &&
                type !== FormItemType.radio &&
                type !== FormItemType.checkbox &&
                ((!!options && options.length > 1) ||
                  [
                    FormItemType.number,
                    FormItemType.text,
                    FormItemType.editSelect,
                  ].includes(type)) && (
                  <HotkeyButton
                    onClick={hotkey.onClick}
                    tag={{
                      value: hotkey.value,
                      key: hotkey.value,
                      className: cn(
                        "h-5 w-5 text-label uppercase",
                        hotkey.className,
                      ),
                    }}
                  />
                )}
            </div>

            <FormMessage />
          </Item>
        );
      }}
    />
  );
};
