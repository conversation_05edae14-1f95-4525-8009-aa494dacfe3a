import { cva, type VariantProps } from "class-variance-authority";
import { XIcon } from "lucide-react";
import { cn } from "~/lib/utils";

const buttonVariants = cva("rounded-md relative font-medium w-fit", {
  variants: {
    variant: {
      default:
        "bg-none text-gray-dark border border-gray-dark rounded-full ",
      red: "bg-red-200 text-red-800",
      green: "bg-green-200 text-green",
      yellow: "bg-yellow-200 text-yellow-800",
    },
    size: {
      default: "px-3",
      sm: "px-2 text-sm",
      lg: "px-4 text-lg",
    },
  },
  defaultVariants: {
    variant: "default",
    size: "default",
  },
});

export interface PillProps
  extends React.ButtonHTMLAttributes<HTMLDivElement>,
    VariantProps<typeof buttonVariants> {
  onClose?: () => void;
}

export const Pill = ({
  className,
  variant,
  size,
  onClose,
  ...props
}: PillProps) => (
  <div className={cn(buttonVariants({ variant, size, className }))} {...props}>
    {onClose && (
      <XIcon
        className="absolute -right-1 -top-1 h-4 w-4 cursor-pointer"
        onClick={onClose}
      />
    )}
    {props.children}
  </div>
);
