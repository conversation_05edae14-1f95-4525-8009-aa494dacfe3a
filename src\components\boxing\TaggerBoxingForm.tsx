"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useParams } from "next/navigation";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { Card } from "~/components/Card";
import { type FormItemProps, FormItem } from "~/components/FormItem";
import { Button } from "~/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormItem as ShadFormItem,
} from "~/components/ui/form";
import { useStore } from "~/hooks/store";
import { FormItemType } from "~/lib/enums/enums";
import { toast } from "react-hot-toast";
import { boxingPunchSchema } from "~/lib/schemas";
import { api } from "~/trpc/react";
import { Checkbox } from "../ui/check-box";
import {
  BoxingCorner,
  BoxingPunchType,
  boxingPunchTypes,
} from "~/lib/enums/boxing";

interface BoxingPunchTypeFormProps extends FormItemProps {
  name: keyof BoxingFormProps;
  className?: string;
}

export type BoxingFormProps = z.infer<typeof boxingPunchSchema>;

const boxingInitialValues: BoxingFormProps = {
  id: "",
  number: "1",
  round: "1",
  punch: BoxingPunchType.Jab,
  success: "false",
  head: "false",
  unsure: "false",
  feint: "false",
  clinch: "false",
  switch: "false",
  color: BoxingCorner.Blue,
};

// Helper function to convert string boolean values to actual booleans
const convertToBoolean = (value: "true" | "false"): boolean => {
  return value === "true";
};

export const TaggerBoxingForm = () => {
  const params = useParams<{ id: string }>();
  const videoId = params.id;
  const utils = api.useUtils();

  const currentFrame = useStore((state) => state.currentFrame);
  // const selectedAthlete = useStore((state) => state.selectedAthlete);

  const form = useForm<BoxingFormProps>({
    resolver: zodResolver(boxingPunchSchema),
    defaultValues: { ...boxingInitialValues },
  });
  //will ahve to pass athleteid in here once that flow is set up from PTA
  const tempId = "5c2c7177-78bf-4ff5-a5b7-a02b6660b01b";

  const { mutate: upsertBoxingPunch, isPending } =
    api.boxing.upsertBoxingPunch.useMutation({
      onSuccess: () => {
        toast.success("Punch added successfully");
        void utils.boxing.getTags.invalidate({ videoId });
        // Reset form to initial values but increment the punch number
        const currentNumber = parseInt(form.getValues().number);
        form.reset({
          ...boxingInitialValues,
          number: (currentNumber + 1).toString(),
        });
      },
      onError: (error) => {
        toast.error(`Failed to add punch: ${error.message}`);
      },
    });

  const onAddPunch = () => {
    if (!videoId) {
      toast.error("Video ID not selected");
      return;
    }

    if (!tempId) {
      toast.error("Athlete not selected");
      return;
    }

    // Get current form values
    const formValues = form.getValues();

    // No need to explicitly set success to false here, as we'll convert it properly below

    // Convert string values to numbers for the database
    const numberValue = parseInt(formValues.number);
    const roundValue = parseInt(formValues.round);

    // Call the upsert mutation
    upsertBoxingPunch({
      videoId,
      athleteId: tempId,
      startFrame: currentFrame,
      number: numberValue,
      round: roundValue,
      punch: formValues.punch,
      isSuccess: convertToBoolean(formValues.success),
      head: convertToBoolean(formValues.head),
      unsure: convertToBoolean(formValues.unsure),
      feint: convertToBoolean(formValues.feint),
      clinch: convertToBoolean(formValues.clinch),
      switch: convertToBoolean(formValues.switch),
      color: formValues.color,
    });
  };

  const boxingFormItems: BoxingPunchTypeFormProps[] = [
    {
      name: "number",
      title: "Punch Number",
      type: FormItemType.number,
    },
    {
      name: "round",
      title: "Round Number",
      type: FormItemType.number,
    },
    {
      name: "punch",
      title: "Punch Type",
      type: FormItemType.checkbox,
      CustomRender: () => (
        <div className="grid flex-1 grid-cols-2 gap-[5px]">
          {boxingPunchTypes.map((punchType) => (
            <FormField
              key={punchType}
              control={form.control}
              name="punch"
              render={({ field }) => (
                <ShadFormItem className="flex flex-row items-center space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value === punchType}
                      onCheckedChange={() => {
                        form.setValue("punch", punchType);
                      }}
                    />
                  </FormControl>
                  <FormLabel className="font-normal">{punchType}</FormLabel>
                </ShadFormItem>
              )}
            />
          ))}
        </div>
      ),
    },
    {
      name: "success",
      title: "Success",
      type: FormItemType.boolean,
      className: "grid grid-cols-2",
      hotkey: {
        value: "s",
        className: "bg-green-500 text-white",
        onClick: () => {
          // Get the current value and ensure it's treated as a string
          const currentValue = String(form.getValues().success);

          // The FormItem component expects string 'true'/'false' for boolean fields
          const newValue = currentValue === "true" ? "false" : "true";

          form.setValue("success", newValue);
        },
      },
    },
    {
      name: "head",
      title: "Head",
      type: FormItemType.boolean,
      className: "grid grid-cols-2",
    },
    {
      name: "unsure",
      title: "Unsure",
      type: FormItemType.boolean,
      className: "grid grid-cols-2",
    },
    {
      name: "feint",
      title: "Feint",
      type: FormItemType.boolean,
      className: "grid grid-cols-2",
    },
    {
      name: "clinch",
      title: "Clinch",
      type: FormItemType.boolean,
      className: "grid grid-cols-2",
    },
    {
      name: "switch",
      title: "Switch",
      type: FormItemType.boolean,
      className: "grid grid-cols-2",
    },
    {
      name: "color",
      title: "Colour",
      type: FormItemType.radio,
      className: "grid grid-cols-2",
      options: [
        { label: "Red", value: BoxingCorner.Red },
        { label: "Blue", value: BoxingCorner.Blue },
      ],
    },
  ];

  return (
    <div className="flex h-full flex-col gap-2.5">
      <Form {...form}>
        <form className="flex flex-col gap-2.5 text-smallLabel">
          <Card className="grid w-full gap-2.5">
            {boxingFormItems.map((item) => (
              <FormItem key={item.name} control={form.control} {...item} />
            ))}
          </Card>
        </form>
      </Form>
      <Button
        className="col-span-2 mt-auto w-full"
        type="button"
        onClick={onAddPunch}
        disabled={isPending}
      >
        {isPending ? "Adding..." : "Add Punch"}
      </Button>
    </div>
  );
};
