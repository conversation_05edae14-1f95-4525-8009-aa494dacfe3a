import { useHotkeys } from "react-hotkeys-hook";
import { useStore } from "~/hooks/store";
import { useShotputTags } from "~/hooks/useShotputTags";
import { ShotputTag, shotputTags } from "~/lib/enums/shotput";
import { getAthleteOptions } from "~/lib/utils";
import { AddTagPannel } from "./AddTagPannel";
import { AthleteSelect } from "./AthleteSelect";
import { ShotputThrowForm } from "./ShotputThrowForm";
import { TagListView } from "./TagListView";

export const ShotputForm = () => {
  const { tags, onAddTag, onDeleteTag } = useShotputTags();

  const videoSummary = useStore((state) => state.videoSummary);

  const athleteOptions = getAthleteOptions(videoSummary, tags?.throws);

  const throws = tags?.throws;

  useHotkeys("1", () => onAddTag(ShotputTag.RotationStart));
  useHotkeys("2", () => onAddTag(ShotputTag.RightToeOff));
  useHotkeys("3", () => onAddTag(ShotputTag.LeftToeOffBack));
  useHotkeys("4", () => onAddTag(ShotputTag.RightFootDown));
  useHotkeys("5", () => onAddTag(ShotputTag.DoubleStance));
  useHotkeys("6", () => onAddTag(ShotputTag.RearFootTakeOff));
  useHotkeys("7", () => onAddTag(ShotputTag.FrontFootTakeOff));
  useHotkeys("8", () => onAddTag(ShotputTag.Release));
  useHotkeys("9", () => onAddTag(ShotputTag.PostRelease));

  return (
    <>
      <AthleteSelect athleteOptions={athleteOptions} />
      {throws && throws.length < 1 && <div>no throws found</div>}
      {throws && throws.length > 0 && (
        <>
          <ShotputThrowForm disabled />
          <TagListView
            isVirtual={false}
            height={216}
            tagOptions={shotputTags["key phase"]}
            onRemoveTag={onDeleteTag}
          />
          <AddTagPannel
            tags={shotputTags}
            textClassName="line-clamp-2"
            onAddTag={onAddTag}
          />
        </>
      )}
    </>
  );
};
