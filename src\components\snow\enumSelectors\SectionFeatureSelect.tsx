"use client";

import { Selector } from "~/components/Selector";
import { transitionSectionMap } from "~/lib/constants";
import { SnowFeatureType } from "~/lib/enums/snow";
import type { Option } from "~/lib/interface";
import { getStringNumber } from "~/lib/utils";
import type { SnowFeature } from "~/server/db/snowSchema";

export const SectionFeatureSelect = ({
  value,
  feature,
  onChange,
}: {
  value?: string;
  feature: SnowFeature;
  onChange: (value: string) => void;
}) => {
  let options: Option[] = [];
  if (feature.type === SnowFeatureType.transition) {
    if (!feature.sections) {
      options = [];
    } else {
      for (let i = 1; i <= feature.sections.length; i++) {
        const letter = feature.sections[i - 1]!.toLowerCase();
        const word =
          transitionSectionMap[letter as keyof typeof transitionSectionMap];
        options.push({
          label: `${feature.featureNumber}.${i} ${word}`,
          value: i.toString(),
        });
      }
    }
  } else {
    options = Array.from(
      {
        length: getStringNumber(feature.sections) ?? 0,
      },
      (_, i) => i + 1,
    ).map((x) => ({
      label: `${feature.featureNumber}.${x} ${feature.type}`,
      value: x.toString(),
    }));
  }

  return (
    <Selector
      options={options}
      placeholder="Select section"
      label="Section Feature"
      value={value}
      className="w-full"
      containerClassName=" text-smallLabel"
      onValueChange={onChange}
    />
  );
};
