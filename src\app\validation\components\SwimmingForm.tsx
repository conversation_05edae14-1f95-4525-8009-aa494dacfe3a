import { useHotkeys } from "react-hotkeys-hook";
import { RepSelector } from "~/components/swimming/RepSelector";
import { useSwimmingTags } from "~/hooks/useSwimmingTags";
import { SwimmingTag, swimmingTags } from "~/lib/enums/swimming";
import { AddTagPannel } from "./AddTagPannel";
import { TagListView } from "./TagListView";
import { useStore } from "~/hooks/store";

export const SwimmingForm = () => {
  const { tags, onAddTag, onDeleteTag } = useSwimmingTags();
  const editingTagId = useStore((state) => state.editingTagId);
  const setEditingTagId = useStore((state) => state.setEditingTagId);

  const isVirtual = !!tags && tags.tags.length >= 1000;

  useHotkeys("1", () => onAddTag(SwimmingTag["5m"]));
  useHotkeys("2", () => onAddTag(SwimmingTag["15m"]));
  useHotkeys("3", () => onAddTag(SwimmingTag["25m"]));
  useHotkeys("4", () => onAddTag(SwimmingTag["35m"]));
  useHotkeys("5", () => onAddTag(SwimmingTag["45m"]));

  useHotkeys("q", () => onAddTag(SwimmingTag.Gun));
  useHotkeys("w", () => onAddTag(SwimmingTag.Touch));
  useHotkeys("e", () => onAddTag(SwimmingTag.StrokeCount));
  useHotkeys("r", () => onAddTag(SwimmingTag.StrokeCycle));
  useHotkeys("a", () => onAddTag(SwimmingTag.ToeOff));
  useHotkeys("s", () => onAddTag(SwimmingTag.UW));
  useHotkeys("d", () => onAddTag(SwimmingTag.Swim));
  useHotkeys("f", () => onAddTag(SwimmingTag.Breath));
  useHotkeys("enter", () => {
    if (editingTagId) {
      setEditingTagId(null);
    }
  });

  return (
    <>
      <RepSelector />
      <TagListView
        isVirtual={isVirtual}
        height={345}
        tagOptions={[...swimmingTags.distance, ...swimmingTags.movement]}
        onRemoveTag={onDeleteTag}
      />
      <AddTagPannel tags={swimmingTags} onAddTag={onAddTag} />
    </>
  );
};
