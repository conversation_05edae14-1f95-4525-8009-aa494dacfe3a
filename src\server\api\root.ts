import { videoRouter } from "~/server/api/routers/video";
import { createCallerFactory, createTRPCRouter } from "~/server/api/trpc";
import { swimmingRouter } from "./routers/swimming";
import { seedRouter } from "./routers/seed";
import { shotputRouter } from "./routers/shotput";
import { snowRouter } from "./routers/snow";
import { snowOptionsRouter } from "./routers/snowOptions";
import { highJumpRouter } from "./routers/highJump";
import { swimmingPtaRouter } from "./routers/swimmingPta";
import { boxingRouter } from "./routers/boxing";
import { discusRouter } from "./routers/discus";
import { poseRouter } from "./routers/pose";
import { sprintRouter } from "./routers/sprinting";
/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  videos: videoRouter,
  swimming: swimmingRouter,
  swimmingPta: swimmingPtaRouter,
  seed: seedRouter,
  shotput: shotputRouter,
  snow: snowRouter,
  snowOptions: snowOptionsRouter,
  highJump: highJumpRouter,
  boxing: boxingRouter,
  discus: discusRouter,
  pose: poseRouter,
  sprint: sprintRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
